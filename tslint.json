{"rules": {"class-name": true, "comment-format": [true, "check-space"], "indent": [true, "spaces"], "one-line": [true, "check-open-brace", "check-whitespace"], "no-var-keyword": true, "quotemark": [false, "double", "avoid-escape"], "semicolon": [false, "always", "ignore-bound-class-methods"], "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-module", "check-separator", "check-type", "check-preblock"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}, {"call-signature": "onespace", "index-signature": "onespace", "parameter": "onespace", "property-declaration": "onespace", "variable-declaration": "onespace"}], "no-internal-module": true, "no-trailing-whitespace": false, "no-null-keyword": true, "prefer-const": true, "jsdoc-format": true}}