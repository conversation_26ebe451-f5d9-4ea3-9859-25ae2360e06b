MONGODB_URI=*******************************************
MONGOLAB_URI=MONGOLAB_URI

#GCLOUD
NPM_CONFIG_PRODUCTION=false
#firebase

##POPBILL
POPBILL_LINK_ID=G<PERSON><PERSON><PERSON><PERSON>
POPBILL_SECRET_KEY=JxCCBV058q6tHibZ1cedbUb4pO8kzDNdpTHAF+B836Y=
POPBILL_CORP_NUMBER=**********
POPBILL_SENDER=***********

#POSGRESQL CONFIG
# Development config
DB_NAME=myhome
DB_USER=postgres
DB_PASS=korhitek123
HOST=kormsg.c8bjvmvp5gku.ap-northeast-2.rds.amazonaws.com

## Connection for GLOUD
INSTANCE_CONNECTION_NAME=INSTANCE_CONNECTION_NAME
#
## Connection Mail
#
#
#

#new
NODE_ENV=production

SESSION_SECRET=SESSION_SECRET
SERVER_SECRET=greenapp-myhome

#firebase
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=myhome-e4c53
FIREBASE_PRIVATE_KEY_ID=c66172e495f04002df80630805e416965a679dc3
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=*******
FIREBASE_CLIENT_ID=116885352205140112547
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-dra9l%40myhome-e4c53.iam.gserviceaccount.com

#DB prod
PROD_DB_NAME=myhome
PROD_DB_USER=postgres
PROD_DB_PASS=korhitek123
PROD_HOST=kormsg.c8bjvmvp5gku.ap-northeast-2.rds.amazonaws.com

# Connection Mail
SMTP_MAIL=smtps://mitekit2018%40gmail.com:*******
EMAIL_USERNAME=*******

# AWS S3
AWS_ID=********************
AWS_SECRET_KEY=bishi2ePIa7LLNIODn3I6gIRlSD6VC0II6QZz0WK
AWS_BUCKET_NAME=myhome-s3
IMAGE_URL=myhome-s3.s3.ap-northeast-2.amazonaws.com


HOST_NAME=localhost
PORT=9879
PORT_SSL=9879
PORT_SOCKET=9879


CONTENT61=v=spf1 ip4:************ ~all

GOOGLE_PROJECT_ID=************
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


TEXT_SMS=굿베트남
DOMAIN=goodvina.com