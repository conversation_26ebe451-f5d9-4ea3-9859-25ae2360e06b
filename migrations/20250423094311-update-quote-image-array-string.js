module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Xóa field cũ
    await queryInterface.removeColumn('tbl_quote', 'image_url');

    // Tạo field mới
    await queryInterface.addColumn('tbl_quote', 'image_url', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('tbl_quote', 'image_url');

    await queryInterface.addColumn('tbl_quote', 'image_url', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  }
};
