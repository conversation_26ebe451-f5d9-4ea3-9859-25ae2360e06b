'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create tbl_used_car_post_tag (join table for many-to-many tags)
    await queryInterface.createTable('tbl_used_car_post_tag', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        primaryKey: true,
        comment: 'Primary key - UUID for the used car post tag relationship',
      },
      status: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
        comment: 'Whether this tag relationship is active',
      },
      created_at_unix_timestamp: {
        type: Sequelize.BIGINT,
        comment: 'Unix timestamp when the relationship was created',
      },
      created_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the relationship was created',
      },
      updated_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the relationship was last updated',
      },
      deleted_at: {
        type: 'TIMESTAMP',
        comment: 'Timestamp when the relationship was soft deleted',
      },
      used_car_post_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tbl_used_car_post',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'Reference to the used car post',
      },
      tag_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tbl_tag',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'Reference to the tag',
      },
    }, {
      comment: 'Join table connecting used car posts with tags for categorization and filtering',
    });

    // Create tbl_favourite_used_car_post
    await queryInterface.createTable('tbl_favourite_used_car_post', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        primaryKey: true,
        comment: 'Primary key - UUID for the favorite relationship',
      },
      status: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
        comment: 'Whether this favorite is active',
      },
      created_at_unix_timestamp: {
        type: Sequelize.BIGINT,
        comment: 'Unix timestamp when the favorite was created',
      },
      created_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the favorite was created',
      },
      updated_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the favorite was last updated',
      },
      deleted_at: {
        type: 'TIMESTAMP',
        comment: 'Timestamp when the favorite was soft deleted',
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tbl_user',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'Reference to the user who favorited the post',
      },
      used_car_post_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tbl_used_car_post',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'Reference to the favorited used car post',
      },
    }, {
      comment: 'User favorites for used car posts',
    });

    // Add indexes for tbl_used_car_post_tag
    await queryInterface.addIndex('tbl_used_car_post_tag', 
      ['used_car_post_id', 'tag_id'], 
      {
        unique: true,
        name: 'unique_used_car_post_tag',
        where: {
          deleted_at: null,
        },
      }
    );

    await queryInterface.addIndex('tbl_used_car_post_tag', ['used_car_post_id'], {
      name: 'idx_used_car_post_tag_post_id',
    });

    await queryInterface.addIndex('tbl_used_car_post_tag', ['tag_id'], {
      name: 'idx_used_car_post_tag_tag_id',
    });

    // Add indexes for tbl_favourite_used_car_post
    await queryInterface.addIndex('tbl_favourite_used_car_post', 
      ['user_id', 'used_car_post_id'], 
      {
        unique: true,
        name: 'unique_user_used_car_post_favourite',
        where: {
          deleted_at: null,
        },
      }
    );

    await queryInterface.addIndex('tbl_favourite_used_car_post', ['user_id'], {
      name: 'idx_favourite_used_car_post_user_id',
    });

    await queryInterface.addIndex('tbl_favourite_used_car_post', ['used_car_post_id'], {
      name: 'idx_favourite_used_car_post_post_id',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Drop indexes first
    await queryInterface.removeIndex('tbl_favourite_used_car_post', 'idx_favourite_used_car_post_post_id');
    await queryInterface.removeIndex('tbl_favourite_used_car_post', 'idx_favourite_used_car_post_user_id');
    await queryInterface.removeIndex('tbl_favourite_used_car_post', 'unique_user_used_car_post_favourite');
    
    await queryInterface.removeIndex('tbl_used_car_post_tag', 'idx_used_car_post_tag_tag_id');
    await queryInterface.removeIndex('tbl_used_car_post_tag', 'idx_used_car_post_tag_post_id');
    await queryInterface.removeIndex('tbl_used_car_post_tag', 'unique_used_car_post_tag');

    // Drop tables
    await queryInterface.dropTable('tbl_favourite_used_car_post');
    await queryInterface.dropTable('tbl_used_car_post_tag');
  }
};
