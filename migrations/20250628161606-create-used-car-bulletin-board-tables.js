'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Table creation order:
    // 1. tbl_car_manufacturer
    // 2. tbl_car_option_group
    // 3. tbl_car_model (depends on tbl_car_manufacturer)
    // 4. tbl_car_option (depends on tbl_car_option_group)
    // 5. tbl_used_car_post (depends on tbl_car_manufacturer, tbl_car_model, tbl_user, tbl_category)
    // 6. tbl_used_car_post_option (depends on tbl_used_car_post, tbl_car_option)

    // 1. Create tbl_car_manufacturer
    await queryInterface.createTable('tbl_car_manufacturer', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        primaryKey: true,
      },
      status: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
      },
      created_at_unix_timestamp: {
        type: Sequelize.BIGINT,
      },
      created_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updated_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      deleted_at: { type: 'TIMESTAMP' },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      logo_url: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      website: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      alias: {
        type: Sequelize.STRING,
      },
      index: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
    });

    // 2. Create tbl_car_option_group
    await queryInterface.createTable('tbl_car_option_group', {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: { type: 'TIMESTAMP' },
        name: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: true,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        icon: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
    });

    // 3. Create tbl_car_model
    await queryInterface.createTable('tbl_car_model', {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: { type: 'TIMESTAMP' },
        name: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        body_type: {
            type: Sequelize.ENUM('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'wagon', 'pickup', 'van'),
            allowNull: true,
        },
        fuel_type: {
            type: Sequelize.ENUM('gasoline', 'diesel', 'hybrid', 'electric', 'lpg'),
            allowNull: true,
        },
        transmission: {
            type: Sequelize.ENUM('manual', 'automatic', 'cvt'),
            allowNull: true,
        },
        engine_size: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        start_year: {
            type: Sequelize.INTEGER,
            allowNull: true,
        },
        end_year: {
            type: Sequelize.INTEGER,
            allowNull: true,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        image_url: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        manufacturer_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_manufacturer',
                key: 'id',
            },
        },
    });
    await queryInterface.addIndex('tbl_car_model', ['manufacturer_id', 'name'], {
        name: 'unique_manufacturer_model',
        unique: true,
    });
    
    // 4. Create tbl_car_option
    await queryInterface.createTable('tbl_car_option', {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: { type: 'TIMESTAMP' },
        name: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        icon: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        is_premium: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        option_group_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_option_group',
                key: 'id',
            },
        },
    });
    await queryInterface.addIndex('tbl_car_option', ['option_group_id', 'name'], {
        name: 'unique_group_option',
        unique: true,
    });

    // 5. Create tbl_used_car_post
    await queryInterface.createTable('tbl_used_car_post', {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: { type: 'TIMESTAMP' },
        title: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        price: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        negotiable: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        year: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        mileage: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        fuel_type: {
            type: Sequelize.ENUM('gasoline', 'diesel', 'hybrid', 'electric', 'lpg'),
            allowNull: false,
            defaultValue: 'gasoline',
        },
        transmission: {
            type: Sequelize.ENUM('manual', 'automatic', 'cvt'),
            allowNull: false,
            defaultValue: 'manual',
        },
        body_type: {
            type: Sequelize.ENUM('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'wagon', 'pickup', 'van'),
            allowNull: true,
        },
        exterior_color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        interior_color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        condition: {
            type: Sequelize.ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
            allowNull: false,
            defaultValue: 'good',
        },
        accident_history: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        number_of_owners: {
            type: Sequelize.INTEGER,
            defaultValue: 1,
        },
        images: {
            type: Sequelize.ARRAY(Sequelize.STRING),
            allowNull: true,
        },
        thumbnails: {
            type: Sequelize.ARRAY(Sequelize.STRING),
            allowNull: true,
        },
        contact_phone: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        contact_email: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        latitude: {
            type: Sequelize.DOUBLE,
            allowNull: false,
        },
        longitude: {
            type: Sequelize.DOUBLE,
            allowNull: false,
        },
        position: {
            type: Sequelize.GEOMETRY('POINT', 4326),
            allowNull: true,
        },
        address: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        short_address: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        state: {
            type: Sequelize.ENUM('available', 'pending', 'sold', 'withdrawn'),
            defaultValue: 'available',
        },
        view_count: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        like_count: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        comment_count: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        alias: {
            type: Sequelize.STRING,
        },
        user_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        manufacturer_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_manufacturer',
                key: 'id',
            },
        },
        model_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_model',
                key: 'id',
            },
        },
        category_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_category',
                key: 'id',
            },
        },
    });

    // 6. Create tbl_used_car_post_option
    await queryInterface.createTable('tbl_used_car_post_option', {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: { type: 'TIMESTAMP' },
        is_verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        condition: {
            type: Sequelize.ENUM('excellent', 'good', 'fair', 'poor', 'not_working'),
            allowNull: true,
        },
        notes: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        used_car_post_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_used_car_post',
                key: 'id',
            },
            onDelete: 'CASCADE',
        },
        car_option_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_option',
                key: 'id',
            },
        },
    });
    await queryInterface.addIndex('tbl_used_car_post_option', ['used_car_post_id', 'car_option_id'], {
        name: 'unique_post_option',
        unique: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Drop tables in reverse order of creation
    await queryInterface.dropTable('tbl_used_car_post_option');
    await queryInterface.dropTable('tbl_used_car_post');
    await queryInterface.dropTable('tbl_car_option');
    await queryInterface.dropTable('tbl_car_model');
    await queryInterface.dropTable('tbl_car_option_group');
    await queryInterface.dropTable('tbl_car_manufacturer');
    
    // It's good practice to also drop any custom ENUM types created,
    // though Sequelize can sometimes handle this. If issues arise,
    // explicit `DROP TYPE` commands might be needed here.
    // e.g., await queryInterface.sequelize.query('DROP TYPE "enum_tbl_used_car_post_fuel_type";');
  }
};
