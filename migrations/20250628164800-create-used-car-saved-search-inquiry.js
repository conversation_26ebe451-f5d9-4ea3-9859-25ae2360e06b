'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create tbl_used_car_saved_search table
    await queryInterface.createTable('tbl_used_car_saved_search', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        primaryKey: true,
        comment: 'Primary key - UUID for the saved search',
      },
      status: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
        comment: 'Whether this saved search is active',
      },
      created_at_unix_timestamp: {
        type: Sequelize.BIGINT,
        comment: 'Unix timestamp when the search was saved',
      },
      created_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the search was saved',
      },
      updated_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the search was last updated',
      },
      deleted_at: { 
        type: 'TIMESTAMP',
        comment: 'Timestamp when the search was soft deleted',
      },
      
      // search metadata
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'User-defined name for this saved search',
      },
      notification_enabled: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        comment: 'Whether to notify user of new matches',
      },
      last_executed_at: {
        type: 'TIMESTAMP',
        comment: 'When this search was last executed',
      },
      
      // search criteria
      price_min: {
        type: Sequelize.DECIMAL(15, 2),
        comment: 'Minimum price filter',
      },
      price_max: {
        type: Sequelize.DECIMAL(15, 2),
        comment: 'Maximum price filter',
      },
      year_min: {
        type: Sequelize.INTEGER,
        comment: 'Minimum year filter',
      },
      year_max: {
        type: Sequelize.INTEGER,
        comment: 'Maximum year filter',
      },
      mileage_min: {
        type: Sequelize.INTEGER,
        comment: 'Minimum mileage filter',
      },
      mileage_max: {
        type: Sequelize.INTEGER,
        comment: 'Maximum mileage filter',
      },
      fuel_type: {
        type: Sequelize.ENUM('gasoline', 'diesel', 'hybrid', 'electric', 'lpg'),
        comment: 'Filter by fuel type',
      },
      transmission: {
        type: Sequelize.ENUM('manual', 'automatic', 'cvt'),
        comment: 'Filter by transmission type',
      },
      body_type: {
        type: Sequelize.ENUM('sedan', 'hatchback', 'suv', 'coupe', 'convertible', 'wagon', 'pickup', 'van', 'minivan'),
        comment: 'Filter by body type',
      },
      condition: {
        type: Sequelize.ENUM('excellent', 'good', 'fair', 'poor'),
        comment: 'Filter by car condition',
      },
      is_negotiable: {
        type: Sequelize.BOOLEAN,
        comment: 'Filter for negotiable prices only',
      },
      keywords: {
        type: Sequelize.TEXT,
        comment: 'Text search keywords',
      },
      tags: {
        type: Sequelize.ARRAY({ type: Sequelize.STRING }),
        comment: 'Array of tag names to filter by',
      },
      
      // location criteria
      location_radius: {
        type: Sequelize.DECIMAL(8, 2),
        comment: 'Search radius in kilometers',
      },
      location_latitude: {
        type: Sequelize.DECIMAL(10, 8),
        comment: 'Center latitude for location search',
      },
      location_longitude: {
        type: Sequelize.DECIMAL(11, 8),
        comment: 'Center longitude for location search',
      },
      
      // foreign keys
      user_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_user',
          key: 'id',
        },
        allowNull: false,
        comment: 'Reference to the user who saved this search',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      manufacturer_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_car_manufacturer',
          key: 'id',
        },
        allowNull: true,
        comment: 'Filter by specific car manufacturer',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      },
      model_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_car_model',
          key: 'id',
        },
        allowNull: true,
        comment: 'Filter by specific car model',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      },
      category_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_category',
          key: 'id',
        },
        allowNull: true,
        comment: 'Filter by car category',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      },
    }, {
      comment: 'User saved search criteria for used car posts',
    });

    // Create tbl_used_car_inquiry table  
    await queryInterface.createTable('tbl_used_car_inquiry', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        primaryKey: true,
        comment: 'Primary key - UUID for the inquiry',
      },
      status: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
        comment: 'Whether this inquiry is active',
      },
      created_at_unix_timestamp: {
        type: Sequelize.BIGINT,
        comment: 'Unix timestamp when the inquiry was created',
      },
      created_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the inquiry was created',
      },
      updated_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        comment: 'Timestamp when the inquiry was last updated',
      },
      deleted_at: { 
        type: 'TIMESTAMP',
        comment: 'Timestamp when the inquiry was soft deleted',
      },
      
      // inquiry details
      inquiry_type: {
        type: Sequelize.ENUM('general', 'price_negotiation', 'test_drive', 'inspection', 'financing'),
        allowNull: false,
        comment: 'Type of inquiry',
      },
      subject: {
        type: Sequelize.STRING,
        comment: 'Subject/title of the inquiry',
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'The inquiry message content',
      },
      
      // buyer contact information
      buyer_name: {
        type: Sequelize.STRING,
        comment: 'Name of the buyer (can be different from username)',
      },
      buyer_phone: {
        type: Sequelize.STRING,
        comment: 'Contact phone number of the buyer',
      },
      buyer_email: {
        type: Sequelize.STRING,
        comment: 'Contact email of the buyer',
      },
      preferred_contact_method: {
        type: Sequelize.ENUM('phone', 'email', 'in_app'),
        defaultValue: 'in_app',
        comment: "Buyer\'s preferred contact method",
      },
      
      // inquiry specifics
      offered_price: {
        type: Sequelize.DECIMAL(15, 2),
        comment: 'Price offered by the buyer (if any)',
      },
      is_serious_buyer: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Whether the buyer indicates they are serious/ready to purchase',
      },
      preferred_meeting_location: {
        type: Sequelize.STRING,
        comment: "Buyer\'s preferred location for meeting/test drive",
      },
      available_dates: {
        type: Sequelize.TEXT,
        comment: "Text description of buyer\'s availability",
      },
      
      // inquiry status and responses
      inquiry_status: {
        type: Sequelize.ENUM('pending', 'replied', 'in_negotiation', 'scheduled', 'closed', 'cancelled'),
        defaultValue: 'pending',
        comment: 'Status of the inquiry',
      },
      seller_response: {
        type: Sequelize.TEXT,
        comment: "Seller\'s response message (if any)",
      },
      seller_responded_at: {
        type: 'TIMESTAMP',
        comment: 'When the seller responded',
      },
      
      // read status
      is_read_by_seller: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Whether the seller has read this inquiry',
      },
      is_read_by_buyer: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: "Whether the buyer has read the seller\'s response",
      },
      
      // meeting scheduling
      meeting_scheduled_at: {
        type: 'TIMESTAMP',
        comment: 'Scheduled meeting time (if applicable)',
      },
      
      // metadata
      metadata: {
        type: Sequelize.JSONB,
        comment: 'Additional metadata (JSON)',
      },
      
      // foreign keys
      used_car_post_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_used_car_post',
          key: 'id',
        },
        allowNull: false,
        comment: 'Reference to the used car post being inquired about',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      buyer_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_user',
          key: 'id',
        },
        allowNull: false,
        comment: 'Reference to the user making the inquiry (buyer)',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      seller_id: {
        type: Sequelize.UUID,
        references: {
          model: 'tbl_user',
          key: 'id',
        },
        allowNull: false,
        comment: 'Reference to the user who owns the car post (seller)',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
    }, {
      comment: 'Inquiry from a potential buyer to a seller about a used car post',
    });

    // Add indexes for tbl_used_car_saved_search
    await queryInterface.addIndex('tbl_used_car_saved_search', ['user_id'], {
      name: 'idx_used_car_saved_search_user_id',
    });
    await queryInterface.addIndex('tbl_used_car_saved_search', ['manufacturer_id'], {
      name: 'idx_used_car_saved_search_manufacturer_id',
    });
    await queryInterface.addIndex('tbl_used_car_saved_search', ['model_id'], {
      name: 'idx_used_car_saved_search_model_id',
    });
    await queryInterface.addIndex('tbl_used_car_saved_search', ['category_id'], {
      name: 'idx_used_car_saved_search_category_id',
    });
    await queryInterface.addIndex('tbl_used_car_saved_search', ['notification_enabled'], {
      name: 'idx_used_car_saved_search_notification_enabled',
    });
    await queryInterface.addIndex('tbl_used_car_saved_search', ['last_executed_at'], {
      name: 'idx_used_car_saved_search_last_executed_at',
    });

    // Add indexes for tbl_used_car_inquiry
    await queryInterface.addIndex('tbl_used_car_inquiry', ['used_car_post_id'], {
      name: 'idx_used_car_inquiry_post_id',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['buyer_id'], {
      name: 'idx_used_car_inquiry_buyer_id',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['seller_id'], {
      name: 'idx_used_car_inquiry_seller_id',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['inquiry_status'], {
      name: 'idx_used_car_inquiry_status',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['inquiry_type'], {
      name: 'idx_used_car_inquiry_type',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['is_read_by_seller'], {
      name: 'idx_used_car_inquiry_read_by_seller',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['is_read_by_buyer'], {
      name: 'idx_used_car_inquiry_read_by_buyer',
    });
    await queryInterface.addIndex('tbl_used_car_inquiry', ['created_at'], {
      name: 'idx_used_car_inquiry_created_at',
    });
    
    // Add unique constraint for buyer-post-time combination to prevent spam
    await queryInterface.addIndex('tbl_used_car_inquiry', ['buyer_id', 'used_car_post_id', 'created_at'], {
      unique: true,
      name: 'idx_used_car_inquiry_unique_buyer_post_time',
    });
  },

  async down(queryInterface, Sequelize) {
    // Drop indexes first
    await queryInterface.removeIndex('tbl_used_car_saved_search', 'idx_used_car_saved_search_user_id');
    await queryInterface.removeIndex('tbl_used_car_saved_search', 'idx_used_car_saved_search_manufacturer_id');
    await queryInterface.removeIndex('tbl_used_car_saved_search', 'idx_used_car_saved_search_model_id');
    await queryInterface.removeIndex('tbl_used_car_saved_search', 'idx_used_car_saved_search_category_id');
    await queryInterface.removeIndex('tbl_used_car_saved_search', 'idx_used_car_saved_search_notification_enabled');
    await queryInterface.removeIndex('tbl_used_car_saved_search', 'idx_used_car_saved_search_last_executed_at');

    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_post_id');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_buyer_id');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_seller_id');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_status');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_type');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_read_by_seller');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_read_by_buyer');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_created_at');
    await queryInterface.removeIndex('tbl_used_car_inquiry', 'idx_used_car_inquiry_unique_buyer_post_time');

    // Drop tables
    await queryInterface.dropTable('tbl_used_car_inquiry');
    await queryInterface.dropTable('tbl_used_car_saved_search');
  }
};
