'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('tbl_advertising_image', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        allowNull: false,
        primaryKey: true,
      },
      image_url: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      position: {
        type: Sequelize.ENUM('banner', 'left', 'right', 'above'),
        allowNull: false,
      },
      page_key: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      created_at_unix_timestamp: {
        type: Sequelize.BIGINT,
        validate: {
          min: 0,
        },
      },
      created_at: {
        type: 'TIMESTAMP',
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: 'TIMESTAMP',
        allowNull: false,
      },
      deleted_at: {
        type: 'TIMESTAMP',
        allowNull: true,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('tbl_advertising_image');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_tbl_advertising_image_position";');
  }
};
