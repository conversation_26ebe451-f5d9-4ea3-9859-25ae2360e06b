ib-cov
*.seed
*.log
*.csv
*.dat
*.out
*.pid
*.gz
*.swp

pids
logs
results
tmp

#Build
public/css/main.css

# API keys and secrets
.env


.png
.gif


# Dependency directory
node_modules
bower_components

# Editors
.idea
*.iml

# OS metadata
.DS_Store
Thumbs.db

# Ignore built ts files
.vscode
dist
build
test
docker
image
video
!docker/docker-compose.yml
tests
scripts
package-lock.json
code-log.txt
# Elastic Beanstalk Files
.elasticbeanstalk/*
.ebextensions
aws_dockers
.nvmrc

ssl
sslkey

# Dockerfile
Dockerfile.bkhill
Dockerfile.goldkey
Dockerfile.kormsg-dev

# .env
.env.bkhill
.env.goldkey
.env.kormsg-dev

# docker-compose
docker-compose.bkhill.yml
docker-compose.goldkey.yml
docker-compose.kormsg-dev.yml

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# local files
tasks.json
tasks/ 
.cursor
.taskmaster
.env 
.env.local
.env.example
docker-compose.yml
Dockerfile.dev
docker-dev.sh
init-db 
init-mongo