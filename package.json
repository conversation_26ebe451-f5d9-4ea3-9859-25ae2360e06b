{"name": "express-typescript-starter", "version": "0.1.0", "description": "A starting point for Node.js express apps with TypeScript", "repository": {"type": "git", "url": "https://github.com/sahat/hackathon-starter.git"}, "author": "xxxigbnhqc", "license": "MIT", "scripts": {"dev": "NODE_ENV=development nodemon --config nodemon.json", "build": "npx tsc && npx babel build --out-dir dist && rm -rf build", "start": "NODE_ENV=production node dist/index.js", "sequelize": "npx sequelize", "sequelize:migrate": "sequelize db:migrate", "docs": "node scripts/generate-swagger-docs.js", "docs:serve": "npx http-server docs -o"}, "jest": {"globals": {"__TS_CONFIG__": "tsconfig.json"}, "moduleFileExtensions": ["ts", "js"], "testMatch": ["**/test/**/*.test.(ts|js)"], "testEnvironment": "node", "moduleNameMapper": {"@/(.*)": "./src/$1"}, "moduleDirectories": ["node_modules", "src"]}, "dependencies": {"@babel/plugin-transform-runtime": "^7.21.4", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^1.4.1", "@google-cloud/translate": "^7.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "ajv": "^5.5.2", "ajv-errors": "^1.0.0", "ajv-keywords": "^2.1.0", "apiai": "^4.0.3", "apple-signin-auth": "^1.5.1", "aws-sdk": "^2.746.0", "axios": "^0.18.0", "body-parser": "^1.18.2", "cheerio": "1.0.0-rc.3", "compression": "^1.7.4", "connect-mongo": "^1.3.2", "core-util-is": "^1.0.2", "cors": "^2.8.4", "custom-template-generator": "^0.1.2", "dateformat": "^3.0.3", "dayjs": "^1.11.13", "ddos": "^0.1.25", "dotenv": "^4.0.0", "express": "^4.16.3", "express-rate-limit": "^5.5.1", "express-session": "^1.15.6", "express-validator": "^4.2.1", "fb": "^2.0.0", "ffmpeg-static": "^5.2.0", "firebase-admin": "13.2.0", "fluent-ffmpeg": "^2.1.3", "geoip-lite": "^1.3.5", "geolib": "^2.0.24", "get-video-duration": "^4.0.0", "gifsicle": "^7.0.1", "guid": "0.0.12", "hbs": "^4.0.1", "in-app-purchase": "^1.10.7", "inquirer": "^6.1.0", "jsdom": "^15.1.1", "jsonexport": "^2.0.11", "jwt-simple": "^0.5.1", "lodash": "^4.17.4", "md5": "^2.2.1", "mime": "^1.6.0", "minipass": "2.7.0", "moment": "^2.18.1", "mongoose": "^4.12.0", "mongoose-timestamp": "^0.6.0", "morgan": "^1.9.0", "multer": "^1.3.0", "node-cron": "^4.0.7", "node-geocoder": "^3.27.0", "node-schedule": "^1.3.0", "node-xlsx": "^0.15.0", "nodemailer": "^5.1.1", "npm": "^6.9.0", "pg": "^8.11.3", "popbill": "^1.53.0", "probe-image-size": "^4.0.0", "raven": "^2.6.2", "request": "^2.83.0", "request-promise": "^4.2.2", "scrape-youtube": "0.0.5", "sequelize": "^4.38.1", "sequelize-replace-enum-postgres": "^1.4.0", "sharp": "0.34.0", "socket.io": "^2.1.1", "stripe": "^6.12.1", "uuid": "^8.3.2", "youtube-comment-api": "^3.0.2"}, "devDependencies": {"@babel/cli": "^7.21.0", "@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/preset-typescript": "^7.21.4", "@sequelize/cli": "^7.0.0-alpha.46", "@types/async": "^2.0.40", "@types/bcrypt-nodejs": "0.0.30", "@types/bluebird": "^3.5.16", "@types/body-parser": "^1.16.2", "@types/compression": "0.0.33", "@types/connect-mongo": "0.0.32", "@types/cors": "^2.8.1", "@types/del": "^3.0.1", "@types/dotenv": "^2.0.20", "@types/errorhandler": "0.0.30", "@types/express": "^4.11.1", "@types/express-session": "0.0.32", "@types/fb": "0.0.22", "@types/fluent-ffmpeg": "^2.1.20", "@types/geolib": "^2.0.23", "@types/gifsicle": "^5.2.0", "@types/jest": "^19.2.2", "@types/jquery": "^2.0.41", "@types/jwt-simple": "^0.5.33", "@types/lodash": "^4.14.63", "@types/lokijs": "^1.5.2", "@types/md5": "^2.1.32", "@types/mongodb": "^2.1.43", "@types/mongoose": "^4.7.9", "@types/morgan": "^1.7.32", "@types/multer": "^1.3.6", "@types/node": "^7.0.43", "@types/nodemailer": "^1.3.32", "@types/passport": "^0.3.3", "@types/pg": "^7.4.10", "@types/probe-image-size": "^7.2.0", "@types/qs": "^6.5.1", "@types/request": "^2.0.6", "@types/request-promise": "^4.1.38", "@types/sequelize": "^4.28.14", "@types/sharp": "^0.30.5", "@types/socket.io": "^1.4.38", "@types/supertest": "^2.0.0", "@types/uuid": "^8.3.4", "babel-plugin-module-resolver": "4.1.0", "concurrently": "^3.4.0", "esbuild-runner": "^2.2.2", "http-server": "^14.1.1", "install": "^0.13.0", "jest": "^29.5.0", "nodemon": "^1.11.0", "sass": "^1.69.5", "sequelize-cli": "^6.6.2", "shelljs": "^0.7.7", "supertest": "^2.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-jest": "^29.1.0", "ts-loader": "^2.3.7", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tslint": "^5.0.0", "typescript": "4.9.5"}, "engines": {"node": ">=20.x"}}