version: '3.7'

services:
  # BE
  backend:
    image: greenapps/be-kormsg-clone:kormsg
    container_name: be-kormsg-clone-kormsg
    deploy:
      resources:
        limits:
          cpus: "0.6"
          memory: "1G"
        reservations:
          cpus: "0.5"
          memory: "700M"
    logging:
      driver: 'json-file'
      options:
        max-file: '5'
        max-size: '10m'
    ports:
      - '3001:9879'
    volumes:
      - ./../../image:/app/image
      - ./../../video:/app/video
    restart: unless-stopped
