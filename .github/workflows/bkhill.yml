name: CI/CD Pipeline CMS for Bkhill Environment

env:
  IMAGE_NAME: ${{ vars.IMAGE_NAME }}
  IMAGE_TAG: ${{ vars.IMAGE_TAG_BKHILL }}
  DOCKER_COMPOSE: ${{ vars.DOCKER_COMPOSE_BKHILL }}

on:
  push:
    branches: [bkhill]
  pull_request:
    branches: [bkhill]

jobs:
  # Build Image
  build-and-push-image:
    strategy:
      matrix:
        node-version: [18]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Login to Docker Hub
        run:
          docker login -u ${{ secrets.DOCKER_USERNAME }} -p ${{
          secrets.DOCKER_PASSWORD }}
      - name: Build and push Docker image
        run: |
          docker build -t ${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }} -f Dockerfile.bkhill .
          docker push ${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          echo "Compile complete."

  # Deploy to server
  deploy-to-server:
    runs-on: [self-hosted, Linux, X64, bkhill]
    needs: [build-and-push-image]
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Login docker registry
        run:
          sudo docker login -u ${{secrets.DOCKER_USERNAME}} -p
          ${{secrets.DOCKER_PASSWORD}}
      - name: Pull image from Docker Hub
        run: docker pull ${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
      - name: Build service
        run: |
          docker-compose -f ${{ env.DOCKER_COMPOSE }} down
          docker-compose -f ${{ env.DOCKER_COMPOSE }} up -d
          docker image prune -f
          echo "Application successfully deployed."
