name: CI/CD Pipeline Backend for Kormsg Environment

env:
  IMAGE_NAME: ${{ vars.IMAGE_NAME }}
  IMAGE_TAG: ${{ vars.IMAGE_TAG_KORMSG }}
  DOCKER_COMPOSE: ${{ vars.DOCKER_COMPOSE_KORMSG }}
  PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_KORMSG }}
  HOST_NAME: ${{secrets.SSH_HOST_NAME_KORMSG}}
  USER_NAME: ${{secrets.SSH_USER_DOOGA}}

on:
  push:
    branches: [kormsg]

jobs:
  # Build Image
  build-and-push-image:
    strategy:
      matrix:
        node-version: [10]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Login to Docker Hub
        run:
          sudo docker login -u ${{ secrets.DOCKER_USERNAME }} -p ${{
          secrets.DOCKER_PASSWORD }}
      - name: Build and push Docker image
        run: |
          sudo docker build -t ${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }} -f Dockerfile.kormsg .
          sudo docker push ${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          echo "Compile complete."

  # Deploy to server
  deploy-to-server:
     needs: [build-and-push-image]
     runs-on: ubuntu-latest
     steps:
       - name: Check out Repository
         uses: actions/checkout@v3

       - name: Deploy to EC2
         run: |
           echo "Starting deploy"
           echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
           ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST_NAME} '
             cd kormsg-api &&
             git pull origin ${{ env.IMAGE_TAG }} &&
             docker pull ${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }} &&
             docker-compose -f ${{ env.DOCKER_COMPOSE }} down &&
             docker-compose -f ${{ env.DOCKER_COMPOSE }} --compatibility up -d &&
             docker image prune -f
           '
           echo "Deploy Successful"
