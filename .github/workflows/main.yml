name: CI/CD Docker Hub

on:
  push:
    branches: ["production"]

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Cache Docker Layers
        uses: actions/cache@v2
        with:
          path: /tmp/.buildx-cache
          key: ${{runner.os}}-buildx-${{github.sha}}
          restore-keys: |
            ${{runner.os}}-buildx-
      - name: Login To Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{secrets.DOCKER_HUB_USERNAME}}
          password: ${{secrets.DOCKER_HUB_ACCESS_TOKEN}}

      - name: Setup Docker Build
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and Push Docker
        id: docker_build
        uses: docker/build-push-action@v3
        with:
          context: ./
          file: ./Dockerfile
          builder: ${{steps.buildx.outputs.name}}
          push: true
          tags: ${{secrets.DOCKER_HUB_USERNAME}}/myhome:latest
          target: production
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Image Digest
        run: echo ${{steps.docker_build.outputs.digest}}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Check out Repository
        uses: actions/checkout@v3

      - name: Deploy to EC2
        env:
          PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          HOST_NAME: ${{secrets.SSH_HOST_NAME}}
          USER_NAME: ${{secrets.SSH_USER}}
        run: |
          echo "Starting deploy"
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST_NAME} '
            cd /var/myhome/docker &&
            ./bash.sh
          '
          echo "Deploy Successful"