# Define target branch to merge
  target_branches=("bkhill" "goldkey" "kormsg" "newbkshop")
  
  for branch in "${target_branches[@]}"
  do
      git checkout $branch
      git pull origin $branch
      git merge --no-ff kormsg-main
  
      if [ $? -eq 0 ]; then
          git push origin $branch
          echo "Merged changes from kormsg-main into $branch successfully"
      else
          echo "Merge conflict occurred with $branch. Resolve conflicts manually."
          exit 1
      fi
  done