version: '3.7'

services:
  # BE
  backend-goodvina:
    image: greenapps/be-kormsg-clone:goodvina
    container_name: be-kormsg-clone-goodvina
    logging:
      driver: 'json-file'
      options:
        max-file: '5'
        max-size: '10m'
    ports:
      - '9879:9879'
    volumes:
      - ./../../image:/app/image
      - ./../../video:/app/video
    restart: always
    networks:
      - goodvina_network
networks:
  goodvina_network:
    driver: bridge
