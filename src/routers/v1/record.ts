import { <PERSON>rudRouter } from '../crud';
import { Request, Response } from '../base';
import { recordController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
import { config } from '@/config';
export default class RecordRouter extends CrudRouter<typeof recordController> {
  constructor() {
    super(recordController);
  }

  customRouting() {
    this.router.get(
      '/check_in_app',
      this.getListMiddlewares(),
      this.route(this.checkInApp)
    );
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  async checkInApp(req: Request, res: Response) {
    const result = await this.controller.checkInApp();
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
