import {<PERSON><PERSON><PERSON>out<PERSON>} from '../crud'
import {Request, Response} from '../base'
import {siteController} from '@/controllers'
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    superAdminTypeMiddleware,
    adminTypeMiddleware, optionalInfoMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'

export default class SeoSsrRouter extends CrudRouter<typeof siteController> {
    constructor() {
        super(siteController)
    }

    customRouting() {
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
        this.router.put('/order-site/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }
    async getItem(req: Request, res: Response) {
        const { id } = req.params
        req.queryInfo.filter.id = id
        const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
        const result = await this.controller.getItem(req.queryInfo , true , user_id)
        this.onSuccess(res, result)
    }

    async getList(req: Request, res: Response) {
        const employee_id = req.tokenInfo ? req.tokenInfo.payload.employee_id : undefined;
        const result = await this.controller.getList(req.queryInfo , !!employee_id)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    getListMiddlewares(): any[] {
        return [optionalInfoMiddleware.run(),queryMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [optionalInfoMiddleware.run(),queryMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
}
