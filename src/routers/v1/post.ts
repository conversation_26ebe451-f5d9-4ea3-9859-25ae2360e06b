import {<PERSON>rudRouter} from '../crud';
import {Request, Response} from '../base';
import {postController} from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
  optionalInfoMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
import { limitRequest } from "@/config/utils";

export default class PostRouter extends CrudRouter<typeof postController> {
  constructor() {
    super(postController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    this.router.put(
        '/like_post/:id',
        this.updateMiddlewares(),
        this.route(this.likePostFunc)
    );
    this.router.put(
        '/unlike_post/:id',
        this.updateMiddlewares(),
        this.route(this.unlikePostFunc)
    );
    this.router.put(
        '/dislike_post/:id',
        this.updateMiddlewares(),
        this.route(this.dislikePostFunc)
    );
    this.router.put(
        '/undislike_post/:id',
        this.updateMiddlewares(),
        this.route(this.undislikePostFunc)
    );
    this.router.post(
        '/get_lists',
        this.getFilterListMiddlewares(),
        this.route(this.getListPost)
    );
    this.router.get(
        '/view_post/:id',
        this.getFilterListMiddlewares(),
        this.route(this.viewPost)
    );
    this.router.put(
        '/report_post/:id',
        this.updateMiddlewares(),
        this.route(this.reportFunc)
    );
    this.router.put(
        '/unreport/:id',
        this.updateMiddlewares(),
        this.route(this.unreportFunc)
    );
    this.router.post('/admin_create', this.roleAdminMiddlewares(), this.route(this.createByAdmin))
    this.router.get('/list', this.getByAdminMiddlewares(), this.route(this.getListByAdmin))
    this.router.get('/rank', this.route(this.rankPost))
    this.router.get('/rank_v2', this.route(this.rankPostV2))
    this.router.get('/count/user', [authInfoMiddleware.run()], this.route(this.countByUser))
  }

  async reportFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.reportFunc(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async unreportFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.unreportFunc(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  // task view post detail
  async viewPost(req: Request, res: Response) {
    console.log('abbb ', req);
    req.params.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    if (!req.queryInfo.filter) {
      req.queryInfo.filter = {};
    }

    req.queryInfo.filter.id = id;

    const result = await this.controller.viewPost(req.params, req.queryInfo);
    this.onSuccess(res, result);
  }

  async getListPost(req: Request, res: Response) {
    req.body.user_id = req && req.tokenInfo && req.tokenInfo.payload && req.tokenInfo.payload.user_id;
    if (req && req.body && req.body.user_id) {
      req.params.user_id = req.body.user_id;
    }
    const result = await this.controller.getListPost(req.params, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async dislikePostFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.dislikePost(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async undislikePostFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.undislikePost(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async likePostFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.likePost(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async unlikePostFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.unlikePost(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async create(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    await this.validateJSON(req.body, {
      required: ['content', 'location'],
    });
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }

  async getList(req: Request, res: Response) {
    req.params.user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const result = await this.controller.getList(req.params, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async delete(req: Request, res: Response) {
    const {id} = req.params;
    const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const result = await this.controller.delete(req.params, {
      filter: {
        id,
        ...(user_id ? {user_id} : {})
      },
    });
    this.onSuccess(res, result);
  }

  async createByAdmin(req: Request, res: Response) {
    req.body.employee_id = req.tokenInfo.payload.employee_id;
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        content: {
          type: 'string',
        },
        location: {
          type: 'string',
        },
        user_name: {
          type: 'string',
        },
        category_id: {
          type: 'string',
        },
      },
      required: ['content', 'employee_id', 'location', 'execute_at', 'category_id'],
    });
    const result = await this.controller.createByAdmin(req.body);
    this.onSuccess(res, result);
  }

  async getListByAdmin(req: Request, res: Response) {
    const result = await this.controller.getListByAdmin(req.params, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async update(req: Request, res: Response) {
    const { id } = req.params
    const user_id = req.tokenInfo.payload.user_id;
    const result = await this.controller.update(req.body, {
      filter: {
        id,
        ...(user_id ? {user_id} : {})
      },
    })
    this.onSuccess(res, result)
  }

  async getItem(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const {id} = req.params;
    req.queryInfo.filter.id = id
    const result = await this.controller.getItem(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }

  async rankPost(req: Request, res: Response) {
    const result = await this.controller.rankPost(req.query);
    this.onSuccess(res,result)
  }

  async rankPostV2(req: Request, res: Response) {
    const result = await this.controller.rankPostV2(req.query);
    this.onSuccess(res,result)
  }

  async countByUser(req: Request, res: Response) {
    const user_id = req.tokenInfo.payload.user_id;
    const result = await this.controller.countByUser(user_id);
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run(),optionalInfoMiddleware.run()];
  }

  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run(),optionalInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run() , limitRequest(10 * 1000,2 , '최대 10초 동안 2개만 올릴 수 있습니다!')];
  }

  roleAdminMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  getByAdminMiddlewares(): any[] {
    return [queryMiddleware.run(), authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
