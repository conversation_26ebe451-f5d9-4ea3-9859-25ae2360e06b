import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { popupWindowController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'
export default class PopupWindowRouter extends <PERSON><PERSON><PERSON>outer<typeof popupWindowController> {
    constructor() {
        super(popupWindowController)
    }

    customRouting() {
        this.router.get('/get_lists', this.getListMiddlewares(), this.route(this.getListV2))
        this.router.put('/order', this.updateMiddlewares(), this.route(this.updateOrder))
    }

    async getListV2(req: Request, res: Response) {
        const result = await this.controller.getListV2(req.queryInfo)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async updateOrder(req: Request, res: Response) {
        const result = await this.controller.updateOrder(req.body)
        this.onSuccess(res, result)
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
}