import { CrudRouter } from '../crud'
import {
  queryMiddleware,
  adminTypeMiddleware,
  authInfoMiddleware,
} from '@/middlewares'
import { appVersionController } from '@/controllers'

/**
 * @swagger
 * tags:
 *   name: AppVersion
 *   description: App version management
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     AppVersion:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated ID of the app version
 *         version:
 *           type: string
 *           description: The version number of the app
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the app version was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the app version was last updated
 */

export default class AppVersion extends CrudRouter<
  typeof appVersionController
> {
  constructor() {
    super(appVersionController)
  }

  /**
   * @swagger
   * /app_version:
   *   get:
   *     summary: Get the app version
   *     tags: [AppVersion]
   *     responses:
   *       200:
   *         description: The app version details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AppVersion'
   */
  customRouting() {
    this.router.get('/', this.getListMiddlewares(), this.route(this.getItem))

    /**
     * @swagger
     * /app_version:
     *   put:
     *     summary: Update the app version
     *     tags: [AppVersion]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               version:
     *                 type: string
     *                 description: The new version number
     *     responses:
     *       200:
     *         description: The app version updated successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/AppVersion'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put('/', this.updateMiddlewares(), this.route(this.update))
  }

  defaultRouting() {}
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
