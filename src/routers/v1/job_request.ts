import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { jobRequestController } from "@/controllers";
import { authInfoMiddleware, queryMiddleware } from "@/middlewares";
import { Request, Response } from "@/routers/base";
import { uploadToS3 } from "@/utils/uploadS3";
import * as multer from "multer";
/**
 * @swagger
 * tags:
 *   name: Job Request
 *   description: Job request endpoints
 */
export default class JobRequest extends CrudRouter<typeof jobRequestController> {
  constructor() {
    super(jobRequestController);
  }
  /**
   * @swagger
   * components:
   *   schemas:
   *     JobRequestCreate:
   *       type: object
   *
   */

  customRouting() {
    const upload = multer({
      storage: multer.memoryStorage(),
      limits: { fileSize: 500 * 1024 * 1024 },
    });
    /**
     * @swagger
     * /job_request:
     *   post:
     *     tags:
     *       - Job Request
     *     summary: Create a new job request
     *     description: Submit a new job request with necessary job details.
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *
     *     responses:
     *       201:
     *         description: Job request created successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *
     */

    this.router.post(
      "/",
      this.createMiddlewares(),
      upload.array("image_url", 10),
      this.route(this.create)
    );
    /**
     * @swagger
     * /job_request/get_lists:
     *   get:
     *     tags:
     *       - Job Request
     *     summary: Get list of job requests
     *     description: Retrieve a paginated list of job requests with optional filters and sorting.
     *     parameters:
     *       - $ref: '#/components/parameters/PageParam'
     *       - $ref: '#/components/parameters/PageSizeParam'
     *       - $ref: '#/components/parameters/SortParam'
     *       - name: is_favorite
     *         in: query
     *         required: false
     *         schema:
     *           type: boolean
     *         description: Filter jobs by favorite status
     *       - name: quote_status
     *         in: query
     *         required: false
     *         schema:
     *           type: string
     *           enum: [has_quote, no_quote, pending_confirm]
     *         description: Filter jobs based on quote status
     *       - name: userId
     *         in: query
     *         required: false
     *         schema:
     *           type: string
     *         description: Filter jobs by specific user ID
     *     responses:
     *       200:
     *         description: List of job requests retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     $ref: '#/components/schemas/JobRequest'
     *       400:
     *         description: Invalid request parameters
     *       500:
     *         description: Internal server error
     */
    /**
     * @swagger
     * /job_request/{id}:
     *   get:
     *     tags:
     *       - Job Request
     *     summary: Get job request detail
     *     description: Retrieve detailed information of a specific job request by ID
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Job request ID
     *     responses:
     *       200:
     *         description: Job request retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   $ref: '#/components/schemas/JobRequest'
     *       404:
     *         description: Job request not found
     *       500:
     *         description: Internal server error
     */
    /**
     * @swagger
     * /job_request/{id}:
     *   delete:
     *     tags:
     *       - Job Request
     *     summary: Delete a job request
     *     description: Delete a specific job request by ID
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Job request ID
     *     responses:
     *       200:
     *         description: Job request deleted successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Job request not found
     *       500:
     *         description: Internal server error
     */
    /**
     * @swagger
     * /job_request/{id}:
     *   patch:
     *     tags:
     *       - Job Request
     *     summary: Update a job request
     *     description: Update information of an existing job request
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: ID of the job request to update
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               title:
     *                 type: string
     *                 description: Updated title of the job
     *               content:
     *                 type: string
     *                 description: Updated content/description of the job
     *               image_url:
     *                 type: string
     *                 format: uri
     *                 description: Updated image URL
     *               category_id:
     *                 type: string
     *                 description: Updated category ID
     *               budget:
     *                 type: integer
     *                 description: Updated budget for the job
     *               preferred_date:
     *                 type: string
     *                 format: date-time
     *                 description: Updated preferred date
     *               is_favorite:
     *                 type: boolean
     *                 description: Whether the job is marked as favorite
     *     responses:
     *       200:
     *         description: Job request updated successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   $ref: '#/components/schemas/JobRequest'
     *       400:
     *         description: Invalid data
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Job request not found
     *       500:
     *         description: Internal server error
     */

    this.router.get(
      "/get_lists",
      this.getAuthListMiddlewares(),
      this.route(this.getListV2)
    );
    this.router.get(
      "/count-category",
      this.getAuthListMiddlewares(),
      this.route(this.getListCountCategory)
    );
    this.router.get(
      "/:id",
      this.getAuthListMiddlewares(),
      this.route(this.getItem)
    );
    this.router.delete(
      "/:id",
      this.deleteMiddlewares(),
      this.route(this.delete)
    );
    this.router.put(
      "/:id",
      this.updateMiddlewares(),
      upload.array("image_url", 10),
      this.route(this.update)
    );
  }

  async create(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    if (req.files && Array.isArray(req.files)) {
      const uploadResult = await Promise.all(
        (req.files as Express.Multer.File[]).map((file) => uploadToS3(file))
      );
      console.log(uploadResult.map((result) => result.url));

      if (uploadResult) {
        req.body.image_url = uploadResult.map((result) => result.url);
      }
    }
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }

  async getListCountCategory(req: Request, res: Response) {
    const result = await this.controller.getListCountCategory(
      req.body,
      req.queryInfo
    );
    this.onSuccess(res, result);
  }
  async getListV2(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    if (req.query && req.query.mode) {
      req.body.mode = req.query.mode;
    }
    const result = await this.controller.getListV2(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }
  async update(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    if (req.files && Array.isArray(req.files)) {
      const uploadResult = await Promise.all(
        (req.files as Express.Multer.File[]).map((file) => uploadToS3(file))
      );
      if (uploadResult) {
        req.body.image_url = uploadResult.map((result) => result.url);
      }
    }
    const { id } = req.params;
    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }
  async getItem(req: Request, res: Response) {
        if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.queryInfo.filter.user_id = req.tokenInfo.payload.user_id;
      
    }
    const { id } = req.params;
    req.queryInfo.filter.id = id;
    const result = await this.controller.getItem(req.queryInfo);
    this.onSuccess(res, result);
  }
  getAuthListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
}
