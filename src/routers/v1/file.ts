import { Request, Response, BaseRouter } from '../base'
import {
  queryMiddleware,
  authInfoMiddleware,
  blockMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'
import * as multer from 'multer'
import * as fs from 'fs'
import * as express from 'express'
import { config } from '@/config'
import * as _ from 'lodash'
import { errorService } from '@/services'
import { uploadToS3 } from '@/utils/uploadS3'
const AWS = require('aws-sdk')

// const IMAGE_URL_SERVER = `${config.server.protocol}://${config.server.host}:${config.server.port}` + '/api/v1/image/get/'
// const IMAGE_URL_SERVER_FOR_PRODUCTION =
//   `https://${config.server.host}:8002` + '/api/v1/file/get/'
const FILE_IMAGE_PATH = 'file/'

export default class ImageRouter extends BaseRouter {
  router: express.Router
  constructor() {
    super()
    this.router = express.Router()
    const upload = multer({
    storage: multer.memoryStorage(),
    limits: { fileSize: 500 * 1024 * 1024 },
  });
    this.router.get('/get/:filename', this.route(this.getImage))
    this.router.post(
      '/upload',
      this.updateImageMiddlewares(),
      upload.single('file'),
      this.route(this.updateFile)
    )
    this.router.post(
      '/upload-multi',
      this.updateImageMiddlewares(),
      upload.array('files', 20),
      this.route(this.updateMultipleFiles)
    )
  }

  // async updateFile(req: Request, res: Response) {
  //   console.log("===========");
    
  //   try {
  //     const s3 = new AWS.S3({
  //       accessKeyId: ID,
  //       secretAccessKey: SECRET,
  //     })
  //     const fileUrl = IMAGE_URL_SERVER_FOR_PRODUCTION
  //     const filename = req.file.filename
  //     console.log('@#$%^$#', req.file)
  //     const url = fileUrl + filename
  //     if (req.tokenInfo.payload.user_id !== undefined) {
  //       req.body.user_id = req.tokenInfo.payload.user_id
  //       req.body.file_url = url
  //     }

  //     const fileContent = fs.readFileSync(FILE_IMAGE_PATH + filename)

  //     const params = {
  //       Bucket: BUCKET_NAME,
  //       Key: filename, // File name you want to save as in S3
  //       Body: fileContent,
  //       ACL: 'public-read',
  //     }
  //     // Uploading files to the bucket
  //     s3.upload(params, (err: any, data: any) => {
  //       if (err) {
  //         throw err
  //       }
  //       // urls3 = data.Location
  //       // console.log(`File uploaded successfully. ${data.Location}`);
  //       const urls3 = `https://${process.env.IMAGE_URL}/${filename}`
  //       fs.unlinkSync(FILE_IMAGE_PATH + req.file.filename)
  //       // fs.unlinkSync(FILE_IMAGE_PATH + filename);
  //       this.onSuccess(res, {url: urls3, filename})
  //     })
  //   } catch (error) {
  //     if (error.message) {
  //       throw errorService.database.queryFail(error.message)
  //     } else {
  //       throw error
  //     }
  //   }
  //   return
  // }

  async updateFile(req: Request, res: Response) {
    try {
      if (!req.file) {
        return this.onError(res, errorService.database.queryFail("File is missing"))
      }
  
      const { url, duration, thumb } = await uploadToS3(req.file)
  
      if (req.tokenInfo.payload.user_id !== undefined) {
        req.body.user_id = req.tokenInfo.payload.user_id
        req.body.file_url = url
      }
  
      this.onSuccess(res, {
        url,
        filename: req.file.originalname,
        ...(duration !== undefined && { duration }),
        ...{thumb : thumb|| null}
      })
    } catch (error) {
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
  }
  async updateMultipleFiles(req: Request, res: Response) {
    try {
      const files = req.files as Express.Multer.File[]; 
      if (!files || !Array.isArray(files)) {
        return this.onError(res, errorService.database.queryFail("Files are missing"))
      }
  
      const uploadPromises = files.map(file => uploadToS3(file))
      const uploadedResults = await Promise.all(uploadPromises)
  
      const userId = req.tokenInfo.payload.user_id
      const result = uploadedResults.map(({ url, duration, thumb }, index) => ({
        url,
        originalName: files[index].originalname,
        user_id: userId,
        ...(duration !== undefined && { duration }),
        ...{thumb : thumb || null}
      }))
  
      return this.onSuccess(res, result)
    } catch (error) {
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
  }
  
  async getImage(req: Request, res: Response) {
    const {filename} = req.params
    // console.log('!!! about to check file exist');
    fs.exists(FILE_IMAGE_PATH + filename, function (exists) {
      // console.log('!!! did check file exist', exists);
      if (exists) {
        fs.readFile(FILE_IMAGE_PATH + filename, function (err, data) {
          if (err) {
            console.log('!!! error reading file')
            res.writeHead(500, {'Content-Type': 'application/json'})
            res.write(`{
                            "code": 500,
                            "type": "database_exception_query_fail",
                            "message": "error reading file"
                        }`)
            res.end()
          } else {
            // console.log('!!! read a file successfully');
            // res.writeHead(200, { 'Content-Type': 'image/png' });
            // res.end(data);
            res.download(FILE_IMAGE_PATH + filename)
          }
        })
      } else {
        res.writeHead(500, {'Content-Type': 'application/json'})
        res.write(`{
                    "code": 500,
                    "type": "database_exception_query_fail",
                    "message": "file does not exist"
                }`)
        res.end()
      }
    })
  }

  updateImageMiddlewares(): any[] {    
    return [authInfoMiddleware.run()]
  }
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    // return [blockMiddleware.run()]
    return []
  }
  deleteAllMiddlewares(): any[] {
    return [blockMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [blockMiddleware.run()]
  }
}
