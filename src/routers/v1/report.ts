import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { reportController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class ReportRouter extends CrudRouter<typeof reportController> {
  constructor() {
    super(reportController);
  }

  customRouting() {
    this.router.put(
      '/restore_multiples_report_subject',
      this.updateMiddlewares(),
      this.route(this.restoreMultiplesFunc)
    );
    this.router.delete(
      '/delete_multiples_report_subject',
      this.deleteMiddlewares(),
      this.route(this.deleteMultiplesFunc)
    );
    this.router.get(
      '/count_report',
      this.route(this.countReport)
    );

    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }
  
  async restoreMultiplesFunc(req: any, res: Response) {
    if (_.has(req.query, 'items')) {
      req.query.items = JSON.parse(req.query.items) || {};
    }
    await this.validateJSON(req.query, {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          uniqueItems: true,
          minItems: 1,
          items: { type: 'string' },
        },
      },
      required: ['items'],
      additionalProperties: false,
    });
    const { items } = req.query;
    const result = await this.controller.restoreMultiplesFunc({
      filter: { id: { $in: items } },
    });
    this.onSuccess(res, result);
  }

  async deleteMultiplesFunc(req: any, res: Response) {
    if (_.has(req.query, 'items')) {
      req.query.items = JSON.parse(req.query.items) || {};
    }
    await this.validateJSON(req.query, {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          uniqueItems: true,
          minItems: 1,
          items: { type: 'string' },
        },
      },
      required: ['items'],
      additionalProperties: false,
    });
    const { items } = req.query;
    const result = await this.controller.deleteMultiplesFunc({
      filter: { id: { $in: items } },
    });
    this.onSuccess(res, result);
  }
  async countReport(req: any, res: Response) {
    const result = await this.controller.countReport();
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
