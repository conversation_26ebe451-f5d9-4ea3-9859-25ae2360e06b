/**
 * @swagger
 * tags:
 *   name: SettingStation
 *   description: Setting station management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     SettingStation:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the station
 *         name:
 *           type: string
 *           description: Station name
 *         en_name:
 *           type: string
 *           description: English name of the station
 *         status:
 *           type: boolean
 *           description: Station status
 *         region:
 *           type: string
 *           description: Region the station belongs to
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import { CrudRouter } from '../crud'
import { Request, Response } from '../base'
import { settingStationController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'
import multer = require('multer')

export default class SettingStationRouter extends CrudRouter<typeof settingStationController> {
    constructor() {
        super(settingStationController)
    }

    /**
     * @swagger
     * /setting-station:
     *   get:
     *     summary: Get all stations
     *     tags: [SettingStation]
     *     parameters:
     *       - $ref: '#/components/parameters/PageParam'
     *       - $ref: '#/components/parameters/PageSizeParam'
     *       - $ref: '#/components/parameters/FilterParam'
     *       - $ref: '#/components/parameters/SortParam'
     *     responses:
     *       200:
     *         description: List of stations
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 data:
     *                   type: array
     *                   items:
     *                     $ref: '#/components/schemas/SettingStation'
     *
     * /setting-station/import_excel:
     *   post:
     *     summary: Import stations from Excel file
     *     tags: [SettingStation]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               file:
     *                 type: string
     *                 format: binary
     *     responses:
     *       200:
     *         description: Stations imported successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *
     * /setting-station/order-item/{id}:
     *   put:
     *     summary: Update station order
     *     tags: [SettingStation]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               order:
     *                 type: integer
     *     responses:
     *       200:
     *         description: Station order updated successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, 'image/')
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        const upload = multer({storage: storage})
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))

        this.router.post(
            '/import_excel',
            this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.put('/order-item/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }
    async importExcel(req: Request, res: Response) {
        const url = 'image/' + req.file.filename
        req.body.url = url

        const result = await this.controller.importExcel(req.body)
        this.onSuccess(res, result)
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}