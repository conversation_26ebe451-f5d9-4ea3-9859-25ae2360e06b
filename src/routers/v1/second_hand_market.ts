import {<PERSON><PERSON><PERSON>out<PERSON>} from '../crud';
import {Request, Response} from '../base';
import {secondHandMarketController} from '@/controllers';
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    superAdminTypeMiddleware,
    adminTypeMiddleware, optionalInfoMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class SecondHandMarketRoute extends CrudRouter<typeof secondHandMarketController> {
    constructor() {
        super(secondHandMarketController);
    }


    customRouting() {
        this.router.post('/call/:id', this.route(this.call))
        this.router.put(
            '/like_item/:id',
            this.updateMiddlewares(),
            this.route(this.likeProduct)
        )
        this.router.put(
            '/unlike_item/:id',
            this.updateMiddlewares(),
            this.route(this.unlikeProduct)
        )
        this.router.put(
            '/update_state/:id',
            this.updateMiddlewares(),
            this.route(this.updateState)
        )
        this.router.get(
            '/count/state',
            this.updateMiddlewares(),
            this.route(this.count)
        )
        this.router.put(
            '/put_item_on_top/:id',
            this.updateMiddlewares(),
            this.route(this.putItemOnTop)
        )
    }

    async create(req: Request, res: Response) {
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id;
        }

        await this.validateJSON(req.body, {
            required: [
                'images',
                'title',
                'description',
                'latitude',
                'longitude',
                'address',
                'category_id',
            ],
        });
        const result = await this.controller.create(req.body);
        this.onSuccess(res, result);
    }

    async call(req: Request, res: Response) {
        const {id} = req.params
        const result = await this.controller.call({
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async likeProduct(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const {id} = req.params
        const result = await this.controller.likeProduct(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }
    async unlikeProduct(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const {id} = req.params
        const result = await this.controller.unlikeProduct(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async getList(req: Request, res: Response) {
        req.params.user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
        const result = await this.controller.getList(req.params, req.queryInfo);
        this.onSuccessAsList(res, result, undefined, req.queryInfo);
    }

    async getItem(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
        const {id} = req.params;
        req.queryInfo.filter.id = id
        const result = await this.controller.getItem(req.body, req.queryInfo);
        this.onSuccess(res, result);
    }

    async updateState(req: Request, res: Response) {
        const {id} = req.params
        const {state} = req.body
        const result = await this.controller.updateState(id, state , req.tokenInfo.payload.user_id)
        this.onSuccess(res, result)
    }

    async count(req: Request, res: Response) {
        const {thema_id} = req.query
        const result = await this.controller.count(req.tokenInfo.payload.user_id , thema_id as string)
        this.onSuccess(res, result)
    }

    async putItemOnTop(req: Request, res: Response) {
        await this.validateJSON(req.params, {
            required: [
                'id',
            ],
        });
        const {id} = req.params
        const result = await this.controller.putItemOnTop(id)
        this.onSuccess(res, result)
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run(),optionalInfoMiddleware.run()];
    }


    getItemMiddlewares(): any[] {
        return [queryMiddleware.run(),optionalInfoMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }
}
