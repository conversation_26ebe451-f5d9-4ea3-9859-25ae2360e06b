import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../crud";
import { quote<PERSON>ontroller } from "@/controllers";
import { authInfoMiddleware, queryMiddleware } from "@/middlewares";
import { Request, Response } from "@/routers/base";
import { uploadToS3 } from "@/utils/uploadS3";
import * as multer from "multer";
/**
 * @swagger
 * tags:
 *   name: Quote
 *   description: Quote endpoints
 */
export default class QuoteController extends CrudRouter<
  typeof quoteController
> {
  constructor() {
    super(quoteController);
  }
/**
 * @swagger
 * components:
 *   schemas:
 *     Quote:
 *       type: object
 *       required:
 *         - job_id
 *         - user_id
 *         - price
 *       properties:
 *         id:
 *           type: string
 *           description: ID of the quote
 *           example: 8fabc271-5d6b-4be4-9c55-e75f9e2a4f67
 *         job_id:
 *           type: string
 *           description: ID of the related job request
 *           example: job_123456
 *         user_id:
 *           type: string
 *           description: ID of the expert submitting the quote
 *           example: user_7890
 *         price:
 *           type: number
 *           description: Quoted price
 *           example: 1500000
 *         content:
 *           type: string
 *           description: Quote description or message
 *           example: T<PERSON><PERSON> có thể hoàn thành công việc này trong 2 ngày.
 *         image_url:
 *           type: string
 *           format: uri
 *           description: Optional image related to the quote
 *           example: https://s3.example.com/uploads/quote-image.jpg
 *         phone:
 *           type: string
 *           description: Optional contact phone number
 *           example: 0987654321
 *         is_confirmed:
 *           type: boolean
 *           description: Whether the quote has been confirmed by the job requester
 *           example: false
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *           example: 2025-04-10T14:23:00.000Z
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *           example: 2025-04-10T14:23:00.000Z
 */

  customRouting() {
/**
 * @swagger
 * /quote:
 *   post:
 *     tags:
 *       - Quote
 *     summary: Create a new quote
 *     description: Submit a new quote for a job request (with optional image upload)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             $ref: '#/components/schemas/Quote'
 *     responses:
 *       201:
 *         description: Quote created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Quote'
 *       400:
 *         description: Invalid data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /quote/get_lists:
 *   get:
 *     tags:
 *       - Quote
 *     summary: Get list of quotes
 *     description: Retrieve a list of quotes with optional filters like job_id and user_id.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - in: query
 *         name: job_id
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by Job Request ID
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by Expert (User) ID
 *       - in: query
 *         name: mode
 *         schema:
 *           type: string
 *           enum: [user, expert]
 *         required: false
 *         description: If set to 'user', exclude current user's quotes
 *     responses:
 *       200:
 *         description: List of quotes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                     rows:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Quote'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 *//**
 * @swagger
 * /quote/{id}/confirm:
 *   put:
 *     tags:
 *       - Quote
 *     summary: Confirm a quote
 *     description: Confirm a quote submitted for a job request. Only the job requester can confirm.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Quote ID to confirm
 *     responses:
 *       200:
 *         description: Quote confirmed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Quote'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Permission denied (only the job requester can confirm)
 *       404:
 *         description: Quote not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /quote/{id}:
 *   get:
 *     tags:
 *       - Quote
 *     summary: Get quote detail
 *     description: Retrieve detailed information of a specific quote by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Quote ID
 *     responses:
 *       200:
 *         description: Quote details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Quote'
 *       404:
 *         description: Quote not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /quote/{id}:
 *   delete:
 *     tags:
 *       - Quote
 *     summary: Delete a quote
 *     description: Delete a quote by ID. Only the user who created the quote may delete it.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Quote ID to delete
 *     responses:
 *       200:
 *         description: Quote deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Quote not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /quote:
 *   put:
 *     tags:
 *       - Quote
 *     summary: Update a quote
 *     description: Update quote information, including optional image upload.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID of the quote to update
 *               price:
 *                 type: number
 *                 description: Updated price for the quote
 *               content:
 *                 type: string
 *                 description: Updated quote description
 *               phone:
 *                 type: string
 *                 description: Updated contact phone number
 *               image_url:
 *                 type: string
 *                 format: binary
 *                 description: Optional image to upload for the quote
 *     responses:
 *       200:
 *         description: Quote updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Quote'
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Quote not found
 *       500:
 *         description: Internal server error
 */

    const upload = multer({
      storage: multer.memoryStorage(),
      limits: { fileSize: 500 * 1024 * 1024 },
    });
    this.router.post(
      "/",
      this.createMiddlewares(),
      upload.array("image_url", 10),
      this.route(this.create)
    );
    this.router.put(
      "/",
      this.createMiddlewares(),
      upload.array("image_url", 10),
      this.route(this.update)
    );
    this.router.get(
      "/get_lists",
      this.getAuthListMiddlewares(),
      this.route(this.getListV2)
    );
      this.router.get(
      "/get_lists_related",
      this.getAuthListMiddlewares(),
      this.route(this.getListRelated)
    );
    this.router.put(
      "/:id/confirm",
      this.confirmAuthMiddlewares(),
      this.route(this.toggleConfirmQuote)
    );
  }

  async create(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }    
    if(req.files && Array.isArray(req.files)){
      
      const uploadResult = await Promise.all(
        (req.files as Express.Multer.File[]).map(file => uploadToS3(file))
      );
      console.log(uploadResult.map(result => result.url));

      
      if(uploadResult){
        req.body.image_url = uploadResult.map(result => result.url);
      }
    }
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }
  async update(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    if(req.file){
      const uploadResult = await Promise.all(
        (req.files as Express.Multer.File[]).map(file => uploadToS3(file))
      );
      if(uploadResult){
        req.body.image_url = uploadResult.map(result => result.url);
      }
    }
    const { id } = req.params;
    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }
  async getListV2(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    if (req.query && req.query.mode) {
      req.body.mode = req.query.mode;
    }

    const result = await this.controller.getListV2(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }

  async getListRelated(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }

    const result = await this.controller.getListRelated(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }

  async toggleConfirmQuote(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    const { id } = req.params;
    if (id) {
      req.body.quote_id = id;
    }

    const result = await this.controller.toggleConfirmQuote(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }

  getAuthListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  confirmAuthMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
