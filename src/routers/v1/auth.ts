import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { errorService, tokenService, userService } from '@/services'
import * as express from 'express'
import { Request, Response, BaseRouter } from '../base'
import {
  authController,
  employeeController,
  userController,
} from '@/controllers'
import { authInfoMiddleware } from '@/middlewares'
import { LOGIN_TYPE, ROLE } from '@/const'
import { UserService } from '@/services/crud/userService'
import { limitRequest } from "@/config/utils";
const nodemailer = require('nodemailer')
const URL_PAGE = 'https://massage-813ec.web.app/user-change-password?token='
const geoip = require('geoip-lite')

export default class AuthRouter extends BaseRouter {
  router: express.Router
  constructor() {
    super()
    this.router = express.Router()
    /**
     * @swagger
     * /auth/login:
     *   post:
     *     tags:
     *       - Auth
     *     summary: User login
     *     description: Authenticates a user with email/username and password
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               email:
     *                 type: string
     *                 description: User email or username
     *               password:
     *                 type: string
     *                 description: User password
     *             required:
     *               - email
     *               - password
     *     responses:
     *       200:
     *         description: Successful login
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: User data
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: JWT token for authentication
     *       401:
     *         description: Invalid credentials
     *       500:
     *         description: Server error
     */
    this.router.post('/login', this.route(this.login))
    
    /**
     * @swagger
     * /auth/login_biz:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Business user login
     *     description: Authenticates a business user
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               email:
     *                 type: string
     *                 description: Business user email
     *               password:
     *                 type: string
     *                 description: Business user password
     *             required:
     *               - email
     *               - password
     *     responses:
     *       200:
     *         description: Successful login
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: User data
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: JWT token for authentication
     *       401:
     *         description: Invalid credentials
     *       500:
     *         description: Server error
     */
    this.router.post('/login_biz', this.route(this.loginBiz))
    
    /**
     * @swagger
     * /auth/register:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Register new user
     *     description: Create a new user account
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               email:
     *                 type: string
     *                 description: User email
     *               phone:
     *                 type: string
     *                 description: User phone number
     *               password:
     *                 type: string
     *                 description: User password
     *               name:
     *                 type: string
     *                 description: User name
     *             required:
     *               - email
     *               - password
     *     responses:
     *       200:
     *         description: Successful registration
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: User data
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: JWT token for authentication
     *       400:
     *         description: Invalid input or user already exists
     *       500:
     *         description: Server error
     */
    this.router.post('/register',this.registerMiddlewares(), this.route(this.registerUser))
    this.router.post('/employee_login', this.route(this.employeeLogin))
    // this.router.post('/login_with_phone_number', this.route(this.loginWithPhoneNumber))
    this.router.post('/login_with_facebook', this.route(this.loginWithFacebook))
    
    /**
     * @swagger
     * /auth/login_with_google:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Login with Google
     *     description: Authenticates a user using Google credentials
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               google_id:
     *                 type: string
     *                 description: Google user ID
     *               email:
     *                 type: string
     *                 description: User email from Google
     *               name:
     *                 type: string
     *                 description: User name from Google
     *             required:
     *               - google_id
     *     responses:
     *       200:
     *         description: Successful login
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: User data
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: JWT token for authentication
     *                     isNewUser:
     *                       type: boolean
     *                       description: Whether this is a new user account
     *       401:
     *         description: Authentication failed
     *       500:
     *         description: Server error
     */
    this.router.post('/login_with_google', this.route(this.loginWithGoogle))

    /**
     * @swagger
     * /auth/admin_forget_password:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Send password reset email for admin
     *     description: Sends a password reset email to an admin's email address
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               email:
     *                 type: string
     *                 description: Admin email address
     *             required:
     *               - email
     *     responses:
     *       200:
     *         description: Reset email sent successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     url_get_password:
     *                       type: string
     *                       description: URL for password reset
     *       400:
     *         description: Invalid email or admin not found
     *       500:
     *         description: Server error
     */
    this.router.post(
      '/admin_forget_password',
      this.route(this.AdminForgetPassword)
    )

    /**
     * @swagger
     * /auth/admin_get_password:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Reset admin password
     *     description: Reset admin password using the token received via email
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               token:
     *                 type: string
     *                 description: Reset password token received via email
     *               new_password:
     *                 type: string
     *                 description: New password to set
     *             required:
     *               - token
     *               - new_password
     *     responses:
     *       200:
     *         description: Password successfully reset
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: Updated admin data
     *       400:
     *         description: Invalid token or password
     *       404:
     *         description: Admin not found
     *       500:
     *         description: Server error
     */
    this.router.post('/admin_get_password', this.route(this.adminGetPassword))

    /**
     * @swagger
     * /auth/forget_password:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Send password reset email for user
     *     description: Sends a password reset email to a user's email address
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               email:
     *                 type: string
     *                 description: User email address
     *             required:
     *               - email
     *     responses:
     *       200:
     *         description: Reset email sent successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     url_get_password:
     *                       type: string
     *                       description: URL for password reset
     *       400:
     *         description: Invalid email or user not found
     *       500:
     *         description: Server error
     */
    this.router.post('/forget_password', this.route(this.forgetPassword))

    /**
     * @swagger
     * /auth/get_password:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Reset user password with token
     *     description: Set a new password using reset token
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               token:
     *                 type: string
     *                 description: Reset password token
     *               password:
     *                 type: string
     *                 description: New password
     *             required:
     *               - token
     *               - password
     *     responses:
     *       200:
     *         description: Password successfully reset
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 message:
     *                   type: string
     *                   example: Password reset successful
     *       400:
     *         description: Invalid token or password
     *       404:
     *         description: Token not found
     *       500:
     *         description: Server error
     */
    this.router.post('/get_password', this.route(this.getPassword))

    /**
     * @swagger
     * /auth/get_locale:
     *   get:
     *     tags:
     *       - Auth
     *     summary: Get user's location based on IP
     *     description: Returns the country code based on the client's IP address
     *     responses:
     *       200:
     *         description: Successfully retrieved location
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     result:
     *                       type: string
     *                       description: Country code
     *                       example: "KR"
     *       500:
     *         description: Server error
     */
    this.router.get('/get_locale', this.route(this.getGeoip))

    /**
     * @swagger
     * /auth/login_with_kakaotalk:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Login with Kakaotalk
     *     description: Authenticate user using Kakaotalk credentials
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               access_token:
     *                 type: string
     *                 description: Kakaotalk access token
     *               app_id:
     *                 type: string
     *                 description: Application ID
     *             required:
     *               - access_token
     *     responses:
     *       200:
     *         description: Successfully authenticated
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 token:
     *                   type: string
     *                   description: Authentication token
     *                 user:
     *                   type: object
     *                   description: User information
     *       400:
     *         description: Invalid credentials
     *       500:
     *         description: Server error
     */
    this.router.post(
      '/login_with_kakaotalk',
      this.route(this.loginWithKakaotalk)
    )
    
    /**
     * @swagger
     * /auth/login_with_naver:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Login with Naver
     *     description: Authenticate user using Naver credentials
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               access_token:
     *                 type: string
     *                 description: Naver access token
     *             required:
     *               - access_token
     *     responses:
     *       200:
     *         description: Successfully authenticated
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: User information
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: Authentication token
     *                     isNewUser:
     *                       type: boolean
     *                       description: Whether this is a new user
     *       400:
     *         description: Invalid credentials
     *       500:
     *         description: Server error
     */
    this.router.post('/login_with_naver', this.route(this.loginWithNaver))
    
    /**
     * @swagger
     * /auth/login_with_apple:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Login with Apple
     *     description: Authenticate user using Apple credentials
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               id_token:
     *                 type: string
     *                 description: Apple ID token
     *               user:
     *                 type: object
     *                 description: User information provided by Apple
     *             required:
     *               - id_token
     *     responses:
     *       200:
     *         description: Successfully authenticated
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: User information
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: Authentication token
     *                     isNewUser:
     *                       type: boolean
     *                       description: Whether this is a new user
     *       400:
     *         description: Invalid credentials
     *       500:
     *         description: Server error
     */
    this.router.post('/login_with_apple', this.route(this.loginWithApple))
    
    /**
     * @swagger
     * /auth/phone_number/{phone}:
     *   get:
     *     tags:
     *       - Auth
     *     summary: Check if phone number exists
     *     description: Validates if a phone number is already registered
     *     parameters:
     *       - in: path
     *         name: phone
     *         required: true
     *         schema:
     *           type: string
     *         description: Phone number to check
     *     responses:
     *       200:
     *         description: Validation result
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     exists:
     *                       type: boolean
     *                       description: Whether the phone number exists
     *       400:
     *         description: Invalid phone number format
     *       500:
     *         description: Server error
     */
    this.router.get(
      '/phone_number/:phone',
      this.route(this.checkExistPhoneNumber)
    )

    /**
     * @swagger
     * /auth/reset_password:
     *   put:
     *     tags:
     *       - Auth
     *     summary: Reset user password
     *     description: Reset a user's password using a valid token
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               token:
     *                 type: string
     *                 description: Reset password token
     *               new_password:
     *                 type: string
     *                 description: New password to set
     *             required:
     *               - token
     *               - new_password
     *     responses:
     *       200:
     *         description: Password successfully reset
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: Updated user data
     *       400:
     *         description: Invalid token or password
     *       500:
     *         description: Server error
     */
    this.router.put('/reset_password', this.route(this.resetPassword))

    /**
     * @swagger
     * /auth/employee_login_otp:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Send OTP for employee login
     *     description: Sends a one-time password to employee's phone for login verification
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               phone:
     *                 type: string
     *                 description: Employee phone number
     *             required:
     *               - phone
     *     responses:
     *       200:
     *         description: OTP sent successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     msg:
     *                       type: string
     *                       example: "Done"
     *       400:
     *         description: Invalid phone number
     *       500:
     *         description: Server error
     */
    this.router.post('/employee_login_otp', this.route(this.sendOtp))

    /**
     * @swagger
     * /auth/verify:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Verify employee OTP
     *     description: Validates the OTP sent to employee's phone
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               phone:
     *                 type: string
     *                 description: Employee phone number
     *               otp:
     *                 type: string
     *                 description: One-time password to verify
     *             required:
     *               - phone
     *               - otp
     *     responses:
     *       200:
     *         description: OTP verified successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   description: Employee data
     *                 meta:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       description: JWT token for authentication
     *       400:
     *         description: Invalid OTP
     *       401:
     *         description: OTP verification failed
     *       500:
     *         description: Server error
     */
    this.router.post('/verify', this.route(this.verifyOtp))

    /**
     * @swagger
     * /auth/send_otp:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Send OTP to user
     *     description: Sends a one-time password to user's phone for verification
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               phone:
     *                 type: string
     *                 description: User phone number
     *             required:
     *               - phone
     *     responses:
     *       200:
     *         description: OTP sent successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     msg:
     *                       type: string
     *                       example: "Done"
     *       400:
     *         description: Invalid phone number
     *       500:
     *         description: Server error
     */
    this.router.post('/send_otp', this.route(this.sendOtpUser))

    /**
     * @swagger
     * /auth/user/verify:
     *   post:
     *     tags:
     *       - Auth
     *     summary: Verify user OTP
     *     description: Validates the OTP sent to user's phone
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               phone:
     *                 type: string
     *                 description: User phone number
     *               otp:
     *                 type: string
     *                 description: One-time password to verify
     *             required:
     *               - phone
     *               - otp
     *     responses:
     *       200:
     *         description: OTP verified successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 data:
     *                   type: object
     *                   properties:
     *                     msg:
     *                       type: string
     *                       example: "Done"
     *       400:
     *         description: Invalid OTP
     *       401:
     *         description: OTP verification failed
     *       500:
     *         description: Server error
     */
    this.router.post('/user/verify', this.route(this.verifyOtpUser))
  }
  async getGeoip(req: Request, res: Response) {
    const ipAddr: any =
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress
    req.params.ip = ipAddr
    await this.validateJSON(req.params, {
      type: 'object',
      properties: {
        ip: {
          type: 'string',
        },
      },
      required: ['ip'],
    })
    const result = await geoip.lookup(req.params.ip)
    this.onSuccess(res, { result: result.country })
  }

  async checkExistPhoneNumber(req: Request, res: Response) {
    const phone = await userController.checkExistPhoneNumber(req.params)
    this.onSuccess(res, phone)
  }
  async loginWithApple(req: Request, res: Response) {
    const dataObtained: any = await userController.checkLoginWithApple(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = 'USER'
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
      isNewUser: dataObtained.isNewUser,
    })
  }
  async loginWithNaver(req: Request, res: Response) {
    const dataObtained = await userController.loginWithNaver(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = 'USER'
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
      isNewUser: dataObtained.isNewUser,
    })
  }
  async loginWithKakaotalk(req: Request, res: Response) {
    const dataObtained = await userController.checkLoginWithKakaotalk(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = 'USER'
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
      isNewUser: dataObtained.isNewUser,
    })
  }
  async adminGetPassword(req: Request, res: Response) {
    const token = await tokenService.decodeToken(req.body.token)
    const result = await employeeController.getPassword({
      employee_id: token.payload.employee_id,
      old_password: token.payload.password_md5,
      new_password: req.body.new_password,
    })
    this.onSuccess(res, result)
  }
  async AdminForgetPassword(req: Request, res: Response) {
    const token = await tokenService.getAdminTokenForForgetPassWord(
      req.body.email
    )
    let url_get_password_replace = token.replace(/\./g, '@hitek')
    const url_get_password = URL_PAGE + url_get_password_replace
    const transporter = nodemailer.createTransport(process.env.SMTP_MAIL)
    const mailOptions = {
      from: process.env.EMAIL_USERNAME,
      to: req.body.email,
      subject: 'Ecommerce reset password',
      text: `hello,${req.body.email}`,
      html: `<p>We heard that you lost your password. Sorry about that!</p></br>
                    <p>But don’t worry! You can use the following link to reset your password: <a href="${url_get_password}" style="color:red;">${url_get_password}</a></p></br>
                    <p>If you don’t use this link within 30 minutes, it will expire</p></br>
                    <p>Thank You</p>`,
    }
    transporter.sendMail(mailOptions)
    this.onSuccess(res, { url_get_password: url_get_password })
  }
  async getPassword(req: Request, res: Response) {
    const token = await tokenService.decodeToken(req.body.token)
    const result = await userController.getPassword({
      user_id: token.payload.user_id,
      old_password: token.payload.password_md5,
      new_password: req.body.new_password,
    })
    this.onSuccess(res, result)
  }
  async forgetPassword(req: Request, res: Response) {
    tokenService
      .getUserTokenForForgetPassWord(req.body.email)
      .then((token) => {
        console.log('29148 token nen ene:', token)
        if (token && typeof token === 'string') {
          let url_get_password_replace = token.replace(/\./g, '@hitek')
          const url_get_password = URL_PAGE + url_get_password_replace
          const transporter = nodemailer.createTransport(process.env.SMTP_MAIL)
          const mailOptions = {
            from: process.env.EMAIL_USERNAME,
            to: req.body.email,
            subject: '부산달리기M reset password',
            text: `Hello,${req.body.email}`,
            html: `<p>We heard that you lost your password. Sorry about that!</p></br>
                      <p>But don’t worry! You can use the following link to reset your password: <a href="${url_get_password}" style="color:red;">${url_get_password}</a></p></br>
                      <p>If you don’t use this link within 30 minutes, it will expire</p></br>
                      <p>Thank You</p>`,
          }
          transporter.sendMail(mailOptions)
          this.onSuccess(res, { url_get_password: url_get_password })
        } else {
          throw errorService.database.queryFail(
            'User not found to generate jwt token'
          )
        }
      })
      .catch((err) => {
        console.log('29148 forgetPassword error:', err)
        this.onError(res, err)
      })
  }
  async loginWithFacebook(req: Request, res: Response) {
    const dataObtained = await userController.checkLoginWithFacebook(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = ROLE.USER
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
      isNewUser: dataObtained.isNewUser,
    })
  }
  async loginWithGoogle(req: Request, res: Response) {
    const dataObtained = await userController.checkLoginWithGoogle(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = ROLE.USER
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
      isNewUser: dataObtained.isNewUser,
    })
  }
  // async loginWithPhoneNumber(req: Request, res: Response) {
  //     const dataObtained = await userController.checkLoginWithPhoneNumber(req.body)
  //     if (dataObtained.dataValues) {
  //         dataObtained.dataValues.role = "USER";
  //     }
  //     const token = await tokenService.getUserToken(dataObtained.id)
  //     const hasService = await userController.checkHasService(dataObtained.id);
  //     this.onSuccess(res, dataObtained, { hasService, token, isNewUser: dataObtained.isNewUser })
  // }
  async employeeLogin(req: Request, res: Response) {
    const dataObtained = await employeeController.checkLogin(req.body)
    const token = await tokenService.getEmployeeToken(dataObtained.id)
    this.onSuccess(res, dataObtained, { token })
  }
  async registerUser(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        username: {
          type: 'string',
        },
        password: {
          type: 'string',
        },
        nickname: {
          type: 'string',
        },
        phone: {
          type: 'string',
        },
        secret_answer: {
          type: 'string',
        },
      },
      required: ['username', 'password', 'nickname', 'phone'],
    })
    if (req.body.fullname == undefined) {
      req.body.fullname = ''
    }
    req.body.login_type = LOGIN_TYPE.IN_APP
    const result = await userController.createWithReferral(req.body)
    this.onSuccess(res, result)
  }
  async login(req: Request, res: Response) {
    const dataObtained = await userController.checkLogin(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = ROLE.USER
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
    })
  }

  async loginBiz(req: Request, res: Response) {
    const dataObtained = await userController.checkLoginBiz(req.body)
    if (dataObtained.dataValues) {
      dataObtained.dataValues.role = ROLE.USER
    }
    const token = await tokenService.getUserToken(dataObtained.id)
    this.onSuccess(res, dataObtained, {
      token,
    })
  }
  async resetPassword(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      required: ['token', 'new_password'],
    })
    const { token, new_password } = req.body
    const result = await userService.resetPassword(token, new_password)
    this.onSuccess(res, result)
  }

  async sendOtp(req: Request, res: Response){
    await this.validateJSON(req.body, {
      type: 'object',
      required: ['phone'],
    })
    const {phone} = req.body
    const admin = await employeeController.findAdminByPhone(phone)
    const result = await employeeController.sendOtp(admin.phone)
    this.onSuccess(res, {msg : "Done"})
  }
  async sendOtpUser(req: Request, res: Response){
    await this.validateJSON(req.body, {
      type: 'object',
      required: ['phone'],
    })
    const {phone} = req.body
    const result = await userController.sendOtp(phone)
    this.onSuccess(res, {msg : "Done"})
  }

  async verifyOtp(req: Request, res: Response){
    await this.validateJSON(req.body, {
      type: 'object',
      required: ['phone','otp'],
    })
    const {phone , otp} = req.body
    await employeeController.verifyOtp(phone,otp)
    const admin = await employeeController.findAdminByPhone(phone)
    const token = await tokenService.getEmployeeToken(admin.id)
    this.onSuccess(res, admin, { token })
  }

  async verifyOtpUser(req: Request, res: Response){
    await this.validateJSON(req.body, {
      type: 'object',
      required: ['phone','otp'],
    })
    const {phone , otp} = req.body
     await userController.verifyOtp(phone,otp)
    this.onSuccess(res, {msg : "Done"})
  }

  registerMiddlewares(): any[] {
    return [limitRequest(10 * 1000,2 , 'iP 1개가 회원가입도 10초안에 2번이상 새로운아이디 가입할수있으면 안됩니다.!')];
  }
}
