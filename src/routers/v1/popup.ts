import { <PERSON><PERSON><PERSON>outer } from '../crud'
import { Request, Response } from '../base'
import { popupController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'
export default class FaqR<PERSON>er extends CrudRouter<typeof popupController> {
    constructor() {
        super(popupController)

    }

    customRouting() {
        this.router.put('/order-item/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
}