import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { Request, Response } from "../base";
import { boardController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";

export default class BoardRouter extends CrudRouter<typeof boardController> {
  constructor() {
    super(boardController);
  }
  /**
   * @swagger
   * components:
   *   schemas:
   *     Board:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           format: uuid
   *           example: "2b944122-7cd7-4f3e-99c2-8888b2b9f987"
   *         name:
   *           type: string
   *           example: "Find a Technician"
   *         created_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T10:00:00.000Z"
   *         updated_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T10:00:00.000Z"
   */
  customRouting() {
    /**
     * @swagger
     * /board:
     *   get:
     *     summary: Get all boards
     *     tags: [Board]
     *     responses:
     *       200:
     *         description: List of boards
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 $ref: '#/components/schemas/Board'
     *
     *   post:
     *     summary: Create a new board
     *     tags: [Board]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/Board'
     *     responses:
     *       201:
     *         description: Board created
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Board'
     *
     * /board/{id}:
     *   get:
     *     summary: Get a board by ID
     *     tags: [Board]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Board ID
     *     responses:
     *       200:
     *         description: Found board
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Board'
     *       404:
     *         description: Board not found
     *
     *   put:
     *     summary: Update a board
     *     tags: [Board]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/Board'
     *     responses:
     *       200:
     *         description: Board updated
     *
     *   delete:
     *     summary: Delete a board (soft delete)
     *     tags: [Board]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       204:
     *         description: Board deleted
     */
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
