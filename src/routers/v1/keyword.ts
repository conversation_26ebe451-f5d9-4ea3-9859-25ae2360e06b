import {<PERSON>rudRouter} from '../crud'
import {Request, Response} from '../base'
import {keywordController} from '@/controllers'
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    superAdminTypeMiddleware,
    adminTypeMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
import multer = require('multer');

export default class KeywordRouter extends CrudRouter<typeof keywordController> {
    constructor() {
        super(keywordController)
    }

    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, 'image/')
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        const upload = multer({storage: storage})
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
        this.router.put('/order-keyword/:id', this.createMiddlewares(), this.route(this.dragDropItem))
        this.router.post(
            '/import_excel',
            this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.get(
            '/download_excel',
            this.route(this.downloadExcel)
        )
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }

    async importExcel(req: Request, res: Response) {
        await this.validateJSON(req.query, {
            type: 'object',
            properties: {
                keyword_category_id: {type: 'string'},
            },
            required: ['keyword_category_id'],
        })
        req.body.url = 'image/' + req.file.filename

        const result = await this.controller.importExcel(req.body , req.query)
        this.onSuccess(res, result)
    }

    async downloadExcel(req: Request, res: Response) {
        const result = await this.controller.downloadExcel()
        setTimeout(() => {
            this.onSuccess(res, result)
        }, 1000 * 3)
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
}
