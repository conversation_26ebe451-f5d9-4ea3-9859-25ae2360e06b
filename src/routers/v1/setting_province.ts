/**
 * @swagger
 * tags:
 *   name: SettingProvince
 *   description: Setting province management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     SettingProvince:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the province
 *         name:
 *           type: string
 *           description: Province name
 *         status:
 *           type: boolean
 *           description: Province status
 *         region:
 *           type: string
 *           description: Region the province belongs to
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import {CrudRouter} from '../crud'
import {Request, Response} from '../base'
import {settingProvinceController} from '@/controllers'
import {authInfoMiddleware, queryMiddleware, superAdminTypeMiddleware} from '@/middlewares'
import multer = require('multer');

export default class SettingProvinceRouter extends CrudRouter<typeof settingProvinceController> {
    constructor() {
        super(settingProvinceController)
    }

    /**
     * @swagger
     * /setting-province:
     *   get:
     *     summary: Get all provinces
     *     tags: [SettingProvince]
     *     parameters:
     *       - $ref: '#/components/parameters/PageParam'
     *       - $ref: '#/components/parameters/PageSizeParam'
     *       - $ref: '#/components/parameters/FilterParam'
     *       - $ref: '#/components/parameters/SortParam'
     *     responses:
     *       200:
     *         description: List of provinces
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 data:
     *                   type: array
     *                   items:
     *                     $ref: '#/components/schemas/SettingProvince'
     *
     * /setting-province/import_excel:
     *   post:
     *     summary: Import provinces from Excel file
     *     tags: [SettingProvince]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               file:
     *                 type: string
     *                 format: binary
     *     responses:
     *       200:
     *         description: Provinces imported successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *
     * /setting-province/order-item/{id}:
     *   put:
     *     summary: Update province order
     *     tags: [SettingProvince]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               order:
     *                 type: integer
     *     responses:
     *       200:
     *         description: Province order updated successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, 'image/')
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        const upload = multer({storage: storage})

        this.router.post(
            '/import_excel',
            this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.put('/order-item/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }

    async importExcel(req: Request, res: Response) {
        req.body.url = 'image/' + req.file.filename

        const result = await this.controller.importExcel(req.body)
        this.onSuccess(res, result)
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run() , superAdminTypeMiddleware.run()]
    }
}