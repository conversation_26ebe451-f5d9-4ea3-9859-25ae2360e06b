import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { siteCategoryController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'
export default class SiteControllerRouter extends CrudRouter<typeof siteCategoryController> {
    constructor() {
        super(siteCategoryController)

    }

    customRouting() {
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
        this.router.put('/multiple/data', this.route(this.updateSiteCategory))
        this.router.put('/order-site-category/:id', this.createMiddlewares(), this.route(this.dragDropItem))

    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }

    async updateSiteCategory(req: Request, res: Response) {
        const { list_data } = req.body
        const result = await this.controller.updateSiteCategory({ list_data })
        this.onSuccess(res, result)
    }

    async delete(req: Request, res: Response) {
        const { id } = req.params
        const {new_group} = req.query
        const result = await this.controller.delete({
            new_group
        },{
            filter: { id },
        })
        this.onSuccess(res, result)
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}