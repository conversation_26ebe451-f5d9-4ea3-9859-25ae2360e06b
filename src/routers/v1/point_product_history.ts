import {<PERSON><PERSON><PERSON>out<PERSON>} from '../crud'
import {Request, Response} from '../base'
import {
    authInfoMiddleware,
    queryMiddleware,
    adminTypeMiddleware,
} from '@/middlewares'
import {pointProductHistoryController} from '@/controllers'

export default class PointProductHistoryRouter extends <PERSON><PERSON><PERSON>outer<
    typeof pointProductHistoryController
> {
    constructor() {
        super(pointProductHistoryController)
    }

    customRouting() {
        this.router.put(
            '/confirm/:id',
            this.updateMiddlewares(),
            this.route(this.confirm)
        )
        this.router.put(
            '/send/:id',
            this.updateMiddlewares(),
            this.route(this.sent)
        ),
            this.router.get(
                '/download_excel',
                this.getListMiddlewares(),
                this.route(this.downloadExcel)
            ),
            this.router.get('/count/total', this.getListMiddlewares(), this.route(this.count))
    }

    defaultRouting() {
        this.router.get('/', this.getListMiddlewares(), this.route(this.getList))
    }

    async count(req: Request, res: Response) {
        const result = await this.controller.count(req.queryInfo.filter)
        this.onSuccess(res, result)
    }

    async confirm(req: Request, res: Response) {
        const {id} = req.params
        const result = await this.controller.confirm(id)
        this.onSuccess(res, result)
    }

    async sent(req: Request, res: Response) {
        const {id} = req.params
        const result = await this.controller.sent(id)
        this.onSuccess(res, result)
    }

    async downloadExcel(req: Request, res: Response) {
        const result = await this.controller.downloadExcel(req.queryInfo)
        setTimeout(() => {
            this.onSuccess(res, result)
        }, 1000 * 1)
    }

    getListMiddlewares(): any[] {
        return [
            authInfoMiddleware.run(),
            adminTypeMiddleware.run(),
            queryMiddleware.run(),
        ]
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
}
