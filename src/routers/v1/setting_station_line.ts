/**
 * @swagger
 * tags:
 *   name: SettingStationLine
 *   description: Setting station line management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     SettingStationLine:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the station line
 *         name:
 *           type: string
 *           description: Line name
 *         station_id:
 *           type: string
 *           description: ID of the associated station
 *         order:
 *           type: integer
 *           description: Display order of the line
 *         status:
 *           type: boolean
 *           description: Line status
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import {CrudRouter} from '../crud'
import {Request, Response} from '../base'
import {settingStationLineController} from '@/controllers'
import {authInfoMiddleware, queryMiddleware} from '@/middlewares'
import multer = require('multer');

export default class SettingStationLineRouter extends CrudRouter<typeof settingStationLineController> {
    constructor() {
        super(settingStationLineController)
    }

    /**
     * @swagger
     * /setting-station-line:
     *   get:
     *     summary: Get all station lines
     *     tags: [SettingStationLine]
     *     parameters:
     *       - $ref: '#/components/parameters/PageParam'
     *       - $ref: '#/components/parameters/PageSizeParam'
     *       - $ref: '#/components/parameters/FilterParam'
     *       - $ref: '#/components/parameters/SortParam'
     *     responses:
     *       200:
     *         description: List of station lines
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 data:
     *                   type: array
     *                   items:
     *                     $ref: '#/components/schemas/SettingStationLine'
     *
     * /setting-station-line/import_excel:
     *   post:
     *     summary: Import station lines from Excel file
     *     tags: [SettingStationLine]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               file:
     *                 type: string
     *                 format: binary
     *     responses:
     *       200:
     *         description: Station lines imported successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *
     * /setting-station-line/order-item/{id}:
     *   put:
     *     summary: Update station line order
     *     tags: [SettingStationLine]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               order:
     *                 type: integer
     *     responses:
     *       200:
     *         description: Station line order updated successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, 'image/')
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        const upload = multer({storage: storage})
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))

        this.router.post(
            '/import_excel',
            this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.put('/order-item/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }
    async importExcel(req: Request, res: Response) {
        req.body.url = 'image/' + req.file.filename

        const result = await this.controller.importExcel(req.body)
        this.onSuccess(res, result)
    }
    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}