import { <PERSON><PERSON><PERSON>outer } from '../crud'
import {
  queryMiddleware,
  adminTypeMiddleware,
  authInfoMiddleware,
} from '@/middlewares'
import { adminSettingController } from '@/controllers'
import { Request, Response } from '../base'

export default class AdminSetting extends <PERSON><PERSON><PERSON>outer<
  typeof adminSettingController
> {
  constructor() {
    super(adminSettingController)
  }

  customRouting() {
    this.router.get('/', this.getItemMiddlewares(), this.route(this.getSetting))
    this.router.put(
      '',
      this.updateMiddlewares(),
      this.route(this.updateSetting)
    )
    this.router.put(
      '/mentor_status',
      this.updateMiddlewares(),
      this.route(this.updateMentorStatus)
    )
    this.router.put(
      '/report_limit',
      this.updateMiddlewares(),
      this.route(this.updateReportLimit)
    )
  }

  defaultRouting() {}

  async getSetting(req: Request, res: Response) {
    const result = await this.controller.getAdminSetting()
    this.onSuccess(res, result)
  }
  async updateSetting(req: Request, res: Response) {
    const result = await this.controller.updateAdminSetting()
    this.onSuccess(res, result)
  }

  async updateMentorStatus(req: Request, res: Response) {
    const result = await this.controller.updateMentorStatus()
    this.onSuccess(res, result)
  }

  async updateReportLimit(req: Request, res: Response) {
    const result = await this.controller.updateReportLimit(req.body)
    this.onSuccess(res, result)
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
