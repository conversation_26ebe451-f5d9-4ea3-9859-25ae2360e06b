import {<PERSON><PERSON><PERSON>out<PERSON>} from '../crud';
import {Request, Response} from '../base';
import {realEstateController} from '@/controllers';
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    superAdminTypeMiddleware,
    adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class RealEstateRouter extends CrudRouter<typeof realEstateController> {
    constructor() {
        super(realEstateController);
    }


    customRouting() {
        this.router.post('/call/:id', this.route(this.call))
    }

    async create(req: Request, res: Response) {
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id;
        }

        await this.validateJSON(req.body, {
            required: [
                'images',
                'title',
                'deposit_price',
                'description',
                'management_fee',
                'land_area',
                'contact_phone',
                'latitude',
                'longitude',
                'address',
                'tag_ids',
                'category_id',
            ],
        });
        const result = await this.controller.create(req.body);
        this.onSuccess(res, result);
    }

    async call(req: Request, res: Response) {
        const {id} = req.params
        const result = await this.controller.call({
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()];
    }
}
