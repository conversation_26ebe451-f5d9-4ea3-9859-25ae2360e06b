import { <PERSON>rudRouter } from '../crud'
import { Request, Response } from '../base'
import { reservationController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
import { RESERVATION_STATUS } from '@/const'
import { ICrudOption } from '@/services'
export default class ReservationRouter extends CrudRouter<
  typeof reservationController
> {
  constructor() {
    super(reservationController)
  }

  customRouting() {
    this.router.post(
      '/reserve',
      this.createMiddlewares(),
      this.route(this.createReserve)
    )
    this.router.put(
      '/reserve/:id',
      this.updateMiddlewares(),
      this.route(this.updateReserve)
    )
    this.router.put(
      '/reserve/approve/:id',
      this.updateMiddlewares(),
      this.route(this.approveReserve)
    )
    this.router.put(
      '/reserve/reject/:id',
      this.updateMiddlewares(),
      this.route(this.rejectReserve)
    )
    this.router.put(
      '/reserve/cancel/:id',
      this.updateMiddlewares(),
      this.route(this.cancelReserve)
    )
    this.router.put(
      '/reserve/restore/:id',
      this.updateMiddlewares(),
      this.route(this.restoreReserve)
    )
    this.router.delete(
      '/reserve/delete/:id',
      this.deleteMiddlewares(),
      this.route(this.deleteReserve)
    )
    this.router.delete(
      '/delete_by_type/:state',
      this.deleteMiddlewares(),
      this.route(this.deleteByTypeReserve)
    )
    this.router.get(
      '/state',
      this.getItemMiddlewares(),
      this.route(this.countByState)
    )
    this.router.get(
      '/shop_ranking',
      this.getListByAdminMiddlewares(),
      this.route(this.getShopRanking)
    )
    this.router.get(
      '/feedback/shop/:id',
      this.createMiddlewares(),
      this.route(this.feedbackAvailable)
    )
  }

  async createReserve(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    await this.validateJSON(req.body, {
      required: [
        'date',
        'paymentMethod',
        'contact',
        'memo',
        'prices',
        'shop_id',
        'user_id',
        'seller_id',
      ],
    })
    const result = await this.controller.createReserve(req.body)
    this.onSuccess(res, result)
  }

  async updateReserve(req: Request, res: Response) {
    const { id } = req.params
    delete req.body.state
    delete req.body.reason
    req.body.state = RESERVATION_STATUS.PENDING
    const result = await this.controller.update(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async approveReserve(req: Request, res: Response) {
    req.body.caller_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const body: any = {
      state: RESERVATION_STATUS.APPROVED,
      reason: null,
    }
    const result = await this.controller.updateReserveStatus(body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async rejectReserve(req: Request, res: Response) {
    req.body.caller_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const body: any = {
      state: RESERVATION_STATUS.REJECTED,
      reason: req.body.reason,
    }
    const result = await this.controller.updateReserveStatus(body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async cancelReserve(req: Request, res: Response) {
    req.body.caller_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const body: any = {
      state: RESERVATION_STATUS.CANCELLED,
      reason: req.body.reason,
    }
    req.queryInfo = {
      filter: {
        id,
      },
    }
    const item = await this.controller.getItem(req.queryInfo)
    if (item.state === RESERVATION_STATUS.APPROVED) {
      body.state = RESERVATION_STATUS.PENDING
    }
    const result = await this.controller.updateReserveStatus(body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async restoreReserve(req: Request, res: Response) {
    req.body.caller_id = req.tokenInfo.payload.user_id
    const { id } = req.params

    const body: any = {
      state: RESERVATION_STATUS.PENDING,
      reason: null,
    }
    const result = await this.controller.updateReserveStatus(body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async deleteReserve(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id

    const { id } = req.params

    const result = await this.controller.deleteReserve(req.body, {
      filter: { id },
    })

    this.onSuccess(res, result)
  }

  async deleteByTypeReserve(req: Request, res: Response) {
    const { state } = req.params
    req.body.caller_id = req.tokenInfo.payload.user_id
    req.body.state = state
    const result = await this.controller.deleteByTypeReserve(req.body)

    this.onSuccess(res, result)
  }

  async countByState(req: Request, res: Response) {
    const result = await this.controller.countByState(req.queryInfo)
    this.onSuccess(res, result)
  }

  async getShopRanking(req: Request, res: Response) {
    const result = await this.controller.getShopRanking(req.queryInfo)
    this.onSuccessAsList(res, result)
  }

  async feedbackAvailable(req: Request, res: Response) {
    const shop_id = req.params.id
    const { user_id } = req.tokenInfo.payload
    const result = await this.controller.feedbackAvailable({ user_id, shop_id })
    this.onSuccess(res, result)
  }

  async getList(req: Request, res: Response) {
    const {search_value} = req.query
    const result = await this.controller.getList(req.queryInfo,search_value as any)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  getListByAdminMiddlewares(): any[] {
    return [
      authInfoMiddleware.run(),
      adminTypeMiddleware.run(),
      queryMiddleware.run(),
    ]
  }
}
