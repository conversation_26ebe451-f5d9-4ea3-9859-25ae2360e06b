import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { answerQuestionController } from '@/controllers'
import { Request, Response } from '../base'
import {
  authInfoMiddleware,
  queryMiddleware,
  superAdminTypeMiddleware,
} from '@/middlewares'

/**
 * @swagger
 * tags:
 *   name: AnswerQuestions
 *   description: Answer Question operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     AnswerQuestion:
 *       type: object
 *       required:
 *         - question_id
 *         - answer
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated ID of the answer
 *         question_id:
 *           type: string
 *           description: ID of the question being answered
 *         answer:
 *           type: string
 *           description: The answer content
 *         user_id:
 *           type: string
 *           description: ID of the user answering the question
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the answer was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the answer was last updated
 *       example:
 *         id: 123e4567-e89b-12d3-a456-426614174000
 *         question_id: 123e4567-e89b-12d3-a456-426614174001
 *         answer: This is the answer to your question.
 *         user_id: 123e4567-e89b-12d3-a456-426614174002
 *         created_at: 2022-01-01T12:00:00Z
 *         updated_at: 2022-01-01T12:30:00Z
 */

export default class AnswerQuestionRouter extends CrudRouter<
  typeof answerQuestionController
> {
  constructor() {
    super(answerQuestionController)
  }

  /**
   * @swagger
   * /answer_question:
   *   post:
   *     summary: Create a new answer to a question
   *     tags: [AnswerQuestions]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - question_id
   *               - answer
   *             properties:
   *               question_id:
   *                 type: string
   *                 description: ID of the question being answered
   *               answer:
   *                 type: string
   *                 description: Content of the answer
   *     responses:
   *       201:
   *         description: Answer created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AnswerQuestion'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async create(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const result = await this.controller.create(req.body)
    this.onSuccess(res, result)
  }

  /**
   * @swagger
   * /answer_question:
   *   get:
   *     summary: Get a list of answers
   *     tags: [AnswerQuestions]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - $ref: '#/components/parameters/SearchParam'
   *       - $ref: '#/components/parameters/IncludeParam'
   *     responses:
   *       200:
   *         description: List of answers
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     objects:
   *                       type: object
   *                       properties:
   *                         rows:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/AnswerQuestion'
   *                         count:
   *                           type: integer
   *                 pagination:
   *                   type: object
   *                   properties:
   *                     current_page:
   *                       type: integer
   *                     next_page:
   *                       type: integer
   *                     prev_page:
   *                       type: integer
   *                     limit:
   *                       type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /answer_question/{id}:
   *   get:
   *     summary: Get a single answer by ID
   *     tags: [AnswerQuestions]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The answer ID
   *     responses:
   *       200:
   *         description: The answer details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AnswerQuestion'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /answer_question/{id}:
   *   put:
   *     summary: Update an answer
   *     tags: [AnswerQuestions]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The answer ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               answer:
   *                 type: string
   *                 description: Updated answer content
   *     responses:
   *       200:
   *         description: Answer updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AnswerQuestion'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /answer_question/{id}:
   *   delete:
   *     summary: Delete an answer
   *     tags: [AnswerQuestions]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The answer ID to delete
   *     responses:
   *       200:
   *         description: Answer deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  adminMiddleware(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  getListMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }
}
