import { <PERSON>rud<PERSON>outer } from '../crud'
import { userPaymentHistoryController } from '@/controllers'
import { Request, Response } from '../base'
import * as _ from 'lodash'
import {
  superAdminTypeMiddleware,
  authInfoMiddleware,
  queryMiddleware,
} from '@/middlewares'

export default class EditUserH<PERSON>oryRouter extends CrudRouter<
  typeof userPaymentHistoryController
> {
  constructor() {
    super(userPaymentHistoryController)
  }
}
