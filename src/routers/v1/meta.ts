/**
 * @swagger
 * tags:
 *   name: Meta
 *   description: Website metadata management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Meta:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the meta item
 *         name:
 *           type: string
 *           description: Meta name/key
 *         value:
 *           type: string
 *           description: Meta value
 *         status:
 *           type: boolean
 *           description: Meta status
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import { CrudRouter } from '../crud';
import { Request, Response } from '../base';
import { metaController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * /meta:
 *   get:
 *     summary: Get all meta items
 *     tags: [Meta]
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - $ref: '#/components/parameters/FilterParam'
 *       - $ref: '#/components/parameters/SortParam'
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Meta'
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: number
 *                     page:
 *                       type: number
 *                     pageSize:
 *                       type: number
 *   post:
 *     summary: Create new meta item
 *     tags: [Meta]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               value:
 *                 type: string
 *     responses:
 *       201:
 *         description: Meta item created
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *   put:
 *     summary: Update meta item
 *     tags: [Meta]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               value:
 *                 type: string
 *     responses:
 *       200:
 *         description: Meta item updated
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
export default class MetaRouter extends CrudRouter<typeof metaController> {
  constructor() {
    super(metaController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  /**
   * @swagger
   * /meta:
   *   get:
   *     summary: Get list of meta items
   *     tags: [Meta]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageParam'
   *       - $ref: '#/components/parameters/PageSizeParam'
   *       - $ref: '#/components/parameters/FilterParam'
   *     responses:
   *       200:
   *         description: List of meta items
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /meta/{id}:
   *   get:
   *     summary: Get single meta item
   *     tags: [Meta]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Meta item details
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /meta/{id}:
   *   put:
   *     summary: Update meta item
   *     tags: [Meta]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               value:
   *                 type: string
   *     responses:
   *       200:
   *         description: Meta item updated
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /meta/{id}:
   *   delete:
   *     summary: Delete meta item
   *     tags: [Meta]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Meta item deleted
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /meta:
   *   delete:
   *     summary: Delete all meta items
   *     tags: [Meta]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: All meta items deleted
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /meta:
   *   post:
   *     summary: Create new meta item
   *     tags: [Meta]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               value:
   *                 type: string
   *     responses:
   *       201:
   *         description: Meta item created
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
