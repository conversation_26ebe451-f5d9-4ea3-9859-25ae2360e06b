import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import {navbarController} from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * tags:
 *   name: Navbar
 *   description: Navigation bar items management
 */
export default class NavBarRouter extends CrudRouter<typeof navbarController> {
  constructor() {
    super(navbarController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    this.router.put('/order-nav/:id', this.createMiddlewares(), this.route(this.dragDropItem))
  }

  /**
   * @swagger
   * /navbar/order-nav/{id}:
   *   put:
   *     summary: Reorder navbar items
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               order:
   *                 type: integer
   *                 description: New position in the navbar
   *     responses:
   *       200:
   *         description: Navbar item reordered successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  async dragDropItem(req: Request, res: Response) {
    const {id} = req.params;
    const result = await this.controller.dragDropItem(id, req.body);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /navbar:
   *   get:
   *     summary: Get list of navbar items
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageParam'
   *       - $ref: '#/components/parameters/PageSizeParam'
   *       - $ref: '#/components/parameters/FilterParam'
   *     responses:
   *       200:
   *         description: List of navbar items
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /navbar/{id}:
   *   get:
   *     summary: Get single navbar item
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Navbar item details
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /navbar/{id}:
   *   put:
   *     summary: Update navbar item
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *               url:
   *                 type: string
   *               order:
   *                 type: integer
   *     responses:
   *       200:
   *         description: Navbar item updated
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /navbar/{id}:
   *   delete:
   *     summary: Delete navbar item
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Navbar item deleted
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /navbar:
   *   delete:
   *     summary: Delete all navbar items
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: All navbar items deleted
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /navbar:
   *   post:
   *     summary: Create new navbar item
   *     tags: [Navbar]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *               url:
   *                 type: string
   *               order:
   *                 type: integer
   *     responses:
   *       200:
   *         description: Navbar item created
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
