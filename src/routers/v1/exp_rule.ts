import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { Request, Response } from "../base";
import { expRuleController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";

export default class ExpRuleRouter extends CrudRouter<
  typeof expRuleController
> {
  constructor() {
    super(expRuleController);
  }
  /**
   * @swagger
   * components:
   *   schemas:
   *     ExpRule:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           format: uuid
   *           example: "1a2b3c4d-5678-90ef-abcd-1234567890ef"
   *         action_type:
   *           type: string
   *           description: Action type (must be unique)
   *           example: "write_comment"
   *         exp:
   *           type: integer
   *           description: Amount of experience points
   *           example: 10
   *         point:
   *           type: integer
   *           description: Amount of reward points
   *           example: 5
   *         created_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T10:00:00.000Z"
   *         updated_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T10:00:00.000Z"
   */

  customRouting() {
    /**
     * @swagger
     * /exp_rule:
     *   get:
     *     summary: Get all EXP rules
     *     tags: [ExpRule]
     *     responses:
     *       200:
     *         description: List of EXP rules
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 $ref: '#/components/schemas/ExpRule'
     *
     *   post:
     *     summary: Create a new EXP rule
     *     tags: [ExpRule]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/ExpRule'
     *     responses:
     *       201:
     *         description: EXP rule created
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ExpRule'
     *
     * /exp_rule/{id}:
     *   get:
     *     summary: Get an EXP rule by ID
     *     tags: [ExpRule]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: Found EXP rule
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ExpRule'
     *       404:
     *         description: Not found
     *
     *   put:
     *     summary: Update an EXP rule
     *     tags: [ExpRule]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/ExpRule'
     *     responses:
     *       200:
     *         description: EXP rule updated
     *
     *   delete:
     *     summary: Delete an EXP rule (soft delete)
     *     tags: [ExpRule]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       204:
     *         description: EXP rule deleted
     */
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
