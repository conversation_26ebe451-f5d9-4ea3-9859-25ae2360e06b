import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { shortVideoController } from '@/controllers'
import {
  authInfoMiddleware,
  adminTypeMiddleware,
  queryMiddleware,
  optionalInfoMiddleware,
} from '@/middlewares'

export default class ShortVideoRouter extends <PERSON><PERSON><PERSON>outer<
  typeof shortVideoController
> {
  constructor() {
    super(shortVideoController)
  }

  customRouting() {
    this.router.put(
      '/index/:id',
      this.createMiddlewares(),
      this.route(this.settingIndex)
    )
    this.router.put(
      '/like/:id',
      this.updateMiddlewares(),
      this.route(this.likeShortVideo)
    )
    this.router.put(
      '/unlike/:id',
      this.updateMiddlewares(),
      this.route(this.unlikeShortVideo)
    )
  }

  async settingIndex(req: Request, res: Response) {
    const {id} = req.params

    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        action: {
          type: 'string',
        },
      },
      required: ['action'],
    })
    const result = await this.controller.settingIndex(req.body, {filter: {id}})
    this.onSuccess(res, result)
  }

  async getList(req: Request, res: Response) {
    req.params.user_id =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    const result = await this.controller.getList(req.params, req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  async likeShortVideo(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const {id} = req.params
    const result = await this.controller.likeShortVideo(req.body, {
      filter: {id},
    })
    this.onSuccess(res, result)
  }
  async unlikeShortVideo(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const {id} = req.params
    const result = await this.controller.unlikeShortVideo(req.body, {
      filter: {id},
    })
    this.onSuccess(res, result)
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
