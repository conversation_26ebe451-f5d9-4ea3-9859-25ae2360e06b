import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { loyaltyController } from '@/controllers'
import { Request, Response } from '../base'
import { authInfoMiddleware, queryMiddleware } from '@/middlewares'
import * as _ from 'lodash'

export default class Loyalty<PERSON>out<PERSON> extends C<PERSON><PERSON>outer<
  typeof loyaltyController
> {
  constructor() {
    super(loyaltyController)
  }

  customRouting() {
    /**
     * @swagger
     * /loyalty:
     *   post:
     *     tags:
     *       - Loyalty
     *     summary: Create new loyalty voucher
     *     description: Creates a new loyalty voucher for a user
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               name:
     *                 type: string
     *                 description: Name of the voucher
     *               description:
     *                 type: string
     *                 description: Description of the voucher
     *               points:
     *                 type: number
     *                 description: Points required for the voucher
     *     responses:
     *       200:
     *         description: Voucher created successfully
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post('', this.createMiddlewares(), this.route(this.create)),

    /**
     * @swagger
     * /loyalty/use:
     *   post:
     *     tags:
     *       - Loyalty
     *     summary: Use a loyalty voucher
     *     description: Use a loyalty voucher for the authenticated user
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               voucher_id:
     *                 type: string
     *                 description: ID of the voucher to use
     *     responses:
     *       200:
     *         description: Voucher used successfully
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Voucher not found
     *       500:
     *         description: Server error
     */
    this.router.post(
        '/use',
        this.updateMiddlewares(),
        this.route(this.useVoucher)
      ),

    /**
     * @swagger
     * /loyalty/search:
     *   get:
     *     tags:
     *       - Loyalty
     *     summary: Search loyalty vouchers
     *     description: Search and filter loyalty vouchers
     *     parameters:
     *       - in: query
     *         name: search
     *         schema:
     *           type: string
     *         description: Search term to filter vouchers
     *       - in: query
     *         name: page
     *         schema:
     *           type: integer
     *         description: Page number for pagination
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *         description: Number of items per page
     *     responses:
     *       200:
     *         description: List of vouchers retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       name:
     *                         type: string
     *                       points:
     *                         type: number
     *                 meta:
     *                   type: object
     *                   properties:
     *                     total:
     *                       type: number
     *                     page:
     *                       type: number
     *                     limit:
     *                       type: number
     *       500:
     *         description: Server error
     */
    this.router.get(
        '/search',
        this.getListMiddlewares(),
        this.route(this.getSearchList)
      )
  }

  async useVoucher(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const result = await this.controller.useVoucher(req.body)
    this.onSuccess(res, result)
  }

  async getSearchList(req: Request, res: Response) {
    const result = await this.controller.getSearchList(req.query, req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
