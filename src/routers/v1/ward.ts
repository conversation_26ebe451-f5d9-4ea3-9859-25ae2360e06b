/**
 * @swagger
 * tags:
 *   name: Wards
 *   description: Ward (administrative division) management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Ward:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the ward
 *         name:
 *           type: string
 *           description: Ward name
 *         district_id:
 *           type: string
 *           description: ID of the parent district
 *         status:
 *           type: boolean
 *           description: Ward status
 *         order:
 *           type: integer
 *           description: Display order
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /wards:
 *   get:
 *     summary: Get all wards
 *     tags: [Wards]
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - $ref: '#/components/parameters/FilterParam'
 *       - $ref: '#/components/parameters/SortParam'
 *     responses:
 *       200:
 *         description: List of wards
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Ward'
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: number
 *                     page:
 *                       type: number
 *                     pageSize:
 *                       type: number
 *
 * /wards/import_excel:
 *   post:
 *     summary: Import wards from Excel file
 *     tags: [Wards]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Wards imported successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *
 * /wards/order-item/{id}:
 *   put:
 *     summary: Update ward order
 *     tags: [Wards]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               order:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Ward order updated successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */

import { CrudRouter } from '../crud';
import { Request, Response } from '../base';
import { wardController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class WardRouter extends CrudRouter<typeof wardController> {
  constructor() {
    super(wardController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
