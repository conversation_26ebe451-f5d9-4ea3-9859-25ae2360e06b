import { CrudRouter } from '../crud';
import { errorService, tokenService } from '@/services';
import * as express from 'express';
import { Request, Response, BaseRouter } from '../base';
import { statisticController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
export default class StatisticRouter extends BaseRouter {
  router: express.Router;
  constructor() {
    super();
    this.router = express.Router();
    this.router.post(
      '/visitor_pageview',
      this.statisticMiddlewares(),
      this.route(this.statisticVisitorPageView)
    );
    this.router.post(
      '/traffic',
      this.statisticMiddlewares(),
      this.route(this.statisticTraffic)
    );
    this.router.post(
      '/dashboard',
      this.statisticMiddlewares(),
      this.route(this.statisticDashboardChart)
    );
    this.router.post(
      '/period',
      this.statisticMiddlewares(),
      this.route(this.statisticByPeriod)
    );
    // this.router.post(
    //   '/new_member',
    //   this.statisticMiddlewares(),
    //   this.route(this.statisticNewMember)
    // );
    // this.router.post(
    //   '/hourly_visitor',
    //   this.statisticWithPagingMiddlewares(),
    //   this.route(this.statisticHourlyVisitor)
    // );
    // this.router.post(
    //   '/header',
    //   this.statisticMiddlewares(),
    //   this.route(this.statisticDashboard)
    // );
  }
  async statisticDashboardChart(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        type: {
          enum: ['day', 'month'],
        },
        type_of_month: {
          enum: ['12_month', '6_month', '3_month', '1_month'],
        },
        value_of_day: {
          type: 'object',
        },
        area_type: {
          enum: ['total', 'domestic', 'overseas'],
        },
      },
      required: ['type', 'area_type'],
    });
    const result = await statisticController.statisticDashboardChart(req.body);
    this.onSuccess(res, result);
  }
  //   async statisticDashboard(req: Request, res: Response) {
  //     await this.validateJSON(req.body, {
  //       type: 'object',
  //       properties: {
  //         type_statistic: {
  //           enum: ['day', 'month'],
  //         },
  //       },
  //       required: ['from_date', 'to_date'],
  //     });
  //     const result = await statisticController.statisticDashboard(req.body);
  //     this.onSuccess(res, result);
  //   }
  //   async statisticNewMember(req: Request, res: Response) {
  //     await this.validateJSON(req.body, {
  //       type: 'object',
  //       properties: {
  //         type: {
  //           enum: ['day', 'month'],
  //         },
  //         type_of_month: {
  //           enum: ['12_month', '6_month', '3_month', '1_month'],
  //         },
  //         value_of_day: {
  //           type: 'object',
  //         },
  //         area_type: {
  //           enum: ['total', 'domestic', 'overseas'],
  //         },
  //       },
  //       required: ['type', 'area_type'],
  //     });
  //     const result = await statisticController.statisticNewMember(req.body);
  //     this.onSuccess(res, result);
  //   }
  //   async statisticHourlyVisitor(req: Request, res: Response) {
  //     await this.validateJSON(req.body, {
  //       type: 'object',
  //       properties: {
  //         type: {
  //           enum: ['day', 'month'],
  //         },
  //         type_of_month: {
  //           enum: ['12_month', '24_month'],
  //         },
  //         value_of_day: {
  //           type: 'object',
  //         },
  //         gender: {
  //           enum: ['MALE', 'FEMALE', 'BOTH'],
  //         },
  //         area_type: {
  //           enum: ['total', 'domestic', 'overseas'],
  //         },
  //       },
  //       required: ['type', 'area_type', 'gender'],
  //     });
  //     const result = await statisticController.statisticHourlyVisitor(
  //       req.body,
  //       req.queryInfo
  //     );
  //     this.onSuccess(res, result);
  //   }
  async statisticByPeriod(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        type: {
          enum: ['day', 'month'],
        },
        type_of_month: {
          enum: ['12_month', '6_month', '3_month', '1_month'],
        },
        value_of_day: {
          type: 'object',
        },
      },
      required: ['type'],
    });
    const result = await statisticController.statisticByPeriod(req.body);
    this.onSuccess(res, result);
  }
  async statisticVisitorPageView(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        type: {
          enum: ['day', 'month'],
        },
        type_of_month: {
          enum: ['12_month', '6_month', '3_month', '1_month'],
        },
        value_of_day: {
          type: 'object',
        },
        area_type: {
          enum: [
            'average_visitor',
            'average_pageview',
            'daily_visitor',
            'daily_pageview',
          ],
        },
      },
      required: ['type', 'area_type'],
    });
    const result = await statisticController.statisticVisitorPageView(req.body);
    this.onSuccess(res, result);
  }

  async statisticTraffic(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        type: {
          enum: ['day', 'month'],
        },
        type_of_month: {
          enum: ['12_month', '6_month', '3_month', '1_month'],
        },
        value_of_day: {
          type: 'object',
        },
      },
      required: ['type'],
    });
    const result = await statisticController.statisticTraffic(req.body);
    this.onSuccess(res, result);
  }

  async statisticVisitor(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        type: {
          enum: ['day', 'month'],
        },
        type_of_month: {
          enum: ['12_month', '6_month', '3_month', '1_month'],
        },
        value_of_day: {
          type: 'object',
        },
        area_type: {
          enum: ['total', 'domestic', 'overseas'],
        },
      },
      required: ['type', 'area_type'],
    });
    const result = await statisticController.statisticVisitor(req.body);
    this.onSuccess(res, result);
  }
  statisticMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  statisticWithPagingMiddlewares(): any[] {
    return [
      authInfoMiddleware.run(),
      adminTypeMiddleware.run(),
      queryMiddleware.run(),
    ];
  }
}
