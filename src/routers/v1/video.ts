/**
 * @swagger
 * tags:
 *   name: Videos
 *   description: Video content management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Video:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the video
 *         title:
 *           type: string
 *           description: Video title
 *         description:
 *           type: string
 *           description: Video description
 *         url:
 *           type: string
 *           description: URL to the video
 *         thumbnail:
 *           type: string
 *           description: URL to video thumbnail
 *         duration:
 *           type: number
 *           description: Video duration in seconds
 *         status:
 *           type: boolean
 *           description: Video status
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import * as express from 'express'
import * as multer from 'multer'
import * as _ from 'lodash'
import * as path from 'path'
import * as fs from 'fs'
import * as ffmpeg from 'fluent-ffmpeg'
import { S3 } from 'aws-sdk'
import { errorService } from '@/services'
import { Request, Response, BaseRouter } from '../base'
import { authInfoMiddleware, adminTypeMiddleware } from '@/middlewares'
import { getVideoDurationInSeconds } from 'get-video-duration'
import { VIDEO_FORMAT } from '@/const'
import { path as ffmpegPath } from '../../../node_modules/@ffmpeg-installer/ffmpeg'
const ffprobePath =
  require('../../../node_modules/@ffprobe-installer/ffprobe').path

const ID = process.env.AWS_ID
const SECRET = process.env.AWS_SECRET_KEY
const BUCKET_NAME = process.env.AWS_BUCKET_NAME

ffmpeg.setFfmpegPath(ffmpegPath)
ffmpeg.setFfprobePath(ffprobePath)

const FILE_VIDEO_PATH = 'video/'
export default class VideoRouter extends BaseRouter {
  router: express.Router

  constructor() {
    super()
    this.router = express.Router()
    const storage = multer.diskStorage({
      destination: FILE_VIDEO_PATH,
      filename: function (req: Request, file: any, cb: any) {
        cb(
          null,
          file.fieldname + '-' + Date.now() + path.extname(file.originalname)
        )
      },
    })
    const upload = multer({
      storage: storage,
      fileFilter(req, file, cb) {
        if (!file.originalname.match(/\.(mp4|MPEG-4|mkv|avi|mov)$/)) {
          return cb(new Error('Please upload a video'))
        }
        cb(undefined, true)
      },
    })

    this.router.post(
      '/upload',
      this.updateVideoMiddlewares(),
      upload.single('video'),
      this.route(this.uploadVideo)
    )

    this.router.get('/:id', this.route(this.getVideo))
  }

  async uploadVideo(req: Request, res: Response) {
    const fileName = req.file.filename
    const originalFileName = fileName.split('.')[0]
    const thumbnailName = Date.now() + 'screenshot.png'
    const duration = await getVideoDurationInSeconds(FILE_VIDEO_PATH + fileName)
    if (duration > 160) {
      await new Promise((resolve, reject) => {
        fs.unlink(`${FILE_VIDEO_PATH}${fileName}`, (err) => {
          err ? reject(err) : resolve('')
        })
      })
      throw errorService.router.requestDataInvalid(
        'Maximum of video duration is 160s'
      )
    }

    let playlistContent: string = `#EXTM3U\n#EXT-X-VERSION:3\n`

    const defaultResolution = {
      output: '360p.m3u8',
      videoBitrate: '600',
      audioBitrate: '80',
      size: '640x360',
    }

    const multipleResolutionHorizontal = [
      {
        output: '480p.m3u8',
        videoBitrate: '1200',
        audioBitrate: '98',
        size: '854x480',
      },
      {
        output: '720p.m3u8',
        videoBitrate: '2400',
        audioBitrate: '128',
        size: '1280x720',
      },
      {
        output: '1080p.m3u8',
        videoBitrate: '5000',
        audioBitrate: '192',
        size: '1920x1080',
      },
    ]

    const multipleResolutionVertical = [
      {
        output: '480p.m3u8',
        videoBitrate: '1200',
        audioBitrate: '98',
        size: '608x1080',
      },
      {
        output: '720p.m3u8',
        videoBitrate: '2400',
        audioBitrate: '128',
        size: '810x1440',
      },
    ]

    let multipleResolution: Array<any> = []
    let resolution = VIDEO_FORMAT.HORIZONTAL

    try {
      await new Promise((resolve, reject) => {
        ffmpeg.ffprobe(
          `${FILE_VIDEO_PATH}${fileName}`,
          function (err, metadata) {
            if (err) {
              reject(err)
            } else {
              let width: number
              let height: number

              for (const resolution of metadata.streams) {
                width = resolution.width
                height = resolution.height
                if (width && height) {
                  break
                }
              }
              if (width > height) {
                multipleResolution = multipleResolutionHorizontal.filter(
                  (item: any) => {
                    const widthItem = item.size.split('x')[0]
                    return parseInt(widthItem) <= width
                  }
                )
              } else {
                multipleResolution = multipleResolutionVertical.filter(
                  (item: any) => {
                    const heightItem = item.size.split('x').pop()
                    return parseInt(heightItem) <= height
                  }
                )
                defaultResolution.size = '480x854'
                resolution = VIDEO_FORMAT.VERTICAL
              }

              resolve('')
            }
          }
        )
      })

      await Promise.all([
        new Promise((resolve, reject) => {
          ffmpeg(FILE_VIDEO_PATH + fileName)
            .output(FILE_VIDEO_PATH + thumbnailName)
            .noAudio()
            .seek('0:00')
            .outputOptions('-frames', '1')
            .on('error', reject)
            .on('end', resolve)
            .run()
        }),
        await new Promise((resolve, reject) => {
          ffmpeg(FILE_VIDEO_PATH + fileName, { timeout: 432000 })
            .addOptions([
              '-profile:v baseline',
              '-level 3.0',
              '-start_number 0',
              '-hls_time 4',
              '-hls_playlist_type vod',
              '-hls_flags periodic_rekey',
              '-hls_list_size 0',
              '-f hls',
              '-threads 0',
            ])
            .withVideoBitrate(defaultResolution.videoBitrate)
            .withAudioBitrate(defaultResolution.audioBitrate)
            .withAudioChannels(2)
            .withSize(defaultResolution.size)
            .withVideoCodec('libx264')
            .withAudioCodec('aac')
            .withAudioChannels(2)
            .on('error', reject)
            .on('end', async () => {
              playlistContent =
                playlistContent +
                `#EXT-X-STREAM-INF:BANDWIDTH=${
                  parseInt(defaultResolution.videoBitrate, 10) * 1024
                },RESOLUTION=${
                  defaultResolution.size
                },CODECS="avc1.42c01e,mp4a.40.2"\n${originalFileName}-${
                  defaultResolution.output
                }\n`
              fs.writeFileSync(
                `${FILE_VIDEO_PATH}${originalFileName}-playlist.m3u8`,
                playlistContent,
                'utf8'
              )
              resolve('')
            })
            .output(
              `${FILE_VIDEO_PATH}${originalFileName}-${defaultResolution.output}`,
              {
                end: false,
              }
            )
            .run()
        }),
      ])

      const removeFile = async () => {
        await Promise.all([
          new Promise((resolve, reject) => {
            fs.unlink(`${FILE_VIDEO_PATH}${fileName}`, (err) => {
              err ? reject(err) : resolve('')
            })
          }),
          new Promise((resolve, reject) => {
            fs.unlink(`${FILE_VIDEO_PATH}${thumbnailName}`, (err) =>
              err ? reject(err) : resolve('')
            )
          }),
        ])
      }

      multipleResolution.forEach(
        (
          data: {
            output: string
            videoBitrate: string
            audioBitrate: string
            size: string
          },
          index: number,
          array: any
        ) => {
          ffmpeg(FILE_VIDEO_PATH + fileName, { timeout: 432000 })
            .addOptions([
              '-profile:v baseline',
              '-level 3.0',
              '-start_number 0',
              '-hls_time 4',
              '-hls_playlist_type vod',
              '-hls_flags periodic_rekey',
              '-hls_list_size 0',
              '-f hls',
              '-threads 0',
            ])
            .withVideoBitrate(data.videoBitrate)
            .withAudioBitrate(data.audioBitrate)
            .withAudioChannels(2)
            .withSize(data.size)
            .withVideoCodec('libx264')
            .withAudioCodec('aac')
            .withAudioChannels(2)
            .on('end', async () => {
              playlistContent =
                playlistContent +
                `#EXT-X-STREAM-INF:BANDWIDTH=${
                  parseInt(data.videoBitrate, 10) * 1024
                },RESOLUTION=${
                  data.size
                },CODECS="avc1.42c01e,mp4a.40.2"\n${originalFileName}-${
                  data.output
                }\n`
              fs.writeFileSync(
                `${FILE_VIDEO_PATH}${originalFileName}-playlist.m3u8`,
                playlistContent,
                'utf8'
              )
              if (index === array.length - 1) {
                removeFile()
              }
            })
            .on('error', function (err: any, stdout: any, stderr: any) {
              if (err) {
                console.log(err.message)
                console.log('stdout:\n' + stdout)
                console.log('stderr:\n' + stderr)
              }
            })
            .output(`${FILE_VIDEO_PATH}${originalFileName}-${data.output}`, {
              end: false,
            })
            .run()
        }
      )

      const s3 = new S3({
        accessKeyId: ID,
        secretAccessKey: SECRET,
      })
      const thumbnail = await new Promise((resolve, reject) => {
        fs.readFile(FILE_VIDEO_PATH + thumbnailName, (err, data) =>
          err ? reject(err) : resolve(data)
        )
      })

      await s3
        .upload({
          Bucket: BUCKET_NAME,
          Key: thumbnailName,
          Body: thumbnail,
          ACL: 'public-read',
        })
        .promise()
      if (multipleResolution.length === 0) {
        removeFile()
      }
      const urlVideo = `${originalFileName}-playlist.m3u8`
      const urlThumbnail = `https://${process.env.IMAGE_URL}/${thumbnailName}`
      this.onSuccess(res, {
        video: urlVideo,
        thumbnail: urlThumbnail,
        resolution: resolution,
      })
    } catch (error) {
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
  }

  async getVideo(req: Request, res: Response) {
    let filePath = FILE_VIDEO_PATH + req.params.id
    try {
      fs.createReadStream(filePath).pipe(res)
    } catch (e) {
      res.end(e)
    }
  }

  updateVideoMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
