import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import { eventController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
  optionalInfoMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class EventRouter extends CrudRouter<typeof eventController> {
  constructor() {
    super(eventController);
  }
  customRouting() {
    /**
     * @swagger
     * /event/create_event:
     *   post:
     *     tags:
     *       - Event
     *     summary: Create new event
     *     description: Create a new event with images and time details
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               images:
     *                 type: array
     *                 items:
     *                   type: string
     *                 description: Array of image URLs for the event
     *               start_time:
     *                 type: string
     *                 format: date-time
     *                 description: Event start time
     *               end_time:
     *                 type: string
     *                 format: date-time
     *                 description: Event end time
     *               description:
     *                 type: string
     *                 description: Event description
     *             required:
     *               - images
     *               - start_time
     *               - end_time
     *               - description
     *     responses:
     *       200:
     *         description: Event created successfully
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post(
      '/create_event',
      this.createMiddlewares(),
      this.route(this.createEvent)
    );

    /**
     * @swagger
     * /event/get_lists:
     *   post:
     *     tags:
     *       - Event
     *     summary: Get filtered list of events
     *     description: Get events list with location-based filtering
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               latitude:
     *                 type: number
     *                 description: Latitude for location filtering
     *               longitude:
     *                 type: number
     *                 description: Longitude for location filtering
     *               distance_order:
     *                 type: boolean
     *                 description: Whether to order by distance
     *     parameters:
     *       - in: query
     *         name: page
     *         schema:
     *           type: integer
     *         description: Page number
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *         description: Items per page
     *     responses:
     *       200:
     *         description: List of events retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       images:
     *                         type: array
     *                         items:
     *                           type: string
     *                       start_time:
     *                         type: string
     *                         format: date-time
     *                       end_time:
     *                         type: string
     *                         format: date-time
     *                       description:
     *                         type: string
     *                 meta:
     *                   type: object
     *                   properties:
     *                     total:
     *                       type: number
     *                     page:
     *                       type: number
     *                     limit:
     *                       type: number
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post(
      '/get_lists',
      this.getFilterListMiddlewares(),
      this.route(this.getListEvent)
    );
  }
  async getListEvent(req: Request, res: Response) {
    if (req && req.body && req.body.latitude) {
      req.params.latitude = req.body.latitude;
    }
    if (req && req.body && req.body.longitude) {
      req.params.longitude = req.body.longitude;
    }
    if (req && req.body && req.body.distance_order) {
      req.params.distance_order = req.body.distance_order;
    }
    if (req && req.body && req.body.radius) {
      req.params.radius = req.body.radius
  }
    console.log('29148 getList params - queryInfo:', req.params, req.queryInfo);
    const result = await this.controller.getListEvent(
      req.params,
      req.queryInfo
    );
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }
  async createEvent(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;

    await this.validateJSON(req.body, {
      required: ['images', 'start_time', 'end_time', 'description'],
    });
    const result = await this.controller.createEvent(req.body);
    this.onSuccess(res, result);
  }

  async update(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;
    const { id } = req.params;
    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }
  async delete(req: Request, res: Response) {
    const { id } = req.params;
    req.params.account_type = req.tokenInfo.payload.account_type;
    req.params.user_role = req.tokenInfo.role;

    const result = await this.controller.delete(req.params, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }
  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()];
  }
  /**
   * @swagger
   * /events:
   *   get:
   *     summary: Get a list of events
   *     tags: [Events]
   *     responses:
   *       200:
   *         description: List of events
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     count:
   *                       type: integer
   *                     rows:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/Event'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /events/{id}:
   *   get:
   *     summary: Get an event by ID
   *     tags: [Events]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The event ID
   *     responses:
   *       200:
   *         description: The event details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Event'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /events:
   *   post:
   *     summary: Create a new event
   *     tags: [Events]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Event'
   *     responses:
   *       201:
   *         description: Event created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Event'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  /**
   * @swagger
   * /events/{id}:
   *   put:
   *     summary: Update an event
   *     tags: [Events]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The event ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Event'
   *     responses:
   *       200:
   *         description: Event updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Event'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  /**
   * @swagger
   * /events/{id}:
   *   delete:
   *     summary: Delete an event
   *     tags: [Events]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The event ID to delete
   *     responses:
   *       200:
   *         description: Event deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
