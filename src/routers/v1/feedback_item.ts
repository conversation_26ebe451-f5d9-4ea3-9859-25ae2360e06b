/**
 * @swagger
 * components:
 *   schemas:
 *     FeedbackItem:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the feedback item
 *         icon:
 *           type: string
 *           description: Icon representing the feedback item
 *         content:
 *           type: string
 *           description: Content/text of the feedback item
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * tags:
 *   name: FeedbackItems
 *   description: Feedback items management operations
 */

import { feedbackItemController } from '@/controllers'
import { Request, Response } from '../base'
import { CrudRouter } from '../crud'
import { authInfoMiddleware, superAdminTypeMiddleware } from '@/middlewares'

export default class ReviewItemRouter extends CrudRouter<
  typeof feedbackItemController
> {
  constructor() {
    super(feedbackItemController)
  }

  customRouting(): void {
    /**
     * @swagger
     * /feedback-items/bulk:
     *   post:
     *     summary: Create multiple feedback items
     *     tags: [FeedbackItems]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               data:
     *                 type: array
     *                 items:
     *                   type: object
     *                   properties:
     *                     icon:
     *                       type: string
     *                     content:
     *                       type: string
     *                   required:
     *                     - icon
     *                     - content
     *             required:
     *               - data
     *     responses:
     *       200:
     *         description: Feedback items created successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.post(
      '/bulk',
      this.createMiddlewares(),
      this.route(this.bulkCreate)
    )

    /**
     * @swagger
     * /feedback-items/thema/{id}:
     *   post:
     *     summary: Associate feedback items with a theme
     *     tags: [FeedbackItems]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Theme ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               feedback_item_ids:
     *                 type: array
     *                 items:
     *                   type: string
     *                 description: Array of feedback item IDs
     *             required:
     *               - feedback_item_ids
     *     responses:
     *       200:
     *         description: Feedback items associated successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.post(
      '/thema/:id',
      this.createMiddlewares(),
      this.route(this.createThemaFeedbackItem)
    )
  }

  async bulkCreate(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          minItems: 1,
          items: {
            type: 'object',
            properties: {
              icon: {
                type: 'string',
              },
              content: {
                type: 'string',
              },
            },
            required: ['icon', 'content'],
          },
        },
      },
      required: ['data'],
      additionalProperties: false,
    })

    const { data } = req.body
    const result = await this.controller.bulkCreate(data)
    this.onSuccess(res, result)
  }

  async createThemaFeedbackItem(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        feedback_item_ids: {
          type: 'array',
          items: {
            type: 'string',
          },
          minItems: 1,
        },
      },
      required: ['feedback_item_ids'],
      additionalProperties: false,
    })

    const thema_id = req.params.id
    const { feedback_item_ids } = req.body

    const result = await this.controller.themaFeedbackItem({
      thema_id,
      feedback_item_ids,
    })
    this.onSuccess(res, result)
  }

  /**
   * @swagger
   * /feedback-items:
   *   get:
   *     summary: Get all feedback items
   *     tags: [FeedbackItems]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *     responses:
   *       200:
   *         description: List of feedback items
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 count:
   *                   type: integer
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/FeedbackItem'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }
}
