/**
 * @swagger
 * tags:
 *   name: SettingStationSubway
 *   description: Setting subway station management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     SettingStationSubway:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the subway station
 *         name:
 *           type: string
 *           description: Station name
 *         line_id:
 *           type: string
 *           description: ID of the associated subway line
 *         order:
 *           type: integer
 *           description: Display order of the station
 *         status:
 *           type: boolean
 *           description: Station status
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import { CrudRouter } from '../crud'
import { Request, Response } from '../base'
import { settingStationSubwayController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'
import multer = require('multer');

export default class SettingStationSubwayRouter extends CrudRouter<typeof settingStationSubwayController> {
    constructor() {
        super(settingStationSubwayController)
    }

    /**
     * @swagger
     * /setting-station-subway:
     *   get:
     *     summary: Get all subway stations
     *     tags: [SettingStationSubway]
     *     parameters:
     *       - $ref: '#/components/parameters/PageParam'
     *       - $ref: '#/components/parameters/PageSizeParam'
     *       - $ref: '#/components/parameters/FilterParam'
     *       - $ref: '#/components/parameters/SortParam'
     *     responses:
     *       200:
     *         description: List of subway stations
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 data:
     *                   type: array
     *                   items:
     *                     $ref: '#/components/schemas/SettingStationSubway'
     *
     * /setting-station-subway/import_excel:
     *   post:
     *     summary: Import subway stations from Excel file
     *     tags: [SettingStationSubway]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               file:
     *                 type: string
     *                 format: binary
     *     responses:
     *       200:
     *         description: Subway stations imported successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *
     * /setting-station-subway/order-item/{id}:
     *   put:
     *     summary: Update subway station order
     *     tags: [SettingStationSubway]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               order:
     *                 type: integer
     *     responses:
     *       200:
     *         description: Subway station order updated successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, 'image/')
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        const upload = multer({storage: storage})
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))

        this.router.post(
            '/import_excel',
            this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.put('/order-item/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }
    async importExcel(req: Request, res: Response) {
        req.body.url = 'image/' + req.file.filename

        const result = await this.controller.importExcel(req.body)
        this.onSuccess(res, result)
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}