import * as express from 'express'
const { Translate } =
  require('../../../node_modules/@google-cloud/translate').v2

import { Request, Response, BaseRouter } from '../base'
import { authInfoMiddleware } from '@/middlewares'

const translate = new Translate({
  projectId: process.env.GOOGLE_PROJECT_ID,
  credentials: JSON.parse(process.env.CREDENTIALS_GOOGLE),
})

export default class GoogleRoute extends BaseRouter {
  router: express.Router
  constructor() {
    super()
    this.router = express.Router()
    /**
     * @swagger
     * /google/translate:
     *   post:
     *     tags:
     *       - Google
     *     summary: Translate text
     *     description: Translate text to a target language (defaults to Korean)
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               text:
     *                 type: string
     *                 description: Text to translate
     *               target:
     *                 type: string
     *                 description: Target language code (defaults to 'ko')
     *                 example: ko
     *             required:
     *               - text
     *     responses:
     *       200:
     *         description: Text translated successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: string
     *                   description: Translated text
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post(
      '/translate',
      this.googleMiddleware(),
      this.route(this.translate)
    )
  }

  async translate(req: Request, res: Response) {
    let { text, target } = req.body
    await this.validateJSON(req.body, {
      type: 'object',
      required: ['text'],
    })
    if (!target) {
      target = 'ko'
    }
    const result = await this.translateAction(text, target)
    this.onSuccess(res, result)
  }

  async translateAction(text: string, language: string) {
    let [translation] = await translate.translate(text, language)
    return translation
  }

  async prediction(text: string) {
    let [predictions] = await translate.detect(text)
    return predictions
  }

  googleMiddleware() {
    return [authInfoMiddleware.run()]
  }
}
