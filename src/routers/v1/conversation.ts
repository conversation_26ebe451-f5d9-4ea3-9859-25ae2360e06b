import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { conversationController } from '@/controllers'
import { Request, Response } from '../base'
import { queryMiddleware, authInfoMiddleware } from '@/middlewares'

/**
 * @swagger
 * components:
 *   schemas:
 *     Conversation:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the conversation
 *         user_id:
 *           type: string
 *           description: User ID who participates in the conversation
 *         shop_id:
 *           type: string
 *           description: Shop/business ID that participates in the conversation
 *         last_message:
 *           type: string
 *           description: The last message sent in the conversation
 *         unread_count:
 *           type: integer
 *           description: Number of unread messages
 *         last_message_time:
 *           type: string
 *           format: date-time
 *           description: Timestamp of the last message
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The creation date
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The last update date
 */

/**
 * @swagger
 * tags:
 *   name: Conversations
 *   description: Conversation management and operations
 */

export default class ConversationRouter extends CrudRouter<
  typeof conversationController
> {
  constructor() {
    super(conversationController)
  }

  defaultRouting() {
    /**
     * @swagger
     * /conversation/find/{id}:
     *   get:
     *     summary: Find or create a conversation with a shop
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Shop ID to find or create conversation with
     *     responses:
     *       200:
     *         description: Conversation found or created
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Conversation'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.get(
      '/find/:id',
      this.getItemMiddlewares(),
      this.route(this.findOrCreate)
    )
    /**
     * @swagger
     * /conversation/biz/{shop_id}/{user_id}:
     *   get:
     *     summary: Find or create a business conversation
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: shop_id
     *         required: true
     *         schema:
     *           type: string
     *         description: Shop ID
     *       - in: path
     *         name: user_id
     *         required: true
     *         schema:
     *           type: string
     *         description: User ID
     *     responses:
     *       200:
     *         description: Conversation found or created
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Conversation'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.get(
      '/biz/:shop_id/:user_id',
      this.getItemMiddlewares(),
      this.route(this.bizFindOrCreate)
    )
    /**
     * @swagger
     * /conversation/find:
     *   get:
     *     summary: Create a conversation with query parameters
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: query
     *         name: user_id
     *         schema:
     *           type: string
     *         description: User ID
     *       - in: query
     *         name: shop_id
     *         schema:
     *           type: string
     *         description: Shop ID
     *       - in: query
     *         name: user_id_2
     *         schema:
     *           type: string
     *         description: Second user ID (optional)
     *       - in: query
     *         name: shop_id_2
     *         schema:
     *           type: string
     *         description: Second shop ID (optional)
     *     responses:
     *       200:
     *         description: Conversation found or created
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Conversation'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.get(
        '/find',
        this.getItemMiddlewares(),
        this.route(this.createConversation)
    )
    /**
     * @swagger
     * /conversation:
     *   get:
     *     summary: Get all conversations for authenticated user
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - $ref: '#/components/parameters/PageParam'
     *       - $ref: '#/components/parameters/PageSizeParam'
     *       - $ref: '#/components/parameters/FilterParam'
     *       - $ref: '#/components/parameters/SortParam'
     *       - in: query
     *         name: type
     *         schema:
     *           type: string
     *         description: Conversation type filter
     *     responses:
     *       200:
     *         description: List of conversations
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     objects:
     *                       type: array
     *                       items:
     *                         $ref: '#/components/schemas/Conversation'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.get('/', this.getListMiddlewares(), this.route(this.getList))
    /**
     * @swagger
     * /conversation/{id}:
     *   get:
     *     summary: Get conversation by ID
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Conversation ID
     *       - $ref: '#/components/parameters/IncludeParam'
     *     responses:
     *       200:
     *         description: Conversation details
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Conversation'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *       404:
     *         description: Conversation not found
     */
    this.router.get('/:id', this.getItemMiddlewares(), this.route(this.getItem))
    /**
     * @swagger
     * /conversation/{id}:
     *   delete:
     *     summary: Delete a conversation
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Conversation ID to delete
     *     responses:
     *       200:
     *         description: Conversation deleted
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *       404:
     *         description: Conversation not found
     */
    this.router.delete(
      '/:id',
      this.deleteAllMiddlewares(),
      this.route(this.delete)
    )
    /**
     * @swagger
     * /conversation/report_chat/{id}:
     *   put:
     *     summary: Report a conversation
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Conversation ID to report
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               reason:
     *                 type: string
     *                 description: Reason for reporting
     *     responses:
     *       200:
     *         description: Conversation reported
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
        '/report_chat/:id',
        this.updateMiddlewares(),
        this.route(this.reportFunc)
    );
    /**
     * @swagger
     * /conversation/unreport/{id}:
     *   put:
     *     summary: Unreport a previously reported conversation
     *     tags: [Conversations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Conversation ID to unreport
     *     responses:
     *       200:
     *         description: Conversation unreported
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
        '/unreport/:id',
        this.updateMiddlewares(),
        this.route(this.unreportFunc)
    );
  }

  async findOrCreate(req: Request, res: Response) {
    const { user_id } = req.tokenInfo.payload
    const { id: shop_id } = req.params
    const result = await this.controller.findOrCreate({ user_id, shop_id })
    this.onSuccess(res, result)
  }

  async bizFindOrCreate(req: Request, res: Response) {
    const { shop_id, user_id } = req.params
    const result = await this.controller.findOrCreate({ user_id, shop_id })
    this.onSuccess(res, result)
  }

  async createConversation(req: Request, res: Response){
    const {user_id, shop_id , user_id_2 , shop_id_2} = req.query
    const result = await this.controller.findOrCreate({ user_id, shop_id , user_id_2 , shop_id_2})
    this.onSuccess(res, result)
  }

  async getItem(req: Request, res: Response) {
    const user_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    req.queryInfo.filter.id = id
    const result = await this.controller.getItem({ user_id }, req.queryInfo)
    this.onSuccess(res, result)
  }

  async getList(req: Request, res: Response) {
    const user_id = req.tokenInfo.payload.user_id
    const employee_id = req.tokenInfo.payload.employee_id
    const { type } = req.query
    const result = await this.controller.getList({ user_id,employee_id ,type }, req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  async reportFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.reportFunc(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }
  async unreportFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.unreportFunc(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async delete(req: Request, res: Response) {
    const user_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const result = await this.controller.delete(
      { user_id },
      {
        filter: { id },
      }
    )
    this.onSuccess(res, result)
  }

  /**
   * @swagger
   * /conversation:
   *   get:
   *     summary: Get all conversations for authenticated user
   *     tags: [Conversations]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageParam'
   *       - $ref: '#/components/parameters/PageSizeParam'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *         description: Conversation type filter
   *     responses:
   *       200:
   *         description: List of conversations
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     objects:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/Conversation'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  getListMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }

  /**
   * @swagger
   * /conversation/{id}:
   *   get:
   *     summary: Get conversation by ID
   *     tags: [Conversations]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Conversation ID
   *       - $ref: '#/components/parameters/IncludeParam'
   *     responses:
   *       200:
   *         description: Conversation details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     object:
   *                       $ref: '#/components/schemas/Conversation'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         description: Conversation not found
   */
  getItemMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }
}
