import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { question<PERSON>ontroller } from '@/controllers'
import { Request, Response } from '../base'
import {
  authInfoMiddleware,
  queryMiddleware,
  superAdminTypeMiddleware,
} from '@/middlewares'

export default class Question<PERSON><PERSON><PERSON> extends <PERSON><PERSON><PERSON>outer<
  typeof questionController
> {
  constructor() {
    super(questionController)
  }

  customRouting() {
    this.router.post(
      '/answer_question/:id',
      this.createMiddlewares(),
      this.route(this.answerQuestion)
    )
    this.router.get(
      '/admin',
      this.adminGetListMiddleware(),
      this.route(this.adminGetList)
    )
    this.router.get(
      '/count_unread',
      this.adminGetListMiddleware(),
      this.route(this.countUnread)
    )
  }

  async answerQuestion(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    req.body.role = req.tokenInfo.payload.role
    const { id } = req.params
    const result = await this.controller.answerQuestion(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async create(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const result = await this.controller.create(req.body)
    this.onSuccess(res, result)
  }

  async getList(req: Request, res: Response) {
    req.body.role = req.tokenInfo.payload.role
    const result = await this.controller.getList(req.body, req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  async adminGetList(req: Request, res: Response) {
    const result = await this.controller.adminGetList(req.body, req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  async countUnread(req: Request, res: Response) {
    const result = await this.controller.countUnread()
    this.onSuccess(res, result)
  }

  adminGetListMiddleware() {
    return [
      authInfoMiddleware.run(),
      superAdminTypeMiddleware.run(),
      queryMiddleware.run(),
    ]
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  getListMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }
}
