import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { kLocationController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'

/**
 * @swagger
 * components:
 *   schemas:
 *     KLocation:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the location
 *         name:
 *           type: string
 *           description: Name of the location
 *         address:
 *           type: string
 *           description: Physical address
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude coordinate
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude coordinate
 *         status:
 *           type: boolean
 *           description: Whether the location is active
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

export default class KLocationRouter extends CrudRouter<
  typeof kLocationController
> {
  constructor() {
    super(kLocationController)
  }

  /**
   * @swagger
   * /klocation:
   *   get:
   *     tags:
   *       - KLocation
   *     summary: Get list of locations
   *     description: Retrieve a list of all locations with optional filtering
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *         description: Number of items per page
   *       - in: query
   *         name: filter
   *         schema:
   *           type: object
   *         description: Filter criteria
   *     responses:
   *       200:
   *         description: List of locations retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     total:
   *                       type: integer
   *                     items:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/KLocation'
   *       500:
   *         description: Server error
   * 
   *   post:
   *     tags:
   *       - KLocation
   *     summary: Create new location
   *     description: Add a new location to the system
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/KLocation'
   *     responses:
   *       200:
   *         description: Location created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   $ref: '#/components/schemas/KLocation'
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   * 
   * /klocation/{id}:
   *   get:
   *     tags:
   *       - KLocation
   *     summary: Get location by ID
   *     description: Retrieve detailed information about a specific location
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Location ID
   *     responses:
   *       200:
   *         description: Location details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   $ref: '#/components/schemas/KLocation'
   *       404:
   *         description: Location not found
   *       500:
   *         description: Server error
   * 
   *   put:
   *     tags:
   *       - KLocation
   *     summary: Update location
   *     description: Update an existing location's information
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Location ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/KLocation'
   *     responses:
   *       200:
   *         description: Location updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   $ref: '#/components/schemas/KLocation'
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Location not found
   *       500:
   *         description: Server error
   * 
   *   delete:
   *     tags:
   *       - KLocation
   *     summary: Delete location
   *     description: Delete an existing location
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Location ID
   *     responses:
   *       200:
   *         description: Location deleted successfully
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Location not found
   *       500:
   *         description: Server error
   */

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
