import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import { recentReadingController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class RecentReadingRouter extends CrudRouter<
  typeof recentReadingController
> {
  constructor() {
    super(recentReadingController);
  }

  customRouting() {
    this.router.post(
      '/get_lists',
      this.getFilterListMiddlewares(),
      this.route(this.getListRecentReading)
    );
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }
  async getListRecentReading(req: Request, res: Response) {
    if (req && req.body && req.body.latitude) {
      req.params.latitude = req.body.latitude;
    }

    if (req && req.body && req.body.longitude) {
      req.params.longitude = req.body.longitude;
    }

    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.params.user_id = req.tokenInfo.payload.user_id;
    }

    const result = await this.controller.getListRecentReading(
      req.params,
      req.queryInfo
    );
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }
  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), authInfoMiddleware.run()];
  }
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
