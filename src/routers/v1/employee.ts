import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { employeeController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'

/**
 * @swagger
 * tags:
 *   name: Employees
 *   description: Employee management
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Employee:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The employee ID
 *         username:
 *           type: string
 *           description: The employee username
 *         fullname:
 *           type: string
 *           description: Employee's full name
 *         role:
 *           type: string
 *           description: Employee role
 *         email:
 *           type: string
 *           description: Employee email address
 *         phone:
 *           type: string
 *           description: Employee phone number
 *         status:
 *           type: boolean
 *           description: Employee account status
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

export default class Employee<PERSON><PERSON><PERSON> extends <PERSON><PERSON><PERSON>outer<typeof employeeController> {
    constructor() {
        super(employeeController)

    }

    /**
     * @swagger
     * /employee/check_username:
     *   post:
     *     summary: Check if username exists
     *     tags: [Employees]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               username:
     *                 type: string
     *                 description: Username to check
     *     responses:
     *       200:
     *         description: Username check result
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *       201:
     *         description: Username already exists
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 201
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       type: object
     *                       properties:
     *                         message:
     *                           type: string
     */
    customRouting() {
        this.router.post('/check_username', this.route(this.checkUsername))
    }
    
    /**
     * @swagger
     * /employee:
     *   post:
     *     summary: Create a new employee
     *     tags: [Employees]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/Employee'
     *     responses:
     *       200:
     *         description: Successfully created employee
     *       401:
     *         description: Unauthorized
     */
    async create(req: Request, res: Response) {
        req.body.role = req.tokenInfo.payload.role
        const result = await this.controller.create(req.body)
        this.onSuccess(res, result)
    }
    
    /**
     * @swagger
     * /employee/{id}:
     *   put:
     *     summary: Update employee information
     *     tags: [Employees]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: string
     *         required: true
     *         description: Employee ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/Employee'
     *     responses:
     *       200:
     *         description: Successfully updated employee
     *       401:
     *         description: Unauthorized
     */
    async update(req: Request, res: Response) {
        req.body.employee_id = req.tokenInfo.payload.employee_id
        req.body.role = req.tokenInfo.payload.role
        const { id } = req.params
        const result = await this.controller.update(req.body, {
            filter: { id }
        })
        this.onSuccess(res, result)
    }
    
    /**
     * @swagger
     * /employee:
     *   get:
     *     summary: Get list of employees
     *     tags: [Employees]
     *     parameters:
     *       - in: query
     *         name: offset
     *         schema:
     *           type: integer
     *         description: The number of items to skip
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *         description: The number of items to return
     *       - in: query
     *         name: filter
     *         schema:
     *           type: object
     *         description: Filter criteria
     *     responses:
     *       200:
     *         description: A list of employees
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     objects:
     *                       type: object
     *                       properties:
     *                         count:
     *                           type: integer
     *                         rows:
     *                           type: array
     *                           items:
     *                             $ref: '#/components/schemas/Employee'
     *                 pagination:
     *                   type: object
     *                   properties:
     *                     current_page:
     *                       type: integer
     *                     next_page:
     *                       type: integer
     *                     prev_page:
     *                       type: integer
     *                     limit:
     *                       type: integer
     */
    async getList(req: Request, res: Response) {
        let objects = await this.controller.getList(req.queryInfo)
        if (objects.toJSON) {
            objects = objects.toJSON()
        }
        const resultNotPass = Object.assign({
            objects
        }, undefined)
        const rowJson = resultNotPass.objects.rows;
        for (let i = 0; i < rowJson.length; i++) {
            const jsonObject = rowJson[i].dataValues;
            delete jsonObject["password"]
            resultNotPass.objects.rows[i].dataValues = jsonObject;
        }
        const page = _.floor(req.queryInfo.offset / req.queryInfo.limit) + 1
        res.json({
            code: 200,
            results: resultNotPass,
            pagination: {
                'current_page': page,
                'next_page': page + 1,
                'prev_page': page - 1,
                'limit': req.queryInfo.limit
            }
        })
    }
    
    /**
     * @swagger
     * /employee/{id}:
     *   get:
     *     summary: Get employee by ID
     *     tags: [Employees]
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: string
     *         required: true
     *         description: Employee ID
     *     responses:
     *       200:
     *         description: Employee information
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Employee'
     */
    async getItem(req: Request, res: Response) {
        const { id } = req.params
        req.queryInfo.filter.id = id
        let object = await this.controller.getItem(req.queryInfo)
        object = object || {}
        const resultNotPass = Object.assign({
            object
        }, undefined)
        const rowJson = resultNotPass.object;

        const jsonObject = rowJson.dataValues;
        delete jsonObject["password"]
        resultNotPass.object.dataValues = jsonObject;

        if (Object.keys(object).length === 0) {
            res.json({
                code: 200
            })
        } else {
            res.json({
                code: 200,
                results: resultNotPass
            })
        }
    }

    async checkUsername(req: Request, res: Response) {
        const result = await this.controller.checkUsername(req.body)
        if (result['duplicate'] == true) {
            res.status(201).json({
                code: 201,
                results: { object: { message: result['resultOfCheckUser'] } }
            })
        } else {
            this.onSuccess(res, result)
        }
    }
  /**
   * @swagger
   * /employee:
   *   get:
   *     summary: Get a list of employees
   *     tags: [Employees]
   *     responses:
   *       200:
   *         description: List of employees
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     objects:
   *                       type: object
   *                       properties:
   *                         count:
   *                           type: integer
   *                         rows:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/Employee'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /employee/{id}:
   *   get:
   *     summary: Get an employee by ID
   *     tags: [Employees]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The employee ID
   *     responses:
   *       200:
   *         description: The employee details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Employee'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /employee:
   *   post:
   *     summary: Create a new employee
   *     tags: [Employees]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Employee'
   *     responses:
   *       201:
   *         description: Employee created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Employee'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }

  /**
   * @swagger
   * /employee/{id}:
   *   put:
   *     summary: Update an employee
   *     tags: [Employees]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The employee ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Employee'
   *     responses:
   *       200:
   *         description: Employee updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Employee'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  /**
   * @swagger
   * /employee/{id}:
   *   delete:
   *     summary: Delete an employee
   *     tags: [Employees]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The employee ID to delete
   *     responses:
   *       200:
   *         description: Employee deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }
}
