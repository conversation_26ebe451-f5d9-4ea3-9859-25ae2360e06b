import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { settingController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class SettingRouter extends CrudRouter<
  typeof settingController
> {
  constructor() {
    super(settingController);
  }

  customRouting() {
    this.router.get('/geo_code', this.route(this.geoCode))
    this.router.post('/send_sms', this.route(this.sendSms))
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    this.router.post('/blogs_per_page', this.route(this.blogsPerPage))
  }
  async geoCode (req: Request, res: Response) {
    const address = req.query.address as any
    const data = await this.controller.geoCode(address)
    this.onSuccess(res, data)
  }


  async blogsPerPage(req: Request, res: Response) {
    const data = req.body;
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
          mobile: { type: 'number' },
          desktop: { type: 'number' }
      },
       anyOf: [
          { required: ['mobile'] },
          { required: ['desktop'] }
        ]
    });

    const blogLimit = await this.controller.blogsPerPage(data);
    this.onSuccess(res, blogLimit);
  };

  async sendSms(req: Request, res: Response) {
    await this.validateJSON(req.body, {
        type: 'object',
        properties: {
            content: { type: 'string' },
        },
        required: ['content'],
    })
    const content = req.body.content;
    const data = await this.controller.sendSms(content);
    this.onSuccess(res, data);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
}
