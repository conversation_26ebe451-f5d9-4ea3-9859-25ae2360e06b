import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { pointController } from '@/controllers'
import { Request, Response } from '../base'
import {
  authInfoMiddleware,
  queryMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'

export default class PointRouter extends <PERSON><PERSON><PERSON>outer<typeof pointController> {
  constructor() {
    super(pointController)
  }

  customRouting() {
    this.router.get(
      '/history',
      this.getHistoryMiddlewares(),
      this.route(this.getHistory)
    )
    this.router.post(
      '/attendance',
      this.createMiddlewares(),
      this.route(this.attendance)
    )
    this.router.post(
      '/lottery',
      this.createMiddlewares(),
      this.route(this.lottery)
    )
    this.router.get('/count/total', this.getListMiddlewares(), this.route(this.count))
  }

  defaultRouting() {
    this.router.get('/', this.getListMiddlewares(), this.route(this.getList))
  }
  async count(req: Request, res: Response) {
    const result = await this.controller.count(req.queryInfo.filter)
    this.onSuccess(res, result)
  }

  async attendance(req: Request, res: Response) {
    const { user_id } = req.tokenInfo.payload
    const result = await this.controller.attendance(user_id)
    this.onSuccess(res, result)
  }

  async lottery(req: Request, res: Response) {
    const { user_id } = req.tokenInfo.payload
    const result = await this.controller.lottery(user_id)
    this.onSuccess(res, result)
  }

  async getHistory(req: Request, res: Response) {
    const { user_id } = req.tokenInfo.payload
    req.queryInfo = {
      ...req.queryInfo,
      filter: { ...req.queryInfo.filter, user_id },
    }
    const result = await this.controller.getList(req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  getHistoryMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }

  getListMiddlewares(): any[] {
    return [
      authInfoMiddleware.run(),
      adminTypeMiddleware.run(),
      queryMiddleware.run(),
    ]
  }
}
