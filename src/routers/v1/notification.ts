import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { notificationController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: User notification management
 */
export default class NotificationRouter extends CrudRouter<
  typeof notificationController
> {
  constructor() {
    super(notificationController);
  }

  customRouting() {
    this.router.delete(
      '/user_delete_all',
      this.deleteAllMiddlewares(),
      this.route(this.deleteAllUserNoti)
    );
    this.router.get(
      '/view/:id',
      this.getItemMiddlewares(),
      this.route(this.viewNotification)
    );
    this.router.get(
        '/read-all/:id',
        this.updateMultiViewMiddlewares(),
        this.route(this.readAll)
    );
    this.router.get(
        '/unread-all/:id',
        this.updateMultiViewMiddlewares(),
        this.route(this.unreadAll)
    );
  }

  /**
   * @swagger
   * /notifications/user_delete_all:
   *   delete:
   *     summary: Delete all notifications for current user
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: All user notifications deleted
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async deleteAllUserNoti(req: any, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const result = await this.controller.deleteAllUserNoti(req.body);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /notifications/view/{id}:
   *   get:
   *     summary: Mark a notification as viewed
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Notification marked as viewed
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  async viewNotification(req: any, res: Response) {
    const result = await this.controller.viewNotification(req.params.id);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /notifications/read-all/{id}:
   *   get:
   *     summary: Mark all notifications as read for user
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: All notifications marked as read
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async readAll(req: any, res: Response) {
    const result = await this.controller.updateMultiView(req.queryInfo , true);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /notifications/unread-all/{id}:
   *   get:
   *     summary: Mark all notifications as unread for user
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: All notifications marked as unread
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async unreadAll(req: any, res: Response) {
    const result = await this.controller.updateMultiView(req.queryInfo , false);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /notifications:
   *   get:
   *     summary: Get list of notifications
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageParam'
   *       - $ref: '#/components/parameters/PageSizeParam'
   *       - $ref: '#/components/parameters/FilterParam'
   *     responses:
   *       200:
   *         description: List of notifications
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /notifications/{id}:
   *   get:
   *     summary: Get single notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Notification details
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /notifications/{id}:
   *   put:
   *     summary: Update notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *               message:
   *                 type: string
   *               is_read:
   *                 type: boolean
   *     responses:
   *       200:
   *         description: Notification updated
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMultiViewMiddlewares(): any[] {
    return [authInfoMiddleware.run() , queryMiddleware.run()];
  }

  /**
   * @swagger
   * /notifications/{id}:
   *   delete:
   *     summary: Delete notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Notification deleted
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /notifications:
   *   post:
   *     summary: Create new notification
   *     tags: [Notifications]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               user_id:
   *                 type: string
   *                 format: uuid
   *               title:
   *                 type: string
   *               message:
   *                 type: string
   *               type:
   *                 type: string
   *               is_read:
   *                 type: boolean
   *                 default: false
   *     responses:
   *       201:
   *         description: Notification created
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
