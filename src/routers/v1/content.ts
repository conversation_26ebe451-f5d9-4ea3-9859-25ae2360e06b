import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import { contentController } from '@/controllers';
/**
 * @swagger
 * tags:
 *   name: Contents
 *   description: Content management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Content:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated ID of the content
 *         title:
 *           type: string
 *           description: Content title
 *         description:
 *           type: string
 *           description: Content description
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the content was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the content was last updated
 */

import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class ContentRouter extends CrudRouter<
  typeof contentController
> {
  constructor() {
    super(contentController);
  }

  /**
   * @swagger
   * /content:
   *   get:
   *     summary: Get a list of contents
   *     tags: [Contents]
   *     responses:
   *       200:
   *         description: List of contents
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     count:
   *                       type: integer
   *                     rows:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/Content'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /content/{id}:
   *   get:
   *     summary: Get a content by ID
   *     tags: [Contents]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The content ID
   *     responses:
   *       200:
   *         description: The content details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Content'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /content:
   *   post:
   *     summary: Create a new content
   *     tags: [Contents]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Content'
   *     responses:
   *       201:
   *         description: Content created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Content'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /content/{id}:
   *   put:
   *     summary: Update a content
   *     tags: [Contents]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The content ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Content'
   *     responses:
   *       200:
   *         description: Content updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Content'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /content/{id}:
   *   delete:
   *     summary: Delete a content
   *     tags: [Contents]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The content ID to delete
   *     responses:
   *       200:
   *         description: Content deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
