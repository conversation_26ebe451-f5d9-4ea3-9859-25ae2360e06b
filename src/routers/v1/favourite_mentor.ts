import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { favouriteMentorController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware } from '@/middlewares'
import * as _ from 'lodash'
export default class FavouriteMentorRouter extends CrudRouter<
  typeof favouriteMentorController
> {
  constructor() {
    super(favouriteMentorController)
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
