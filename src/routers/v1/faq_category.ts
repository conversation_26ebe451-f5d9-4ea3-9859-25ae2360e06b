/**
 * @swagger
 * components:
 *   schemas:
 *     FaqCategory:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the FAQ category
 *         name:
 *           type: string
 *           description: Category name
 *         order:
 *           type: integer
 *           description: Display order of the category
 *         status:
 *           type: boolean
 *           description: Whether the category is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * tags:
 *   name: FaqCategories
 *   description: FAQ category management operations
 */

import { CrudRouter } from '../crud'
import { Request, Response } from '../base'
import { faqCategoryController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'

export default class FaqControllerRouter extends CrudRouter<typeof faqCategoryController> {
    constructor() {
        super(faqCategoryController)
    }

    customRouting() {
        /**
         * @swagger
         * /faq-category/multiple/data:
         *   put:
         *     summary: Update multiple FAQ categories
         *     tags: [FaqCategories]
         *     security:
         *       - bearerAuth: []
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               list_data:
         *                 type: array
         *                 items:
         *                   $ref: '#/components/schemas/FaqCategory'
         *     responses:
         *       200:
         *         description: FAQ categories updated successfully
         *       401:
         *         $ref: '#/components/responses/UnauthorizedError'
         */
        this.router.put('/multiple/data', this.route(this.updateFagCategory))
    }

    async updateFagCategory(req: Request, res: Response) {
        const { list_data } = req.body
        const result = await this.controller.updateFagCategory({ list_data })
        this.onSuccess(res, result)
    }

    /**
     * @swagger
     * /faq-category/{id}:
     *   delete:
     *     summary: Delete a FAQ category
     *     tags: [FaqCategories]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The FAQ category ID
     *       - in: query
     *         name: new_group
     *         schema:
     *           type: string
     *         description: New group ID for reassignment
     *     responses:
     *       200:
     *         description: FAQ category deleted successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    async delete(req: Request, res: Response) {
        const { id } = req.params
        const { new_group } = req.query
        const result = await this.controller.delete({
            new_group
        }, {
            filter: { id },
        })
        this.onSuccess(res, result)
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}