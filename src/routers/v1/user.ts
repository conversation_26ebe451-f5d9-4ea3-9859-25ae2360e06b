/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management and operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     UserProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the user
 *         email:
 *           type: string
 *           description: User email
 *         username:
 *           type: string
 *           description: Username
 *         fullname:
 *           type: string
 *           description: User's full name
 *         phone:
 *           type: string
 *           description: User phone number
 *         avatar:
 *           type: string
 *           description: URL to user's avatar image
 *         address:
 *           type: string
 *           description: User's address
 *         role:
 *           type: string
 *           description: User role
 *         status:
 *           type: boolean
 *           description: Account status
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import { CrudRouter } from '../crud'
import { Request, Response } from '../base'
import { userController } from '@/controllers'
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    checkAuthMiddleware,
    adminTypeMiddleware,
    optionalInfoMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
import { ROLE } from '@/const'

/**
 * @swagger
 * /user/login:
 *   post:
 *     summary: Login user
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: JWT access token
 *                 user:
 *                   $ref: '#/components/schemas/UserProfile'
 *       401:
 *         description: Invalid credentials
 *
 * /user/register:
 *   post:
 *     summary: Register new user
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - username
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *               username:
 *                 type: string
 *               fullname:
 *                 type: string
 *               phone:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Invalid input or email already exists
 *
 * /user/profile:
 *   get:
 *     summary: Get current user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserProfile'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *   put:
 *     summary: Update current user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               fullname:
 *                 type: string
 *               phone:
 *                 type: string
 *               avatar:
 *                 type: string
 *               address:
 *                 type: string
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */

export default class UserRouter extends CrudRouter<typeof userController> {
    constructor() {
        super(userController)
    }

    customRouting() {
        this.router.put(
            '/change_password',
            this.createMiddlewares(),
            this.route(this.changePassword)
        )
        this.router.post(
            '/send_notification',
            this.createMiddlewares(),
            this.route(this.sendNotification)
        )
        this.router.post(
            '/subscribe_to_topic',
            this.subscribeFcmMiddlewares(),
            this.route(this.subscribeToTopic)
        )
        this.router.post(
            '/unsubscribe_from_topic',
            this.subscribeFcmMiddlewares(),
            this.route(this.unsubscribeFromTopic)
        )
        this.router.put(
            '/sync_images_url',
            this.subscribeFcmMiddlewares(),
            this.route(this.syncImagesUrl)
        )
        this.router.put(
            '/sync_post_limit_and_post_state_counter',
            this.subscribeFcmMiddlewares(),
            this.route(this.syncPostLimitAndPostStateCounter)
        )
        this.router.put(
            '/change_language',
            this.updateMiddlewares(),
            this.route(this.changeLanguage)
        )
        this.router.get(
            '/get_location_info',
            this.getListMiddlewares(),
            this.route(this.getLocationInfoFunc)
        )
        this.router.get(
            '/get_places_info',
            this.getListMiddlewares(),
            this.route(this.getPlacesInfoFunc)
        )
        this.router.put(
            '/update_jump_limit/:id',
            this.createMiddlewares(),
            this.route(this.updateJumpLimitAPI)
        )
        this.router.get(
            '/get_top_50_ranking',
            this.getListMiddlewares(),
            this.route(this.getTop50Ranking)
        )
        this.router.post(
            '/request_facebook_delete',
            this.createMiddlewares(),
            this.route(this.requestFacebookDelete)
        )
        this.router.get(
            '/find_user/:id',
            this.getItemMiddlewares(),
            this.route(this.findUser)
        )
        this.router.put(
            '/test_change_expired_date/:id',
            this.updateMiddlewares(),
            this.route(this.testChangeExpiredDate)
        )
        this.router.get(
            '/do_secret_job/:option1',
            this.getItemMiddlewares(),
            this.route(this.doSecretJobFunc)
        )
        this.router.put(
            '/roll_gift',
            this.createMiddlewares(),
            this.route(this.doRollGift)
        )
        this.router.put(
            '/notice_messenger_status',
            this.createMiddlewares(),
            this.route(this.updateNoticeMessengerStatus)
        )
        this.router.get(
            '/referral_code',
            this.createMiddlewares(),
            this.route(this.referralCode)
        )
        this.router.get('/phone/:phone', this.route(this.findUserByPhone))
        this.router.get('/count', this.route(this.countUser))
        this.router.get('/count_activity/:id', this.route(this.countActivity))
        this.router.put(
            '/update_auto_approve_status',
            this.createMiddlewares(),
            this.route(this.updateAutoApproveStatus)
        )
        this.router.put(
            '/update_reservation_status',
            this.createMiddlewares(),
            this.route(this.updateReservationStatus)
        )
        this.router.get(
            '/referral_code/check',
            this.route(this.checkReferralCode)
        ),
        this.router.post('/check-user-exists', this.route(this.checkUserByPhoneAndUsername)),
        this.router.put('/change-password-no-auth', this.route(this.changePasswordWithPhoneAndUsername))
        this.router.put('/reward', this.route(this.reward))
    }
    async reward(req: Request, res: Response) {
        console.log();
        
        const result = await this.controller.reward(req.body, req.tokenInfo)
        this.onSuccess(res, result)
      }

    async doRollGift(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const result = await this.controller.doRollGift(req.body)
        this.onSuccess(res, result)
    }

    async doSecretJobFunc(req: Request, res: Response) {
        console.log('29148  ~ file: user.ts ~ doSecretJobFunc')
        const {option1} = req.params
        console.log('29148  ~ file: user.ts ~ doSecretJobFunc ~ option1', option1)
        req.body.option1 = option1
        const result = await this.controller.doSecretJobFunc(
            req.body,
            req.queryInfo
        )
        this.onSuccess(res, result)
    }

    async testChangeExpiredDate(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const {id} = req.params
        const result = await this.controller.testChangeExpiredDate(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async getTop50Ranking(req: Request, res: Response) {
        req.queryInfo.order = [['exp', 'DESC']]
        req.queryInfo.limit = 50
        const result = await this.controller.getList(req.query, req.queryInfo)
        this.onSuccess(res, result)
    }

    async updateJumpLimitAPI(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            required: ['jump_limit'],
        })

        const {id} = req.params

        const result = await this.controller.updateJumpLimitAPI(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async getLocationInfoFunc(req: Request, res: Response) {
        const result = await userController.getLocationInfoFunc(req.query)

        this.onSuccess(res, result)
    }

    async getPlacesInfoFunc(req: Request, res: Response) {
        const result = await userController.getPlacesInfoFunc(req.query)

        this.onSuccess(res, result)
    }

    async syncPostLimitAndPostStateCounter(req: Request, res: Response) {
        const result = await userController.syncPostLimitAndPostStateCounter(
            req.body
        )

        this.onSuccess(res, result)
    }

    async syncImagesUrl(req: Request, res: Response) {
        const result = await userController.syncImagesUrl(req.body)

        this.onSuccess(res, result)
    }

    async subscribeToTopic(req: Request, res: Response) {
        const result = await userController.subscribeToTopic(req.body)

        this.onSuccess(res, result)
    }

    async changeLanguage(req: Request, res: Response) {
        const user_id = req.tokenInfo.payload.user_id

        const result = await userController.changeLanguage(req.body, {
            filter: {user_id},
        })

        this.onSuccess(res, result)
    }

    async unsubscribeFromTopic(req: Request, res: Response) {
        const result = await userController.unsubscribeFromTopic(req.body)

        this.onSuccess(res, result)
    }

    async update(req: Request, res: Response) {
        req.body.employee_id = req.tokenInfo.payload.employee_id
        req.body.user_role = req.tokenInfo.role

        const {id} = req.params
        const result = await this.controller.update(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async delete(req: Request, res: Response) {
        req.params.employee_id = req.tokenInfo.payload.employee_id
        const {id} = req.params
        const result = await this.controller.delete(req.params, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async requestFacebookDelete(req: Request, res: Response) {
        console.log('29148 FACEBOOK DELETE: ', req.body)
        const result = await userController.requestFacebookDelete(req.body)
        this.onSuccess(res, result)
    }

    async sendNotification(req: Request, res: Response) {
        const result = await userController.sendNotification(req.body)
        this.onSuccess(res, result)
    }

    async changePassword(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                password: {
                    type: 'string',
                },
                new_password: {
                    type: 'string',
                },
            },
            required: ['password', 'new_password'],
        })
        req.body.user_id = req.tokenInfo.payload.user_id
        const result = await this.controller.changePassword(req.body)
        this.onSuccess(res, result)
    }

    async checkUserByPhoneAndUsername(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                phone: {
                    type: 'string',
                },
                username: {
                    type: 'string',
                },
                secret_answer: {
                    type: 'string',
                },
            },
            // required: ['phone', 'username'],
        })
        const {phone, username , secret_answer} = req.body
        const result = await this.controller.checkUserByPhoneAndUsername(phone, username , secret_answer)
        this.onSuccess(res, result)
    }

    async changePasswordWithPhoneAndUsername(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                phone: {
                    type: 'string',
                },
                username: {
                    type: 'string',
                },
                new_password: {
                    type: 'string',
                },
            },
            required: ['phone', 'username', 'new_password'],
        })
        const result = await this.controller.changePasswordWithPhoneAndUsername(req.body)
        this.onSuccess(res, result)
    }

    async getList(req: Request, res: Response) {
        let objects = await this.controller.getList(req.query, req.queryInfo)
        if (objects.toJSON) {
            objects = objects.toJSON()
        }
        const resultNotPass = Object.assign(
            {
                objects,
            },
            undefined
        )
        const rowJson = resultNotPass.objects.rows
        for (let i = 0; i < rowJson.length; i++) {
            const jsonObject = rowJson[i].dataValues
            delete jsonObject['password']
            resultNotPass.objects.rows[i].dataValues = jsonObject
        }
        const page = _.floor(req.queryInfo.offset / req.queryInfo.limit) + 1
        res.json({
            code: 200,
            results: resultNotPass,
            pagination: {
                current_page: page,
                next_page: page + 1,
                prev_page: page - 1,
                limit: req.queryInfo.limit,
            },
        })
    }

    async findUser(req: Request, res: Response) {
        const {id} = req.params
        req.queryInfo.filter.id = id
        let object = await this.controller.findUser(req.queryInfo)
        object = object || {}
        const resultNotPass = Object.assign(
            {
                object,
            },
            undefined
        )
        const rowJson = resultNotPass.object

        const jsonObject = rowJson.dataValues
        if (jsonObject) {
            delete jsonObject['password']
        }
        resultNotPass.object.dataValues = jsonObject

        if (Object.keys(object).length === 0) {
            res.json({
                code: 200,
            })
        } else {
            res.json({
                code: 200,
                results: resultNotPass,
            })
        }
    }

    async getItem(req: Request, res: Response) {
        const role = req.tokenInfo.role
        const {id} = req.params
        req.queryInfo.filter.id = id
        let object = await this.controller.getItem(req.queryInfo)
        object = object || {}
        const resultNotPass = Object.assign(
            {
                object,
            },
            undefined
        )
        const rowJson = resultNotPass.object

        const jsonObject = rowJson.dataValues

        if (
            role !== ROLE.OPERATOR &&
            role !== ROLE.ADMIN &&
            role !== ROLE.SUPERADMIN
        ) {
            if (jsonObject) {
                delete jsonObject['password']
            }
        }
        resultNotPass.object.dataValues = jsonObject

        if (Object.keys(object).length === 0) {
            res.json({
                code: 200,
            })
        } else {
            res.json({
                code: 200,
                results: resultNotPass,
            })
        }
    }

    async updateNoticeMessengerStatus(req: Request, res: Response) {
        await this.validateJSON(req.body, {type: 'object', required: ['status']})
        const {user_id} = req.tokenInfo.payload
        const result = await this.controller.updateNoticeMessengerStatus(
            {status: req.body.status},
            {
                filter: {id: user_id},
            }
        )
        return this.onSuccess(res, result)
    }

    async referralCode(req: Request, res: Response) {
        const {user_id} = req.tokenInfo.payload
        const result = await this.controller.referralCode(user_id)
        return this.onSuccess(res, result)
    }

    async findUserByPhone(req: Request, res: Response) {
        const {phone} = req.params
        const result = await this.controller.findUserByPhone(phone)
        return this.onSuccess(res, result)
    }

    async countUser(req: Request, res: Response) {
        const group_id: string = req.query.group_id.toString()
        const result = await this.controller.countUser(group_id)
        return this.onSuccess(res, result)
    }

    async countActivity(req: Request, res: Response) {
        const {id} = req.params
        const result = await this.controller.countActivity(id)
        return this.onSuccess(res, result)
    }

    async updateAutoApproveStatus(req: Request, res: Response) {
        await this.validateJSON(req.body, {type: 'object', required: ['status']})
        const {user_id} = req.tokenInfo.payload
        const result = await this.controller.updateAutoApproveStatus(
            {status: req.body.status},
            {
                filter: {id: user_id},
            }
        )
        return this.onSuccess(res, result)
    }

    async updateReservationStatus(req: Request, res: Response) {
        await this.validateJSON(req.body, {type: 'object', required: ['status']})
        const {user_id} = req.tokenInfo.payload
        const result = await this.controller.updateReservationStatus(
            {status: req.body.status},
            {
                filter: {id: user_id},
            }
        )
        return this.onSuccess(res, result)
    }

    async checkReferralCode(req: Request, res: Response) {
        const {code} = req.query
        const result = await this.controller.checkReferralCode(code as any)
        return this.onSuccess(res, result)
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), queryMiddleware.run()]
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), checkAuthMiddleware.run()]
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), checkAuthMiddleware.run()]
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    subscribeFcmMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }

    rechargeMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
}
