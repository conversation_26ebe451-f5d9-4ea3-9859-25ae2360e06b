import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { Request, Response } from "../base";
import { actionLogController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";

export default class ActionLogRouter extends CrudRouter<
  typeof actionLogController
> {
  constructor() {
    super(actionLogController);
  }
  /**
   * @swagger
   * components:
   *   schemas:
   *     ActionLog:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           format: uuid
   *           example: "d290f1ee-6c54-4b01-90e6-d701748f0851"
   *         user_id:
   *           type: string
   *           format: uuid
   *           example: "b790f1ee-6c54-4b01-90e6-d701748f0879"
   *         action_type:
   *           type: string
   *           example: "write_comment"
   *         point:
   *           type: integer
   *           example: 10
   *         exp:
   *           type: integer
   *           example: 20
   *         created_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T12:34:56.789Z"
   */
  customRouting() {
    /**
     * @swagger
     * /action-log:
     *   get:
     *     summary: Get all action logs
     *     tags: [ActionLog]
     *     responses:
     *       200:
     *         description: List of action logs
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 $ref: '#/components/schemas/ActionLog'
     *
     *   post:
     *     summary: Create a new action log
     *     tags: [ActionLog]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/ActionLog'
     *     responses:
     *       201:
     *         description: Action log created
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ActionLog'
     *
     * /action-log/{id}:
     *   get:
     *     summary: Get an action log by ID
     *     tags: [ActionLog]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Action log ID
     *     responses:
     *       200:
     *         description: Found action log
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ActionLog'
     *       404:
     *         description: Action log not found
     *
     *   put:
     *     summary: Update an action log
     *     tags: [ActionLog]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/ActionLog'
     *     responses:
     *       200:
     *         description: Action log updated
     *
     *   delete:
     *     summary: Delete an action log (soft delete)
     *     tags: [ActionLog]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       204:
     *         description: Action log deleted
     */
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
