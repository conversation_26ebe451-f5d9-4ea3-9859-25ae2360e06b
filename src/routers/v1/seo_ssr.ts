import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { seoSsrController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
export default class SeoSsrRouter extends CrudRouter<typeof seoSsrController> {
  constructor() {
    super(seoSsrController)
  }

  customRouting() {
    this.router.get('/', this.getMiddlewares(), this.route(this.get))
    this.router.put('/', this.updateMiddlewares(), this.route(this.update))
    this.router.get('/seo/content', this.route(this.getSeo))
  }

  defaultRouting() {}

  async get(req: Request, res: Response) {
    const result = await this.controller.get()
    this.onSuccess(res, result)
  }

  async update(req: Request, res: Response) {
    const result = await this.controller.update(req.body)
    this.onSuccess(res, result)
  }

  async getSeo(req: Request, res: Response) {
    await this.validateJSON(req.query, {
      type: 'object',
      required: ['province', 'district', 'thema_id']
    })
    const { province, district, thema_id } = req.query as any
    const result = this.controller.getSeo(province, district, thema_id)
    this.onSuccess(res, result)
  }

  getMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }
}
