import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { groupController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'
export default class Faq<PERSON><PERSON>er extends C<PERSON><PERSON>outer<typeof groupController> {
    constructor() {
        super(groupController)
    }

    customRouting() {
        /**
         * @swagger
         * /group/order-group/{id}:
         *   put:
         *     tags:
         *       - Group
         *     summary: Update group order
         *     description: Reorder a group using drag and drop functionality
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *         description: ID of the group to reorder
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               order:
         *                 type: number
         *                 description: New order position for the group
         *     responses:
         *       200:
         *         description: Group order updated successfully
         *       401:
         *         description: Unauthorized
         *       404:
         *         description: Group not found
         *       500:
         *         description: Server error
         */
        this.router.put('/order-group/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }

    /**
     * @swagger
     * /group:
     *   post:
     *     tags:
     *       - Group
     *     summary: Create new group
     *     description: Create a new group with name and description
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               name:
     *                 type: string
     *                 description: Name of the group
     *               description:
     *                 type: string
     *                 description: Description of the group
     *             required:
     *               - name
     *     responses:
     *       200:
     *         description: Group created successfully
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     *   get:
     *     tags:
     *       - Group
     *     summary: Get list of groups
     *     description: Retrieve a paginated list of groups
     *     parameters:
     *       - in: query
     *         name: page
     *         schema:
     *           type: integer
     *         description: Page number for pagination
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *         description: Number of items per page
     *     responses:
     *       200:
     *         description: List of groups retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       name:
     *                         type: string
     *                       description:
     *                         type: string
     *                       order:
     *                         type: number
     *                 meta:
     *                   type: object
     *                   properties:
     *                     total:
     *                       type: number
     *                     page:
     *                       type: number
     *                     limit:
     *                       type: number
     *       500:
     *         description: Server error
     * 
     * /group/{id}:
     *   delete:
     *     tags:
     *       - Group
     *     summary: Delete group
     *     description: Delete a group and optionally move its items to another group
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: ID of the group to delete
     *       - in: query
     *         name: new_group
     *         schema:
     *           type: string
     *         description: ID of the group to move items to (optional)
     *     responses:
     *       200:
     *         description: Group deleted successfully
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Group not found
     *       500:
     *         description: Server error
     */
    async delete(req: Request, res: Response) {
        const { id } = req.params
        const {new_group} = req.query
        const result = await this.controller.delete({
            new_group
        },{
            filter: { id },
        })
        this.onSuccess(res, result)
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(),adminTypeMiddleware.run()]
    }
}