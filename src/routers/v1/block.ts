import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { authInfoMiddleware } from '@/middlewares'
import { blockController } from '@/controllers'

/**
 * @swagger
 * tags:
 *   - name: Blocks
 *     description: User blocking operations
 * 
 * components:
 *   schemas:
 *     Block:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the block
 *         user_id:
 *           type: string
 *           description: ID of the user who initiated the block
 *         blocked_id:
 *           type: string
 *           description: ID of the blocked user
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the block was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the block was last updated
 *       example:
 *         id: "123e4567-e89b-12d3-a456-426614174000"
 *         user_id: "123e4567-e89b-12d3-a456-426614174001"
 *         blocked_id: "123e4567-e89b-12d3-a456-426614174002"
 *         createdAt: "2022-01-01T00:00:00Z"
 *         updatedAt: "2022-01-01T00:00:00Z"
 */

export default class BlockRouter extends C<PERSON><PERSON>outer<typeof blockController> {
  constructor() {
    super(blockController)
  }

  /**
   * @swagger
   * /block:
   *   post:
   *     summary: Create a new block
   *     tags: [Blocks]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Block'
   *     responses:
   *       201:
   *         description: Block created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Block'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
