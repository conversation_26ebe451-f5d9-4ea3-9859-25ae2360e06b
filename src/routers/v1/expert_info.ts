import { <PERSON>rudRouter } from "../crud";
import { Request, Response } from "../base";
import { expertInfoController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";
import { uploadToS3 } from "@/utils/uploadS3";
import * as multer from "multer";
export default class ExpertInfoRouter extends CrudRouter<
  typeof expertInfoController
> {
  constructor() {
    super(expertInfoController);
  }

  customRouting() {
    const upload = multer({ dest: "uploads/" });
    this.router.post(
      "/",
      upload.fields([
        { name: "business_card_image_urls", maxCount: 5 },
        { name: "portfolio_image_urls", maxCount: 10 },
        { name: "qualifications", maxCount: 5 },
      ]),
      this.createMiddlewares(),
      this.route(this.create)
    );
    this.router.put(
      "/:id",
      upload.fields([
        { name: "business_card_image_urls", maxCount: 5 },
        { name: "portfolio_image_urls", maxCount: 10 },
        { name: "qualifications", maxCount: 5 },
      ]),
      this.updateMiddlewares(),
      this.route(this.update)
    );
  }
  async create(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }

    const fileFields = [
      "business_card_image_urls",
      "portfolio_image_urls",
      "qualifications",
    ];
    const uploadedImages: { [key: string]: string[] } = {};

    const files = req.files as { [fieldname: string]: Express.Multer.File[] };

    for (const field of fileFields) {
      if (files && files[field]) {
        const uploadResults = await Promise.all(
          files[field].map((file) => uploadToS3(file))
        );
        const urls = uploadResults.map((result) => result.url);

        uploadedImages[field] = urls;
        req.body[field] = urls;
      }
    }

    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }
  async update(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    const { id } = req.params;

    const fileFields = [
      "business_card_image_urls",
      "portfolio_image_urls",
      "qualification_files",
    ];
    const uploadedImages: { [key: string]: string[] } = {};

    const files = req.files as { [fieldname: string]: Express.Multer.File[] };

    for (const field of fileFields) {
      if (files && files[field]) {
        const uploadResults = await Promise.all(
          files[field].map((file) => uploadToS3(file))
        );
        const urls = uploadResults.map((result) => result.url);
        uploadedImages[field] = urls;
        req.body[field] = urls;
      }
    }

    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
