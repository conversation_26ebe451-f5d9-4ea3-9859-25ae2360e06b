import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../crud'
import { favorite<PERSON>ob<PERSON>ontroller } from '@/controllers'
import { Request, Response } from '../base'
import * as _ from 'lodash'
/**
 * @swagger
 * tags:
 *   name: Favorite Job
 *   description: Favorite Job endpoints
 */
export default class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends <PERSON><PERSON><PERSON><PERSON>er<
  typeof favoriteJobController
> {
  constructor() {
    super(favoriteJobController)
  }
/**
 * @swagger
 * components:
 *   schemas:
 *     FavoriteJob:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: ID of the favorite job record
 *           example: 9a4b4bb7-8497-4b8d-9f30-2d45c3ac7b84
 *         user_id:
 *           type: string
 *           description: ID of the user who marked the job as favorite
 *           example: 3f3e4cd6-69cd-44bc-bac5-56f73bc34f9a
 *         job_id:
 *           type: string
 *           description: ID of the job that was marked as favorite
 *           example: f23d5b1e-8cd7-4e58-9a52-1b6eecb6c2f7
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the favorite job was created
 *           example: 2025-04-21T12:30:00Z
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the favorite job was last updated
 *           example: 2025-04-21T12:30:00Z
 *         deleted_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the favorite job was deleted (if applicable)
 *           example: null
 */

  customRouting() {
    /**
 * @swagger
 * /favorite_job:
 *   post:
 *     tags:
 *       - Favorite Job
 *     summary: Add a job to favorites
 *     description: Add a job to the user's list of favorite jobs.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - job_id
 *             properties:
 *               user_id:
 *                 type: string
 *                 description: ID of the user adding the job to favorites
 *                 example: 3f3e4cd6-69cd-44bc-bac5-56f73bc34f9a
 *               job_id:
 *                 type: string
 *                 description: ID of the job being added to favorites
 *                 example: f23d5b1e-8cd7-4e58-9a52-1b6eecb6c2f7
 *     responses:
 *       201:
 *         description: Job added to favorites successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/FavoriteJob'
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 *   
 *   delete:
 *     tags:
 *       - Favorite Job
 *     summary: Remove a job from favorites
 *     description: Remove a job from the user's list of favorite jobs.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - job_id
 *             properties:
 *               user_id:
 *                 type: string
 *                 description: ID of the user removing the job from favorites
 *                 example: 3f3e4cd6-69cd-44bc-bac5-56f73bc34f9a
 *               job_id:
 *                 type: string
 *                 description: ID of the job being removed from favorites
 *                 example: f23d5b1e-8cd7-4e58-9a52-1b6eecb6c2f7
 *     responses:
 *       200:
 *         description: Job removed from favorites successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                   example: "Job removed from favorites"
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Favorite job not found
 *       500:
 *         description: Internal server error
 */

    this.router.post('/',this.createMiddlewares(), this.route(this.create))
    
    this.router.delete('/',this.deleteMiddlewares(), this.route(this.removeFavorite))
  }
    async removeFavorite(req: Request, res: Response) {
      if (
        req.tokenInfo &&
        req.tokenInfo.payload &&
        req.tokenInfo.payload.user_id
      ) {
        req.body.user_id = req.tokenInfo.payload.user_id;
      }
      
      const result = await this.controller.removeFavorite(req.body, req.queryInfo);
      this.onSuccess(res, result);
    }
    async create(req: Request, res: Response) {
        if (
          req.tokenInfo &&
          req.tokenInfo.payload &&
          req.tokenInfo.payload.user_id
        ) {
          req.body.user_id = req.tokenInfo.payload.user_id;
        }
        
        const result = await this.controller.createFavorite(req.body, req.queryInfo);
        this.onSuccess(res, result);
      }
}
