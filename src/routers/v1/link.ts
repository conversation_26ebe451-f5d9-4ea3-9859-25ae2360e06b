import {<PERSON><PERSON><PERSON>outer} from '../crud';
import {Request, Response} from '../base';
import {linkController} from '@/controllers';
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    superAdminTypeMiddleware,
    checkAuthMiddleware,
    adminTypeMiddleware, optionalInfoMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class LinkRouter extends CrudRouter<typeof linkController> {
    constructor() {
        super(linkController);
    }

    customRouting() {
        /**
         * @swagger
         * /link/order-link/{id}:
         *   put:
         *     tags:
         *       - Link
         *     summary: Update link order
         *     description: Update the order of a link using drag and drop
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *         description: ID of the link to reorder
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               order:
         *                 type: number
         *                 description: New order position
         *     responses:
         *       200:
         *         description: Link order updated successfully
         *       401:
         *         description: Unauthorized
         *       404:
         *         description: Link not found
         *       500:
         *         description: Server error
         */
        this.router.put('/order-link/:id', this.route(this.dragDropLink))
    }

    /**
     * @swagger
     * /link:
     *   post:
     *     tags:
     *       - Link
     *     summary: Create new link
     *     description: Create a new link with name and image
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               name:
     *                 type: string
     *                 description: Name of the link
     *               image:
     *                 type: string
     *                 description: URL or path of the link image
     *             required:
     *               - name
     *               - image
     *     responses:
     *       200:
     *         description: Link created successfully
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    async create(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            required: ['name', 'image'],
        });
        const result = await this.controller.create(req.body);
        this.onSuccess(res, result);
    }

    /**
     * @swagger
     * /link/{id}:
     *   delete:
     *     tags:
     *       - Link
     *     summary: Delete link
     *     description: Delete a link by ID
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: ID of the link to delete
     *     responses:
     *       200:
     *         description: Link deleted successfully
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Link not found
     *       500:
     *         description: Server error
     */
    async delete(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.delete(req.params, {
            filter: {id},
        });
        this.onSuccess(res, result);
    }

    async dragDropLink(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropLink(id, req.body);
        this.onSuccess(res, result);
    }

    /**
     * @swagger
     * /link:
     *   get:
     *     tags:
     *       - Link
     *     summary: Get list of links
     *     description: Retrieve a list of links with optional filtering
     *     parameters:
     *       - in: query
     *         name: page
     *         schema:
     *           type: integer
     *         description: Page number for pagination
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *         description: Number of items per page
     *     responses:
     *       200:
     *         description: List of links retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       name:
     *                         type: string
     *                       image:
     *                         type: string
     *                       order:
     *                         type: number
     *                 meta:
     *                   type: object
     *                   properties:
     *                     total:
     *                       type: number
     *                     page:
     *                       type: number
     *                     limit:
     *                       type: number
     *       500:
     *         description: Server error
     */
    async getList(req: Request, res: Response) {
        const employee_id = req.tokenInfo ? req.tokenInfo.payload.employee_id : undefined;
        const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
        const result = await this.controller.getList(req.queryInfo ,employee_id,user_id)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run() , optionalInfoMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
}
