import { CrudRouter } from '../crud'
import { ticketUsedController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'

export default class TicketUsedRouter extends C<PERSON>Router<
  typeof ticketUsedController
> {
  constructor() {
    super(ticketUsedController)
  }

  getListMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }

  getItemMiddlewares(): any[] {
    return [authInfoMiddleware.run(), queryMiddleware.run()]
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
