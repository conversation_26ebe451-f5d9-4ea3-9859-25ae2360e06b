import { feedbackController } from '@/controllers'
import { Request, Response } from '../base'
import { CrudRouter } from '../crud'
import { authInfoMiddleware, superAdminTypeMiddleware } from '@/middlewares'

export default class ReviewRouter extends <PERSON><PERSON>Router<
  typeof feedbackController
> {
  constructor() {
    super(feedbackController)
  }

  defaultRouting(): void {}

  customRouting(): void {
    /**
     * @swagger
     * /feedback/shop/{id}:
     *   get:
     *     tags:
     *       - Feedback
     *     summary: Get shop feedback
     *     description: Get list of feedback for a specific shop
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Shop ID
     *     responses:
     *       200:
     *         description: Shop feedback retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       content:
     *                         type: string
     *                       rating:
     *                         type: number
     *                       user_id:
     *                         type: string
     *                       created_at:
     *                         type: string
     *                         format: date-time
     *       404:
     *         description: Shop not found
     *       500:
     *         description: Server error
     */
    this.router.get(
      '/shop/:id',
      this.getItemMiddlewares(),
      this.route(this.getListForShop)
    )
  }

  /**
   * @swagger
   * /feedback:
   *   post:
   *     tags:
   *       - Feedback
   *     summary: Create new feedback
   *     description: Submit new feedback for a shop
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               shop_id:
   *                 type: string
   *                 description: ID of the shop
   *               content:
   *                 type: string
   *                 description: Feedback content
   *               rating:
   *                 type: number
   *                 description: Rating score (1-5)
   *             required:
   *               - shop_id
   *               - rating
   *     responses:
   *       200:
   *         description: Feedback created successfully
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   *   delete:
   *     tags:
   *       - Feedback
   *     summary: Delete feedback
   *     description: Delete feedback by ID
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Feedback ID
   *     responses:
   *       200:
   *         description: Feedback deleted successfully
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Feedback not found
   *       500:
   *         description: Server error
   */

  async getListForShop(req: Request, res: Response) {
    const id = req.params.id
    const result = await this.controller.getListForShop(id)
    return this.onSuccess(res, result)
  }

  async delete(req: Request, res: Response) {
    const { id } = req.params
    const result = await this.controller.delete({
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), superAdminTypeMiddleware.run()]
  }
}
