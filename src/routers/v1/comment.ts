import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { commentController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
  optionalInfoMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
import { REVIEW_TYPE, ROLE } from '@/const';
import { limitRequest } from "@/config/utils";

/**
 * @swagger
 * tags:
 *   - name: Comments
 *     description: Comment management operations
 * 
 * components:
 *   schemas:
 *     Comment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the comment
 *         content:
 *           type: string
 *           description: The comment text
 *         post_id:
 *           type: string
 *           description: ID of the post being commented on
 *         user_id:
 *           type: string
 *           description: ID of the user who made the comment
 *         likes:
 *           type: integer
 *           description: Number of likes on the comment
 *         dislikes:
 *           type: integer
 *           description: Number of dislikes on the comment
 *         status:
 *           type: boolean
 *           description: Whether the comment is approved/visible
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: When the comment was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: When the comment was last updated
 *       example:
 *         id: "123e4567-e89b-12d3-a456-************"
 *         content: "This is a great post!"
 *         post_id: "123e4567-e89b-12d3-a456-************"
 *         user_id: "123e4567-e89b-12d3-a456-************"
 *         likes: 5
 *         dislikes: 1
 *         status: true
 *         createdAt: "2022-01-01T00:00:00Z"
 *         updatedAt: "2022-01-01T00:00:00Z"
 */
export default class CommentRouter extends CrudRouter<
    typeof commentController
> {
  constructor() {
    super(commentController);
  }

  customRouting() {
    /**
     * @swagger
     * /comment/get_lists:
     *   get:
     *     summary: Get filtered list of comments
     *     tags: [Comments]
     *     parameters:
     *       - $ref: '#/components/parameters/PaginationLimit'
     *       - $ref: '#/components/parameters/PaginationOffset'
     *       - $ref: '#/components/parameters/FilterParam'
     *     responses:
     *       200:
     *         description: Filtered list of comments
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     count:
     *                       type: integer
     *                     rows:
     *                       type: array
     *                       items:
     *                         $ref: '#/components/schemas/Comment'
     */
    this.router.get(
        '/get_lists',
        this.getFilterListMiddlewares(),
        this.route(this.getListReview)
    );

    /**
     * @swagger
     * /comment/get_list_admin:
     *   get:
     *     summary: Get list of comments for admin
     *     tags: [Comments]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - $ref: '#/components/parameters/PaginationLimit'
     *       - $ref: '#/components/parameters/PaginationOffset'
     *     responses:
     *       200:
     *         description: Admin comment list
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     count:
     *                       type: integer
     *                     rows:
     *                       type: array
     *                       items:
     *                         $ref: '#/components/schemas/Comment'
     */
    this.router.get(
        '/get_list_admin',
        this.getListAdminMiddlewares(),
        this.route(this.getListAdmin)
    );

    /**
     * @swagger
     * /comment/admin_create:
     *   post:
     *     summary: Create a comment as admin
     *     tags: [Comments]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               content:
     *                 type: string
     *               post_id:
     *                 type: string
     *             required:
     *               - content
     *               - post_id
     *     responses:
     *       201:
     *         description: Comment created by admin
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Comment'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.post('/admin_create',this.roleAdminMiddlewares(),this.route(this.createByAdmin))

    /**
     * @swagger
     * /comment/like_review/{id}:
     *   put:
     *     summary: Like a comment
     *     tags: [Comments]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The comment ID to like
     *     responses:
     *       200:
     *         description: Comment liked successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Comment'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
        '/like_review/:id',
        this.updateMiddlewares(),
        this.route(this.likeReviewFunc)
    );

    /**
     * @swagger
     * /comment/unlike_review/{id}:
     *   put:
     *     summary: Unlike a comment
     *     tags: [Comments]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The comment ID to unlike
     *     responses:
     *       200:
     *         description: Comment unliked successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Comment'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
        '/unlike_review/:id',
        this.updateMiddlewares(),
        this.route(this.unlikeReviewFunc)
    );

    /**
     * @swagger
     * /comment/dislike_review/{id}:
     *   put:
     *     summary: Dislike a comment
     *     tags: [Comments]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The comment ID to dislike
     *     responses:
     *       200:
     *         description: Comment disliked successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Comment'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
        '/dislike_review/:id',
        this.updateMiddlewares(),
        this.route(this.dislikeReviewFunc)
    );

    /**
     * @swagger
     * /comment/undislike_review/{id}:
     *   put:
     *     summary: Remove dislike from a comment
     *     tags: [Comments]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The comment ID to remove dislike from
     *     responses:
     *       200:
     *         description: Dislike removed successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Comment'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
        '/undislike_review/:id',
        this.updateMiddlewares(),
        this.route(this.undislikeReviewFunc)
    );
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }
  async getListAdmin(req: Request, res: Response) {
    req.queryInfo.filter.type = REVIEW_TYPE.POST;
    const result = await this.controller.getListAdmin(req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }
  /**
   * @swagger
   * /comment:
   *   post:
   *     summary: Create a new comment
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               content:
   *                 type: string
   *                 description: Comment content
   *               post_id:
   *                 type: string
   *                 description: ID of the post being commented on
   *             required:
   *               - content
   *               - post_id
   *     responses:
   *       201:
   *         description: Comment created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Comment'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async create(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    await this.validateJSON(req.body, {
      required: ['content', 'post_id', 'user_id'],
    });
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }

  async getListReview(req: Request, res: Response) {
    req.params.user_id =
        req &&
        req.tokenInfo &&
        req.tokenInfo.payload &&
        req.tokenInfo.payload.user_id;

    const role = req &&
        req.tokenInfo &&
        req.tokenInfo.payload &&
        req.tokenInfo.payload.role;

    if (role !== ROLE.OPERATOR && role !== ROLE.ADMIN && role !== ROLE.SUPERADMIN) {
      if (!req.queryInfo.filter) {
        req.queryInfo.filter = {}
      }
      req.queryInfo.filter.status = true;
    }

    const result = await this.controller.getList(req.params, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async getList(req: Request, res: Response) {
    const result = await this.controller.getList(req.params, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async createByAdmin(req : Request ,res : Response) {
    req.body.employee_id = req.tokenInfo.payload.employee_id;
    await this.validateJSON(req.body, {
      type: 'object',
      properties : {
        content: {
          type: 'string',
        },
      },
      required: ['content','employee_id','post_id'],
    });
    const result = await this.controller.createByAdmin(req.body);
    this.onSuccess(res, result);
  }
  async dislikeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.dislikeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async undislikeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.undislikeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async likeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.likeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async unlikeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.unlikeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  /**
   * @swagger
   * /comment/{id}:
   *   delete:
   *     summary: Delete a comment
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The comment ID to delete
   *     responses:
   *       200:
   *         description: Comment deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /comment:
   *   delete:
   *     summary: Delete multiple comments
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               ids:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: Array of comment IDs to delete
   *     responses:
   *       200:
   *         description: Comments deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /comment:
   *   post:
   *     summary: Create a new comment with rate limiting
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Comment'
   *     responses:
   *       201:
   *         description: Comment created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Comment'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       429:
   *         description: Rate limit exceeded
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run() , limitRequest(10 * 1000,4 , '10초 동안 최대 4개의 댓글만 달 수 있습니다!')];
  }
  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()];
  }
  getListAdminMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  roleAdminMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
  getByAdminMiddlewares(): any[] {
    return [queryMiddleware.run(),authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
