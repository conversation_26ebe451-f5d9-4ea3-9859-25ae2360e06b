import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { Request, Response } from "../base";
import { advertisingImageController, cityController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";
import { uploadToS3 } from "@/utils/uploadS3";
import * as multer from "multer";

export default class AdvertisingImageRouter extends CrudRouter<
  typeof advertisingImageController
> {
  constructor() {
    super(advertisingImageController);
  }

  customRouting() {
    /**
     * @swagger
     * tags:
     *   - name: Advertising Image
     *     description: CRUD for advertising images (banner, left, right, above)
     */

    /**
     * @swagger
     * components:
     *   schemas:
     *     AdvertisingImage:
     *       type: object
     *       properties:
     *         id:
     *           type: string
     *           format: uuid
     *         image_url:
     *           type: string
     *         position:
     *           type: string
     *           enum: [banner, left, right, above]
     *         page_key:
     *           type: string
     *         created_at:
     *           type: string
     *           format: date-time
     *         updated_at:
     *           type: string
     *           format: date-time
     */

    /**
     * @swagger
     * /advertising_image:
     *   post:
     *     summary: Upload an advertising image
     *     tags:
     *       - Advertising Image
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               image_url:
     *                 type: string
     *                 format: binary
     *               position:
     *                 type: string
     *                 enum: [banner, left, right, above]
     *               page_key:
     *                 type: string
     *     responses:
     *       '200':
     *         description: Image uploaded successfully
     *
     *   get:
     *     summary: Get a list of advertising images
     *     tags:
     *       - Advertising Image
     *     parameters:
     *       - in: query
     *         name: position
     *         schema:
     *           type: string
     *           enum: [banner, left, right, above]
     *       - in: query
     *         name: page_key
     *         schema:
     *           type: string
     *     responses:
     *       '200':
     *         description: List of advertising images
     */

    /**
     * @swagger
     * /advertising_image/{id}:
     *   get:
     *     summary: Get advertising image details
     *     tags:
     *       - Advertising Image
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       '200':
     *         description: Advertising image detail
     *
     *   put:
     *     summary: Update an advertising image
     *     tags:
     *       - Advertising Image
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               image_url:
     *                 type: string
     *                 format: binary
     *               position:
     *                 type: string
     *                 enum: [banner, left, right, above]
     *               page_key:
     *                 type: string
     *     responses:
     *       '200':
     *         description: Image updated successfully
     *
     *   delete:
     *     summary: Delete an advertising image
     *     tags:
     *       - Advertising Image
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       '200':
     *         description: Image deleted successfully
     */

    const upload = multer({ dest: "uploads/" });
    this.router.post(
      "/",
      upload.single("image_url"),
      this.createMiddlewares(),
      this.route(this.create)
    );
    this.router.get("/", this.getListMiddlewares(), this.route(this.getList));
    this.router.put(
      "/",
      upload.single("image_url"),
      this.updateMiddlewares(),
      this.route(this.update)
    );
  }
  async getList(req: Request, res: Response) {
    const result = await this.controller.getList(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }

  async create(req: Request, res: Response) {
    if (req.file) {
      const uploadResult = await uploadToS3(req.file);
      req.body.image_url = uploadResult.url;
    }
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }
  async update(req: Request, res: Response) {
    if (req.file) {
      const uploadResult = await uploadToS3(req.file);
      req.body.image_url = uploadResult.url;
    }
    const { id } = req.params;
    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
