import {<PERSON><PERSON><PERSON>outer} from '../crud'
import {Request, Response} from '../base'
import {settingDistrictController} from '@/controllers'
import {authInfoMiddleware, queryMiddleware} from '@/middlewares'
import multer = require('multer')

export default class SettingDistrictRouter extends CrudRouter<typeof settingDistrictController> {
    constructor() {
        super(settingDistrictController)

    }

    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, 'image/')
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        const upload = multer({storage: storage})
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))

        this.router.post(
            '/import_excel',
            // this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.put('/order-item/:id', this.createMiddlewares(), this.route(this.dragDropItem))
    }
    async importExcel(req: Request, res: Response) {
        req.body.url = 'image/' + req.file.filename

        const result = await this.controller.importExcel(req.body)
        this.onSuccess(res, result)
    }

    async dragDropItem(req: Request, res: Response) {
        const {id} = req.params;
        const result = await this.controller.dragDropItem(id, req.body);
        this.onSuccess(res, result);
    }
    
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}