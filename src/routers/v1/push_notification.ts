/**
 * @swagger
 * tags:
 *   name: PushNotification
 *   description: Push notification management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     PushNotification:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the notification
 *         title:
 *           type: string
 *           description: Notification title
 *         body:
 *           type: string
 *           description: Notification body content
 *         group:
 *           type: string
 *           description: Target user group for notification
 *         user_id:
 *           type: string
 *           description: Target specific user ID
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import { CrudRouter } from '../crud'
import { Request, Response } from '../base'
import { pushNotificationController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, adminTypeMiddleware } from '@/middlewares'

/**
 * @swagger
 * /push-notification/send_fcm_to_group/{notification_id}:
 *   post:
 *     summary: Send notification to a group
 *     tags: [PushNotification]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: notification_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the notification to send
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 * 
 * /push-notification/send_fcm_to_user/{notification_id}:
 *   post:
 *     summary: Send notification to a specific user
 *     tags: [PushNotification]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: notification_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the notification to send
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 * 
 * /push-notification:
 *   get:
 *     summary: Get all notifications
 *     tags: [PushNotification]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - $ref: '#/components/parameters/FilterParam'
 *       - $ref: '#/components/parameters/SortParam'
 *     responses:
 *       200:
 *         description: List of notifications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/PushNotification'
 *   post:
 *     summary: Create new notification
 *     tags: [PushNotification]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - body
 *             properties:
 *               title:
 *                 type: string
 *               body:
 *                 type: string
 *               group:
 *                 type: string
 *               user_id:
 *                 type: string
 *     responses:
 *       201:
 *         description: Notification created successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */

export default class PushNotificationRouter extends CrudRouter<typeof pushNotificationController> {
    constructor() {
        super(pushNotificationController)

    }
    customRouting() {
        this.router.post('/send_fcm_to_group/:notification_id', this.adminMiddlewares(), this.route(this.sendFcmToGroup));
        this.router.post('/send_fcm_to_user/:notification_id', this.adminMiddlewares(), this.route(this.sendFcmToUser));
    }
    async sendFcmToUser(req: Request, res: Response) {
        req.body.id = req.params.notification_id;
        const result = await this.controller.sendFcmToUser(req.body);
        this.onSuccess(res, result);
    }
    async sendFcmToGroup(req: Request, res: Response) {
        req.body.id = req.params.notification_id;
        const result = await this.controller.sendFcmToGroup(req.body);
        this.onSuccess(res, result);
    }
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
    adminMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run(), ]
    }
}