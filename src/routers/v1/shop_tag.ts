/**
 * @swagger
 * tags:
 *   name: ShopTags
 *   description: Shop tag management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     ShopTag:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the shop tag
 *         name:
 *           type: string
 *           description: Tag name
 *         order:
 *           type: integer
 *           description: Display order of the tag
 *         status:
 *           type: boolean
 *           description: Tag status
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import { CrudRouter } from '../crud';
import { Request, Response } from '../base';
import { shopTagController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * /shop-tag:
 *   get:
 *     summary: Get all shop tags
 *     tags: [ShopTags]
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'  
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - $ref: '#/components/parameters/FilterParam'
 *       - $ref: '#/components/parameters/SortParam'
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ShopTag'
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: number
 *                     page:
 *                       type: number
 *                     pageSize:
 *                       type: number
 *   post:
 *     summary: Create new shop tag
 *     tags: [ShopTags] 
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               order:
 *                 type: integer
 *               status:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Created
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *         
 *   put:
 *     summary: Update shop tag
 *     tags: [ShopTags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               order:
 *                 type: integer
 *               status:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Updated
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
export default class ShopTagRouter extends CrudRouter<
  typeof shopTagController
> {
  constructor() {
    super(shopTagController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
