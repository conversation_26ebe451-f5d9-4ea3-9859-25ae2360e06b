import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { rankingController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'

export default class RankingRouter extends Crud<PERSON>outer<
  typeof rankingController
> {
  constructor() {
    super(rankingController);
  }

  /**
   * @swagger
   * components:
   *   schemas:
   *     Ranking:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           format: uuid
   *         period:
   *           type: string
   *           example: "weekly"
   *           description: "Ranking period (e.g., daily, weekly, monthly)"
   *         period_start:
   *           type: string
   *           format: date-time
   *           example: "2025-05-01T00:00:00Z"
   *         metric:
   *           type: string
   *           example: "exp"
   *           description: "Ranking metric (e.g., exp, point, posts)"
   *         user_id:
   *           type: string
   *           format: uuid
   *         score:
   *           type: integer
   *           example: 250
   *         rank:
   *           type: integer
   *           example: 1
   *         post_score:
   *           type: integer
   *           example: 30
   *         comment_score:
   *           type: integer
   *           example: 20
   *         review_score:
   *           type: integer
   *           example: 15
   *         point_score:
   *           type: integer
   *           example: 40
   *         attendance_score:
   *           type: integer
   *           example: 10
   *         like_score:
   *           type: integer
   *           example: 25
   *         invite_score:
   *           type: integer
   *           example: 50
   *         report_score:
   *           type: integer
   *           example: 5
   *         created_at:
   *           type: string
   *           format: date-time
   *         updated_at:
   *           type: string
   *           format: date-time
   *
   *   parameters:
   *     RankingFilter:
   *       in: query
   *       name: filter
   *       schema:
   *         type: string
   *       description: "JSON string for filtering, e.g., {\"period\": \"weekly\", \"metric\": \"point\"}"
   *       required: false
   */

  /**
   * @swagger
   * tags:
   *   - name: Ranking
   *     description: User ranking management
   */

  /**
   * @swagger
   * /ranking:
   *   get:
   *     summary: Get list of rankings
   *     tags:
   *       - Ranking
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         description: "JSON string for filtering, e.g., {\"period\": \"weekly\", \"metric\": \"post\"}"
   *       - in: query
   *         name: order
   *         schema:
   *           type: string
   *         description: "JSON string for sorting, e.g., [[\"created_at\", \"DESC\"]]"
   *     responses:
   *       200:
   *         description: List of ranking records
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Ranking'
   *                 paging:
   *                   type: object
   *                   properties:
   *                     page:
   *                       type: integer
   *                     limit:
   *                       type: integer
   *                     total:
   *                       type: integer
   *
   *   post:
   *     summary: Create a ranking record
   *     tags: [Ranking]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Ranking'
   *     responses:
   *       201:
   *         description: Ranking created
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Ranking'
   *
   * /ranking/{id}:
   *   put:
   *     summary: Update a ranking record
   *     tags: [Ranking]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             allOf:
   *               - $ref: '#/components/schemas/Ranking'
   *             required:
   *               - id
   *     responses:
   *       200:
   *         description: Ranking updated
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Ranking'
   *
   *   delete:
   *     summary: Soft delete a ranking record
   *     tags: [Ranking]
   *     parameters:
   *       - in: query
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: ID of the ranking to delete
   *     responses:
   *       204:
   *         description: Ranking deleted
   */

  customRouting() {
    this.router.get(
      "/get_lists",
      this.getListMiddlewares(),
      this.route(this.getListV2)
    );
  }

  async getListV2(req: Request, res: Response) {
    const result = await this.controller.getListV2(req.query, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
