import { <PERSON>rudRouter } from '../crud';
import { Request, Response } from '../base';
import { navIconController} from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class NavIconRouter extends CrudRouter<typeof navIconController> {
  constructor() {
    super(navIconController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }
  // async getList(req: Request, res: Response) {
  //   const result = await this.controller.getList(req.queryInfo)
  //   this.onSuccess(res, result)
  // }

  getListMiddlewares(): any[] {
    return [];
  }
  getItemMiddlewares(): any[] {
    return [];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
