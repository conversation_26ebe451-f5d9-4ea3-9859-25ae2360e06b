import {<PERSON>rud<PERSON>out<PERSON>} from '../crud'
import {blog<PERSON>ontroller} from '@/controllers'
import {authInfoMiddleware, adminTypeMiddleware, queryMiddleware, optionalInfoMiddleware} from '@/middlewares'
import {Request, Response} from "@/routers/base";

/**
 * @swagger
 * tags:
 *   - name: Blogs
 *     description: Blog management operations
 * 
 * components:
 *   schemas:
 *     Blog:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the blog
 *         title:
 *           type: string
 *           description: Blog title
 *         content:
 *           type: string
 *           description: Blog content
 *         thumbnail:
 *           type: string
 *           description: URL to the blog thumbnail image
 *         author:
 *           type: string
 *           description: Author of the blog
 *         status:
 *           type: boolean
 *           description: Whether the blog is published or draft
 *         employee_id:
 *           type: string
 *           description: ID of the employee who created the blog
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the blog was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the blog was last updated
 *       example:
 *         id: "123e4567-e89b-12d3-a456-426614174000"
 *         title: "Getting Started with TypeScript"
 *         content: "TypeScript is a typed superset of JavaScript..."
 *         thumbnail: "https://example.com/blog/typescript.jpg"
 *         author: "John Doe"
 *         status: true
 *         employee_id: "123e4567-e89b-12d3-a456-************"
 *         createdAt: "2022-01-01T00:00:00Z"
 *         updatedAt: "2022-01-01T00:00:00Z"
 */

export default class BlogController extends CrudRouter<typeof blogController> {
    constructor() {
        super(blogController)
    }

    customRouting() {
        /**
         * @swagger
         * /blog/statistic:
         *   post:
         *     summary: Get blog statistics
         *     tags: [Blogs]
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               id:
         *                 type: string
         *                 description: Blog ID
         *               startDate:
         *                 type: string
         *                 format: date
         *                 description: Start date for statistics
         *               endDate:
         *                 type: string
         *                 format: date
         *                 description: End date for statistics
         *     responses:
         *       200:
         *         description: Blog statistics retrieved
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 code:
         *                   type: integer
         *                   example: 200
         *                 results:
         *                   type: object
         */
        this.router.post(
            '/statistic',
            this.route(this.statisticBlog)
        )
        
        /**
         * @swagger
         * /blog/create-by-client:
         *   post:
         *     summary: Create a blog as a client
         *     tags: [Blogs]
         *     security:
         *       - bearerAuth: []
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/Blog'
         *     responses:
         *       200:
         *         description: Blog created successfully
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 code:
         *                   type: integer
         *                   example: 200
         *                 results:
         *                   type: object
         *                   properties:
         *                     object:
         *                       $ref: '#/components/schemas/Blog'
         */
        this.router.post('/create-by-client', this.clientCreateMiddlewares(), this.route(this.createByClient))
    }

    async statisticBlog(req: Request, res: Response) {
        const {id, startDate, endDate} = req.body
        const result = await this.controller.statisticBlog(id, startDate, endDate)
        this.onSuccess(res, result)
    }

    async getList(req: Request, res: Response) {
        const employee_id = req.tokenInfo ? req.tokenInfo.payload.employee_id : undefined;
        if (!employee_id) {
            if (req.queryInfo.filter) {
                req.queryInfo.filter = {...req.queryInfo.filter, status: true}
            } else {
                req.queryInfo.filter = {status: true}
            }
        }
        const result = await this.controller.getList(req.queryInfo)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }
    async createByClient(req: Request, res: Response) {
        const result = await this.controller.createByClient(req.body, req.queryInfo)
        this.onSuccess(res, result)
    }

    async getItem(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
        const {id} = req.params;
        req.queryInfo.filter.id = id
        const result = await this.controller.getItem(req.body, req.queryInfo);
        this.onSuccess(res, result);
    }

    /**
     * @swagger
     * /blog:
     *   post:
     *     summary: Create a new blog
     *     tags: [Blogs]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/Blog'
     *     responses:
     *       201:
     *         description: Blog created successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Blog'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }

    /**
     * @swagger
     * /blog/{id}:
     *   put:
     *     summary: Update a blog
     *     tags: [Blogs]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The blog ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/Blog'
     *     responses:
     *       200:
     *         description: Blog updated successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/Blog'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }

    /**
     * @swagger
     * /blog/{id}:
     *   delete:
     *     summary: Delete a blog
     *     tags: [Blogs]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The blog ID
     *     responses:
     *       200:
     *         description: Blog deleted successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
    }
    getListMiddlewares(): any[] {
        return [queryMiddleware.run() ,optionalInfoMiddleware.run()]
    }

    getItemMiddlewares(): any[] {
        return [queryMiddleware.run() ,optionalInfoMiddleware.run()]
    }

    clientCreateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}
