import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { contact<PERSON><PERSON>roller } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * components:
 *   schemas:
 *     Contact:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the contact
 *         name:
 *           type: string
 *           description: Contact name
 *         email:
 *           type: string
 *           description: Contact email address
 *         phone:
 *           type: string
 *           description: Contact phone number
 *         message:
 *           type: string
 *           description: Contact message content
 *         user_id:
 *           type: string
 *           description: ID of the user who submitted the contact form
 *         user_role:
 *           type: string
 *           description: Role of the user who submitted the contact form
 *         account_type:
 *           type: string
 *           description: Type of account
 *         status:
 *           type: boolean
 *           description: Contact status
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the contact was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the contact was last updated
 * 
 * tags:
 *   name: Contacts
 *   description: Contact form management
 */

export default class Contact<PERSON><PERSON>er extends C<PERSON><PERSON>outer<
  typeof contactController
> {
  constructor() {
    super(contactController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  /**
   * @swagger
   * /contact:
   *   post:
   *     summary: Create a new contact message
   *     tags: [Contacts]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Contact name
   *               email:
   *                 type: string
   *                 description: Contact email address
   *               phone:
   *                 type: string
   *                 description: Contact phone number
   *               message:
   *                 type: string
   *                 description: Contact message content
   *     responses:
   *       200:
   *         description: Contact created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     object:
   *                       $ref: '#/components/schemas/Contact'
   */
  async create(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /contact:
   *   get:
   *     summary: Get a list of contacts
   *     tags: [Contacts]
   *     responses:
   *       200:
   *         description: List of contacts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     count:
   *                       type: integer
   *                     rows:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/Contact'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /contact/{id}:
   *   get:
   *     summary: Get a contact by ID
   *     tags: [Contacts]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The contact ID
   *     responses:
   *       200:
   *         description: The contact details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Contact'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /contact/{id}:
   *   put:
   *     summary: Update a contact
   *     tags: [Contacts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The contact ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Contact'
   *     responses:
   *       200:
   *         description: Contact updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Contact'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /contact/{id}:
   *   delete:
   *     summary: Delete a contact
   *     tags: [Contacts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The contact ID to delete
   *     responses:
   *       200:
   *         description: Contact deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /contact:
   *   post:
   *     summary: Create a new contact
   *     tags: [Contacts]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Contact'
   *     responses:
   *       201:
   *         description: Contact created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Contact'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [queryMiddleware.run(), authInfoMiddleware.run()];
  }
}
