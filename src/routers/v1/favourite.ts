/**
 * @swagger
 * components:
 *   schemas:
 *     Favourite:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the favourite
 *         user_id:
 *           type: string
 *           description: ID of the user who created the favourite
 *         item_id:
 *           type: string
 *           description: ID of the favourited item
 *         type:
 *           type: string
 *           description: Type of the favourite item
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * tags:
 *   name: Favourites
 *   description: Favourite items management operations
 */

import { CrudRouter } from '../crud';
import { Request, Response } from '../base';
import { favouriteController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class FavouriteRouter extends CrudRouter<typeof favouriteController> {
  constructor() {
    super(favouriteController);
  }

  customRouting() {
    /**
     * @swagger
     * /favourite/get_lists:
     *   post:
     *     summary: Get filtered list of favourites
     *     tags: [Favourites]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               latitude:
     *                 type: number
     *                 description: Latitude for location filtering
     *               longitude:
     *                 type: number
     *                 description: Longitude for location filtering
     *     responses:
     *       200:
     *         description: List of favourites retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     count:
     *                       type: integer
     *                     rows:
     *                       type: array
     *                       items:
     *                         $ref: '#/components/schemas/Favourite'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.post(
      '/get_lists',
      this.getFilterListMiddlewares(),
      this.route(this.getListFavourite)
    );
  }

  async getListFavourite(req: Request, res: Response) {
    if (req && req.body && req.body.latitude) {
      req.params.latitude = req.body.latitude;
    }

    if (req && req.body && req.body.longitude) {
      req.params.longitude = req.body.longitude;
    }

    const result = await this.controller.getListFavourite(
      req.params,
      req.queryInfo
    );
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  /**
   * @swagger
   * /favourite:
   *   get:
   *     summary: Get all favourites
   *     tags: [Favourites]
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *     responses:
   *       200:
   *         description: List of favourites
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 count:
   *                   type: integer
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Favourite'
   */
  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), authInfoMiddleware.run()];
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
