import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { Request, Response } from "../base";
import { boardPermissionController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";

export default class BoardPermissionRouter extends CrudRouter<
  typeof boardPermissionController
> {
  constructor() {
    super(boardPermissionController);
  }
  /**
   * @swagger
   * components:
   *   schemas:
   *     BoardPermission:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           format: uuid
   *           example: "c123e456-78ab-4def-90gh-1234567890ab"
   *         level_id:
   *           type: string
   *           format: uuid
   *           description: Foreign key referencing level
   *           example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef"
   *         board_id:
   *           type: string
   *           format: uuid
   *           description: Foreign key referencing board
   *           example: "b1c2d3e4-f5g6-7890-abcd-0987654321ef"
   *         can_read:
   *           type: boolean
   *           example: true
   *         can_write:
   *           type: boolean
   *           example: false
   *         created_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T10:00:00.000Z"
   *         updated_at:
   *           type: string
   *           format: date-time
   *           example: "2025-05-27T10:00:00.000Z"
   */

  customRouting() {
    /**
     * @swagger
     * /board_permission:
     *   get:
     *     summary: Get all board permissions
     *     tags: [BoardPermission]
     *     responses:
     *       200:
     *         description: List of board permissions
     *         content:
     *           application/json:
     *             schema:
     *               type: array
     *               items:
     *                 $ref: '#/components/schemas/BoardPermission'
     *
     *   post:
     *     summary: Create a new board permission
     *     tags: [BoardPermission]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/BoardPermission'
     *     responses:
     *       201:
     *         description: Board permission created
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/BoardPermission'
     *
     * /board_permission/{id}:
     *   get:
     *     summary: Get a board permission by ID
     *     tags: [BoardPermission]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: Found board permission
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/BoardPermission'
     *       404:
     *         description: Not found
     *
     *   put:
     *     summary: Update a board permission
     *     tags: [BoardPermission]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/BoardPermission'
     *     responses:
     *       200:
     *         description: Board permission updated
     *
     *   delete:
     *     summary: Delete a board permission (soft delete)
     *     tags: [BoardPermission]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       204:
     *         description: Board permission deleted
     */
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
