import { <PERSON>rudRouter } from '../crud'
import { tagController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
import {Request, Response} from "@/routers/base";
export default class TagRouter extends CrudRouter<typeof tagController> {
  constructor() {
    super(tagController)
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    this.router.put('/order-tag/:id', this.createMiddlewares(), this.route(this.dragDrop))
  }

  async dragDrop(req: Request, res: Response) {
    const {id} = req.params;
    const result = await this.controller.dragDrop(id, req.body);
    this.onSuccess(res, result);
  }

  async getList(req: Request, res: Response) {
    const employee_id = req.tokenInfo ? req.tokenInfo.payload.employee_id : undefined;
    const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const result = await this.controller.getList(req.queryInfo ,employee_id,user_id)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
