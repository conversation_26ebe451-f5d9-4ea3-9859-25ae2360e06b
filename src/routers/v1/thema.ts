import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { themaController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class ThemaRouter extends CrudRouter<typeof themaController> {
  constructor() {
    super(themaController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    this.router.post('/clone/:id', this.createMiddlewares(), this.route(this.cloneThema))
  }

  async cloneThema(req: Request, res: Response){
    const {id} = req.params
    const result =await this.controller.cloneThema(id)
    this.onSuccess(res, result)
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
}
