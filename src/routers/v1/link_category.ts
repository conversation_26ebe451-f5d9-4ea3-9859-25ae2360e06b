import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { linkCategoryController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class LinkCategoryRouter extends CrudRouter<
  typeof linkCategoryController
> {
  constructor() {
    super(linkCategoryController);
  }

  customRouting() {
    /**
     * @swagger
     * /link-category/order-link-category/{id}:
     *   put:
     *     tags:
     *       - Link Category
     *     summary: Update category order
     *     description: Update the order of a link category using drag and drop
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: ID of the category to reorder
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               order:
     *                 type: number
     *                 description: New order position
     *     responses:
     *       200:
     *         description: Category order updated successfully
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Category not found
     *       500:
     *         description: Server error
     */
    this.router.put('/order-link-category/:id', this.route(this.dragDrop))
  }

  async dragDrop(req: Request, res: Response) {
    const {id} = req.params;
    const result = await this.controller.dragDrop(id, req.body);
    this.onSuccess(res, result);
  }

  /**
   * @swagger
   * /link-category:
   *   post:
   *     tags:
   *       - Link Category
   *     summary: Create new link category
   *     description: Create a new link category with name and description
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Name of the category
   *               description:
   *                 type: string
   *                 description: Description of the category
   *             required:
   *               - name
   *     responses:
   *       200:
   *         description: Category created successfully
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   *   get:
   *     tags:
   *       - Link Category
   *     summary: Get list of link categories
   *     description: Retrieve a list of link categories with optional filtering
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *         description: Number of items per page
   *     responses:
   *       200:
   *         description: List of categories retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                       name:
   *                         type: string
   *                       description:
   *                         type: string
   *                       order:
   *                         type: number
   *                 meta:
   *                   type: object
   *                   properties:
   *                     total:
   *                       type: number
   *                     page:
   *                       type: number
   *                     limit:
   *                       type: number
   *       500:
   *         description: Server error
   */

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
}
