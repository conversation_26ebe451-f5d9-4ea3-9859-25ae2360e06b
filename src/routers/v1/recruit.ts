import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import { recruitController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
  optionalInfoMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
import { ROLE } from '@/const';
export default class RecruitRouter extends CrudRouter<typeof recruitController> {
  constructor() {
    super(recruitController);
  }
  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    this.router.put(
      '/like/:id',
      this.updateMiddlewares(),
      this.route(this.likeRecruitFunc)
    );
    this.router.put(
      '/unlike/:id',
      this.updateMiddlewares(),
      this.route(this.unlikeRecruitFunc)
    );
    this.router.post(
      '/get_lists',
      this.getFilterListMiddlewares(),
      this.route(this.getListRecruit)
    );
    this.router.get(
      '/view/:id',
      this.getItemMiddlewares(),
      this.route(this.view)
    );
    this.router.get(
      '/get_relate/:id',
      this.getFilterListMiddlewares(),
      this.route(this.getRelate)
    );
    this.router.post(
      '/get_near',
      this.getFilterListMiddlewares(),
      this.route(this.getNear)
    );
    this.router.delete(
      '/delete_by_type/:type',
      this.deleteAllMiddlewares(),
      this.route(this.deleteByType)
    );
  }

  async deleteByType(req: Request, res: Response) {
    const { type } = req.params;
    const result = await this.controller.deleteByType({
      filter: { type },
    });
    this.onSuccess(res, result);
  }
  
  async getNear(req: Request, res: Response) {

    req.params.user_id =
    req &&
    req.tokenInfo &&
    req.tokenInfo.payload &&
    req.tokenInfo.payload.user_id;
    
    if (req && req.body && req.body.order_option) {
      req.params.order_option = req.body.order_option;
    }
    if (req && req.body && req.body.initial_id) {
      req.params.initial_id = req.body.initial_id;
    }
    if (req && req.body && req.body.categories) {
      req.params.categories = req.body.categories;
    }
    if (req && req.body && req.body.tags) {
      req.params.tags = req.body.tags;
    }
    const result = await this.controller.getNear(
      req.params,
      req.queryInfo
    );
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }
  async getRelate(req: Request, res: Response) {
    req.params.user_id =
    req &&
    req.tokenInfo &&
    req.tokenInfo.payload &&
    req.tokenInfo.payload.user_id;
    
    if (req && req.body && req.body.order_option) {
      req.params.order_option = req.body.order_option;
    }
    if (req && req.body && req.body.initial_id) {
      req.params.initial_id = req.body.initial_id;
    }
    if (req && req.body && req.body.categories) {
      req.params.categories = req.body.categories;
    }
    if (req && req.body && req.body.tags) {
      req.params.tags = req.body.tags;
    }
    req.params.initial_id = req.params.id;
    const result = await this.controller.getRelate(
      req.params,
      req.queryInfo
    );
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async view(req: Request, res: Response) {
    const { id } = req.params;
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.params.user_id = req.tokenInfo.payload.user_id;
    }
  
    req.queryInfo.filter.id = id;

    const result = await this.controller.view(req.params, req.queryInfo);
    this.onSuccess(res, result);
  }
  async create(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;
    
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }

  async update(req: Request, res: Response) {
    const { id } = req.params;

    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;
    console.log("29148 : RecruitRouters -> update -> req.body", req.body)

    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }

  async getListRecruit(req: Request, res: Response) {
    req.params.user_id =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id;
    if (req && req.body && req.body.location) {
      req.params.location = req.body.location;
    }
    if (req && req.body && req.body.categories) {
      req.params.categories = req.body.categories;
    }
    if (req && req.body && req.body.tags) {
      req.params.tags = req.body.tags;
    }
    const result = await this.controller.getListRecruit(req.params, req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async likeRecruitFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const { id } = req.params;
    const result = await this.controller.likeRecruit(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }

  async unlikeRecruitFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const { id } = req.params;
    const result = await this.controller.unlikeRecruit(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }
  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()];
  }  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getAuthItemMiddlewares(): any[] {
    return [queryMiddleware.run(), authInfoMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
