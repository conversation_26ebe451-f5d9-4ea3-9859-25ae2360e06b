import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { pointProductController } from '@/controllers'
import { authInfoMiddleware, adminTypeMiddleware } from '@/middlewares'

export default class PointProductRouter extends <PERSON><PERSON><PERSON>outer<
  typeof pointProductController
> {
  constructor() {
    super(pointProductController)
  }

  customRouting() {
    this.router.post(
      '/buy/:id',
      this.buyMiddleWare(),
      this.route(this.buyPointProduct)
    )
    this.router.get('/count/total', this.getListMiddlewares(),this.route(this.count))
  }

  async buyPointProduct(req: Request, res: Response) {
    const { id } = req.params
    const { user_id } = req.tokenInfo.payload
    const result = await this.controller.buyPointProduct(user_id, id)
    this.onSuccess(res, result)
  }
  async count(req: Request, res: Response) {
    const result = await this.controller.count(req.queryInfo.filter)
    this.onSuccess(res, result)
  }

  async create(req: Request, res: Response) {
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        type: {
          type: 'string',
        },
        title: {
          type: 'string',
        },
        point: {
          type: 'number',
          min: 0,
        },
        images: {
          type: 'array',
          minItems: 1,
          items: {
            type: 'string',
          },
        },
        thumbnails: {
          type: 'array',
          minItems: 1,
          items: {
            type: 'string',
          },
        },
      },
      required: ['type', 'title', 'point', 'images', 'thumbnails'],
      additionalProperties: false,
    })
    const result = await this.controller.create(req.body)
    this.onSuccess(res, result)
  }

  buyMiddleWare(): any[] {
    return [authInfoMiddleware.run()]
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
