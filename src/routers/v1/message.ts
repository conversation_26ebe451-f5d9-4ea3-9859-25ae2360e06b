import {<PERSON><PERSON><PERSON>out<PERSON>} from '../crud'
import {Request, Response} from '../base'
import {messageController} from '@/controllers'
import {authInfoMiddleware, queryMiddleware} from '@/middlewares'

/**
 * @swagger
 * components:
 *   schemas:
 *     Message:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the message
 *         conversation_id:
 *           type: string
 *           description: ID of the conversation this message belongs to
 *         user_id:
 *           type: string
 *           description: User ID who sent the message
 *         content:
 *           type: string
 *           description: The message content
 *         read:
 *           type: boolean
 *           description: Whether the message has been read
 *         attachments:
 *           type: array
 *           items:
 *             type: object
 *           description: Optional attachments with the message
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The creation date
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The last update date
 */

/**
 * @swagger
 * tags:
 *   name: Messages
 *   description: Message management and operations
 */

export default class MessageRouter extends CrudRouter<
    typeof messageController
> {
    constructor() {
        super(messageController)
    }

    customRouting() {
        /**
         * @swagger
         * /message/conversation/{id}:
         *   get:
         *     summary: Get all messages in a conversation
         *     tags: [Messages]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: string
         *         description: Conversation ID
         *       - $ref: '#/components/parameters/PageParam'
         *       - $ref: '#/components/parameters/PageSizeParam'
         *       - $ref: '#/components/parameters/SortParam'
         *     responses:
         *       200:
         *         description: List of messages in the conversation
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 code:
         *                   type: integer
         *                   example: 200
         *                 results:
         *                   type: object
         *                   properties:
         *                     objects:
         *                       type: array
         *                       items:
         *                         $ref: '#/components/schemas/Message'
         *                     count:
         *                       type: integer
         *                       description: Total count of messages
         *       401:
         *         $ref: '#/components/responses/UnauthorizedError'
         */
        this.router.get(
            '/conversation/:id',
            this.getListMiddlewares(),
            this.route(this.getListByConversationId)
        )
        /**
         * @swagger
         * /message/unread:
         *   get:
         *     summary: Get count of unread messages
         *     tags: [Messages]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: query
         *         name: type
         *         schema:
         *           type: string
         *         description: Message type filter (optional)
         *     responses:
         *       200:
         *         description: Count of unread messages
         *         content:
         *           application/json:
         *             schema:
         *               type: object
         *               properties:
         *                 code:
         *                   type: integer
         *                   example: 200
         *                 results:
         *                   type: object
         *                   properties:
         *                     count:
         *                       type: integer
         *                       description: Total count of unread messages
         *       401:
         *         $ref: '#/components/responses/UnauthorizedError'
         */
        this.router.get(
            '/unread',
            this.createMiddlewares(),
            this.route(this.unreadMessage)
        )
    }

    /**
     * @swagger
     * /message:
     *   post:
     *     summary: Create a new message
     *     tags: [Messages]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - conversation_id
     *             properties:
     *               conversation_id:
     *                 type: string
     *                 description: ID of the conversation
     *               content:
     *                 type: string
     *                 description: Message content
     *               attachments:
     *                 type: array
     *                 items:
     *                   type: object
     *                 description: Optional attachments
     *     responses:
     *       200:
     *         description: Message created
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Message'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     *       400:
     *         description: Invalid request body
     */
    async create(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            required: ['conversation_id'],
        })

        req.body.user_id = req.tokenInfo.payload.user_id
        const result = await this.controller.create(req.body)
        this.onSuccess(res, result)
    }

    async getListByConversationId(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        req.body.employee_id = req.tokenInfo.payload.employee_id
        req.queryInfo.filter.id = req.params.id
        const result = await messageController.getListByConversationId(
            req.body,
            req.queryInfo
        )
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async unreadMessage(req: Request, res: Response) {
        const {user_id} = req.tokenInfo.payload
        const {type} = req.query
        const result = await this.controller.unreadMessage({user_id, type})
        this.onSuccess(res, result)
    }

    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), queryMiddleware.run()]
    }
}
