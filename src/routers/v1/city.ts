import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { cityController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware, blockMiddleware, superAdminTypeMiddleware, adminTypeMiddleware } from '@/middlewares'
import * as _ from 'lodash'

/**
 * @swagger
 * tags:
 *   - name: Cities
 *     description: City management operations
 * 
 * components:
 *   schemas:
 *     City:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the city
 *         name:
 *           type: string
 *           description: City name
 *         code:
 *           type: string
 *           description: City code/abbreviation
 *         country:
 *           type: string
 *           description: Country the city belongs to
 *         status:
 *           type: boolean
 *           description: Whether the city is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the city was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the city was last updated
 *       example:
 *         id: "123e4567-e89b-12d3-a456-************"
 *         name: "New York"
 *         code: "NYC"
 *         country: "United States"
 *         status: true
 *         createdAt: "2022-01-01T00:00:00Z"
 *         updatedAt: "2022-01-01T00:00:00Z"
 */

export default class CityRouter extends CrudRouter<typeof cityController> {
    constructor() {
        super(cityController)

    }

    customRouting() {
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
    }
    
    /**
     * @swagger
     * /city:
     *   get:
     *     summary: Get list of cities
     *     tags: [Cities]
     *     responses:
     *       200:
     *         description: List of cities
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     count:
     *                       type: integer
     *                     rows:
     *                       type: array
     *                       items:
     *                         $ref: '#/components/schemas/City'
     */
    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }

    /**
     * @swagger
     * /city/{id}:
     *   get:
     *     summary: Get a city by ID
     *     tags: [Cities]
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The city ID
     *     responses:
     *       200:
     *         description: City details
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/City'
     */
    getItemMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }

    /**
     * @swagger
     * /city:
     *   post:
     *     summary: Create a new city
     *     tags: [Cities]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/City'
     *     responses:
     *       201:
     *         description: City created successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/City'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    /**
     * @swagger
     * /city/{id}:
     *   put:
     *     summary: Update a city
     *     tags: [Cities]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The city ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/City'
     *     responses:
     *       200:
     *         description: City updated successfully
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/City'
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    /**
     * @swagger
     * /city/{id}:
     *   delete:
     *     summary: Delete a city
     *     tags: [Cities]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: The city ID to delete
     *     responses:
     *       200:
     *         description: City deleted successfully
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}
