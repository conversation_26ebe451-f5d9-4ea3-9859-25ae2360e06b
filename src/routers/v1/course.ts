import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { Request, Response } from '../base'
import { courseController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware } from '@/middlewares'
import * as _ from 'lodash'
export default class CourseRouter extends <PERSON><PERSON><PERSON>outer<typeof courseController> {
  constructor() {
    super(courseController)
  }

  customRouting() {
    /**
     * @swagger
     * /course/set_recommended/{id}:
     *   put:
     *     tags:
     *       - Course
     *     summary: Set course as recommended
     *     description: Mark a course as recommended or remove recommendation
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Course ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               is_recommended:
     *                 type: boolean
     *                 description: Whether to set as recommended
     *     responses:
     *       200:
     *         description: Course recommendation status updated successfully
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Course not found
     *       500:
     *         description: Server error
     */
    this.router.put(
      '/set_recommended/:id',
      this.updateMiddlewares(),
      this.route(this.setRecommended)
    )

    /**
     * @swagger
     * /course/set_courses/{id}:
     *   post:
     *     tags:
     *       - Course
     *     summary: Set courses for a shop
     *     description: Associate courses with a specific shop
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Shop ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               course_ids:
     *                 type: array
     *                 items:
     *                   type: string
     *                 description: Array of course IDs to associate with the shop
     *     responses:
     *       200:
     *         description: Courses associated with shop successfully
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Shop not found
     *       500:
     *         description: Server error
     */
    this.router.post(
      '/set_courses/:id', // shop_id
      this.createMiddlewares(),
      this.route(this.setCourses)
    )
  }

  async setRecommended(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const result = await this.controller.setRecommended(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async setCourses(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const result = await this.controller.setCourses(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  /**
   * @swagger
   * /course:
   *   get:
   *     tags:
   *       - Course
   *     summary: Get list of courses
   *     description: Retrieve a paginated list of courses
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *         description: Number of items per page
   *       - in: query
   *         name: sort
   *         schema:
   *           type: string
   *         description: Sort field and direction (e.g. created_at:desc)
   *     responses:
   *       200:
   *         description: List of courses retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                       name:
   *                         type: string
   *                       description:
   *                         type: string
   *                       is_recommended:
   *                         type: boolean
   *                 meta:
   *                   type: object
   *                   properties:
   *                     total:
   *                       type: number
   *                     page:
   *                       type: number
   *                     limit:
   *                       type: number
   *       500:
   *         description: Server error
   *   post:
   *     tags:
   *       - Course
   *     summary: Create new course
   *     description: Create a new course
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Course name
   *               description:
   *                 type: string
   *                 description: Course description
   *               duration:
   *                 type: number
   *                 description: Course duration in minutes
   *             required:
   *               - name
   *     responses:
   *       200:
   *         description: Course created successfully
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */

  /**
   * @swagger
   * /courses:
   *   get:
   *     tags:
   *       - Course
   *     summary: Get list of courses
   *     description: Retrieve a paginated list of courses
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *         description: Number of items per page
   *       - in: query
   *         name: sort
   *         schema:
   *           type: string
   *         description: Sort field and direction (e.g. created_at:desc)
   *     responses:
   *       200:
   *         description: List of courses retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                       name:
   *                         type: string
   *                       description:
   *                         type: string
   *                       is_recommended:
   *                         type: boolean
   *                 meta:
   *                   type: object
   *                   properties:
   *                     total:
   *                       type: number
   *                     page:
   *                       type: number
   *                     limit:
   *                       type: number
   *       500:
   *         description: Server error
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /courses/{id}:
   *   get:
   *     tags:
   *       - Course
   *     summary: Get course by ID
   *     description: Retrieve detailed information about a specific course
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Course ID
   *     responses:
   *       200:
   *         description: Course details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   $ref: '#/components/schemas/Course'
   *       404:
   *         description: Course not found
   *       500:
   *         description: Server error
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /courses:
   *   post:
   *     tags:
   *       - Course
   *     summary: Create new course
   *     description: Create a new course
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Course name
   *               description:
   *                 type: string
   *                 description: Course description
   *               duration:
   *                 type: number
   *                 description: Course duration in minutes
   *             required:
   *               - name
   *     responses:
   *       200:
   *         description: Course created successfully
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  /**
   * @swagger
   * /courses/{id}:
   *   put:
   *     tags:
   *       - Course
   *     summary: Update course
   *     description: Update an existing course
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Course ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Course name
   *               description:
   *                 type: string
   *                 description: Course description
   *               duration:
   *                 type: number
   *                 description: Course duration in minutes
   *     responses:
   *       200:
   *         description: Course updated successfully
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Course not found
   *       500:
   *         description: Server error
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }

  /**
   * @swagger
   * /courses/{id}:
   *   delete:
   *     tags:
   *       - Course
   *     summary: Delete course
   *     description: Delete a course by ID
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Course ID
   *     responses:
   *       200:
   *         description: Course deleted successfully
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Course not found
   *       500:
   *         description: Server error
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
