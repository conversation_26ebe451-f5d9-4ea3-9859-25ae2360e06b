import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { dislikePostController } from '@/controllers';
/**
 * @swagger
 * tags:
 *   name: DislikePosts
 *   description: Dislike post management operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     DislikePost:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated ID of the dislike
 *         post_id:
 *           type: string
 *           description: ID of the post being disliked
 *         user_id:
 *           type: string
 *           description: ID of the user who disliked the post
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the dislike was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the dislike was last updated
 */

import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

export default class DislikePostRouter extends CrudRouter<
  typeof dislikePostController
> {
  constructor() {
    super(dislikePostController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  /**
   * @swagger
   * /dislike_post:
   *   get:
   *     summary: Get a list of dislikes
   *     tags: [DislikePosts]
   *     responses:
   *       200:
   *         description: List of dislikes
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     count:
   *                       type: integer
   *                     rows:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/DislikePost'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /dislike_post/{id}:
   *   get:
   *     summary: Get a dislike by ID
   *     tags: [DislikePosts]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The dislike ID
   *     responses:
   *       200:
   *         description: The dislike details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/DislikePost'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  /**
   * @swagger
   * /dislike_post:
   *   post:
   *     summary: Create a new dislike
   *     tags: [DislikePosts]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/DislikePost'
   *     responses:
   *       201:
   *         description: Dislike created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/DislikePost'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /dislike_post/{id}:
   *   put:
   *     summary: Update a dislike
   *     tags: [DislikePosts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The dislike ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/DislikePost'
   *     responses:
   *       200:
   *         description: Dislike updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/DislikePost'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  /**
   * @swagger
   * /dislike_post/{id}:
   *   delete:
   *     summary: Delete a dislike
   *     tags: [DislikePosts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The dislike ID to delete
   *     responses:
   *       200:
   *         description: Dislike deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
