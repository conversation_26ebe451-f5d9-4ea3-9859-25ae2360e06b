import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import { historyController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
import { HISTORY, ROLE } from '@/const';
export default class HistoryRouter extends CrudRouter<typeof historyController> {
  constructor() {
    super(historyController);
  }

  customRouting() {
    /**
     * @swagger
     * /history/get_shop_jump_up_histories/{shop_id}:
     *   get:
     *     tags:
     *       - History
     *     summary: Get shop jump-up history
     *     description: Get the jump-up history for a specific shop
     *     parameters:
     *       - in: path
     *         name: shop_id
     *         required: true
     *         schema:
     *           type: string
     *         description: ID of the shop
     *     responses:
     *       200:
     *         description: Shop jump-up history retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       shop_id:
     *                         type: string
     *                       type_1:
     *                         type: string
     *                       category:
     *                         type: string
     *                       created_at:
     *                         type: string
     *                         format: date-time
     *       404:
     *         description: Shop not found
     *       500:
     *         description: Server error
     */
    this.router.get(
      '/get_shop_jump_up_histories/:shop_id',
      this.getListMiddlewares(),
      this.route(this.getShopJumpUpHistories)
    );

    /**
     * @swagger
     * /history/get_user_shop_jump_up_histories:
     *   get:
     *     tags:
     *       - History
     *     summary: Get all user's shop jump-up histories
     *     description: Get all jump-up histories for shops owned by the authenticated user
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: User's shop jump-up histories retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       shop_id:
     *                         type: string
     *                       user_id:
     *                         type: string
     *                       type_1:
     *                         type: string
     *                       category:
     *                         type: string
     *                       created_at:
     *                         type: string
     *                         format: date-time
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.get(
      '/get_user_shop_jump_up_histories',
      this.getAuthListMiddlewares(),
      this.route(this.getAllShopJumpUpHistories)
    );

    /**
     * @swagger
     * /history/get_user_exp_earning_histories:
     *   get:
     *     tags:
     *       - History
     *     summary: Get user experience earning history
     *     description: Get the history of experience points earned by the authenticated user
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: User experience earning history retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: array
     *                   items:
     *                     type: object
     *                     properties:
     *                       id:
     *                         type: string
     *                       user_id:
     *                         type: string
     *                       type_1:
     *                         type: string
     *                       category:
     *                         type: string
     *                       exp_earned:
     *                         type: number
     *                       created_at:
     *                         type: string
     *                         format: date-time
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.get(
      '/get_user_exp_earning_histories',
      this.getAuthListMiddlewares(),
      this.route(this.getUserExpEarningHistories)
    );
  }

  async getUserExpEarningHistories(req: Request, res: Response) {
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;

    if (req.body.user_role === ROLE.USER) {
      req.queryInfo.filter.user_id = req.tokenInfo.payload.user_id;
    }
    req.queryInfo.filter.category = HISTORY.USER.NAME;
    req.queryInfo.filter.type_1 = HISTORY.USER.TYPES.LEVEL.NAME;

    const result = await this.controller.getShopJumpUpHistories(req.queryInfo);
    this.onSuccess(res, result);
  }

  async getShopJumpUpHistories(req: Request, res: Response) {
    const { shop_id } = req.params;
    req.queryInfo.filter.shop_id = shop_id;
    req.queryInfo.filter.category = HISTORY.SHOP.NAME;
    req.queryInfo.filter.type_1 = HISTORY.SHOP.TYPES.JUMP_UP;
    const result = await this.controller.getShopJumpUpHistories(req.queryInfo);
    this.onSuccess(res, result);
  }

  async getAllShopJumpUpHistories(req: Request, res: Response) {
    
    if (
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id
    ) {
      req.body.user_id = req.tokenInfo.payload.user_id;
    }
    req.body.user_role = req.tokenInfo.role;
    req.body.account_type = req.tokenInfo.payload.account_type;

    req.queryInfo.filter = {"$or": [{"category": HISTORY.SHOP.NAME}, {"category": HISTORY.USER.NAME}]};

    if (req.body.user_role === ROLE.USER) {
      req.queryInfo.filter.user_id = req.tokenInfo.payload.user_id;
    }
    req.queryInfo.filter.type_1 = HISTORY.SHOP.TYPES.JUMP_UP;

    const result = await this.controller.getShopJumpUpHistories(req.queryInfo);
    this.onSuccess(res, result);
  }
  getAuthListMiddlewares(): any[] {
    return [queryMiddleware.run(), authInfoMiddleware.run()];
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
