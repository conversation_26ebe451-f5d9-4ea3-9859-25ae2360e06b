import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud'
import { mentor<PERSON><PERSON>roller } from '@/controllers'
import { Request, Response } from '../base'
import {authInfoMiddleware, optionalInfoMiddleware, queryMiddleware} from '@/middlewares'
import * as _ from 'lodash'

/**
 * @swagger
 * components:
 *   schemas:
 *     Mentor:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the mentor
 *         user_id:
 *           type: string
 *           description: The ID of the user associated with this mentor
 *         bio:
 *           type: string
 *           description: Mentor biography
 *         rating:
 *           type: number
 *           description: Mentor rating score
 *         likes:
 *           type: integer
 *           description: Number of likes received
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The creation date
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The last update date
 */

/**
 * @swagger
 * tags:
 *   name: Mentors
 *   description: Mentor management and operations
 */

export default class MentorRouter extends CrudRouter<typeof mentorController> {
  constructor() {
    super(mentorController)
  }

  customRouting() {
    /**
     * @swagger
     * /mentor/set_mentor/{id}:
     *   post:
     *     summary: Set user as mentor
     *     tags: [Men<PERSON>]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: User ID to set as mentor
     *     responses:
     *       200:
     *         description: Successfully set as mentor
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.post(
      '/set_mentor/:id',
      this.createMiddlewares(),
      this.route(this.setMentor)
    )
    /**
     * @swagger
     * /mentor/like_mentor/{id}:
     *   put:
     *     summary: Like a mentor
     *     tags: [Mentors]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Mentor ID to like
     *     responses:
     *       200:
     *         description: Successfully liked mentor
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
      '/like_mentor/:id',
      this.updateMiddlewares(),
      this.route(this.likeMentor)
    )
    /**
     * @swagger
     * /mentor/unlike_mentor/{id}:
     *   put:
     *     summary: Unlike a mentor
     *     tags: [Mentors]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Mentor ID to unlike
     *     responses:
     *       200:
     *         description: Successfully unliked mentor
     *       401:
     *         $ref: '#/components/responses/UnauthorizedError'
     */
    this.router.put(
      '/unlike_mentor/:id',
      this.updateMiddlewares(),
      this.route(this.unlikeMentor)
    )
    /**
     * @swagger
     * /mentor/rank/day:
     *   get:
     *     summary: Get daily mentor rankings
     *     tags: [Mentors]
     *     parameters:
     *       - in: query
     *         name: latitude
     *         schema:
     *           type: number
     *         description: Latitude for location filtering
     *       - in: query
     *         name: longitude
     *         schema:
     *           type: number
     *         description: Longitude for location filtering
     *       - in: query
     *         name: order_option
     *         schema:
     *           type: string
     *         description: Ordering option
     *       - in: query
     *         name: distance_order
     *         schema:
     *           type: string
     *         description: Distance ordering parameter
     *     responses:
     *       200:
     *         description: List of ranked mentors
     */
    this.router.get(
      '/rank/day',
      this.route(this.rankMentor)
    )
    /**
     * @swagger
     * /mentor/get_lists:
     *   post:
     *     summary: Get filtered list of mentors
     *     tags: [Mentors]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               latitude:
     *                 type: number
     *                 description: Latitude for location filtering
     *               longitude:
     *                 type: number
     *                 description: Longitude for location filtering
     *               order_option:
     *                 type: string
     *                 description: Ordering option
     *               distance_order:
     *                 type: string
     *                 description: Distance ordering parameter
     *     responses:
     *       200:
     *         description: List of mentors
     */
    this.router.post(
        '/get_lists',
        this.getListMiddlewares(),
        this.route(this.getListV2)
    )
  }
  async getListV2(req: Request, res: Response) {
    req.params.user_id =
        req &&
        req.tokenInfo &&
        req.tokenInfo.payload &&
        req.tokenInfo.payload.user_id
    if (req && req.body && req.body.latitude) {
      req.params.latitude = req.body.latitude
    }
    if (req && req.body && req.body.longitude) {
      req.params.longitude = req.body.longitude
    }
    if (req && req.body && req.body.order_option) {
      req.params.order_option = req.body.order_option
    }
    if (req && req.body && req.body.distance_order) {
      req.params.distance_order = req.body.distance_order
    }
    const result = await this.controller.getListV2(req.params, req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  async setMentor(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const {id} = req.params
    const result = await this.controller.setMentor(req.body, {
      filter: {id},
    })
    this.onSuccess(res, result)
  }

  async likeMentor(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const {id} = req.params
    const result = await this.controller.likeMentor(req.body, {
      filter: {id},
    })
    this.onSuccess(res, result)
  }
  async unlikeMentor(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const {id} = req.params
    const result = await this.controller.unlikeMentor(req.body, {
      filter: {id},
    })
    this.onSuccess(res, result)
  }

  /**
   * @swagger
   * /mentor:
   *   get:
   *     summary: Get all mentors
   *     tags: [Mentors]
   *     parameters:
   *       - $ref: '#/components/parameters/PageParam'
   *       - $ref: '#/components/parameters/PageSizeParam'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - in: query
   *         name: latitude
   *         schema:
   *           type: number
   *         description: Latitude for location filtering
   *       - in: query
   *         name: longitude
   *         schema:
   *           type: number
   *         description: Longitude for location filtering
   *       - in: query
   *         name: order_option
   *         schema:
   *           type: string
   *         description: Ordering option
   *       - in: query
   *         name: distance_order
   *         schema:
   *           type: string
   *         description: Distance ordering parameter
   *     responses:
   *       200:
   *         description: List of mentors
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     objects:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/Mentor'
   */
  async getList(req: Request, res: Response) {
    const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const {latitude, longitude, order_option, distance_order} = req.query
    let {shopFilter} = req.query as any
    try {
      shopFilter = JSON.parse(shopFilter)
    } catch (ignore) {
      shopFilter = undefined
    }
    const result = await this.controller.getList({latitude, longitude, order_option, distance_order ,user_id , shopFilter},req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  /**
   * @swagger
   * /mentor/{id}:
   *   get:
   *     summary: Get mentor by ID
   *     tags: [Mentors]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Mentor ID
   *       - $ref: '#/components/parameters/IncludeParam'
   *     responses:
   *       200:
   *         description: Mentor details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     object:
   *                       $ref: '#/components/schemas/Mentor'
   *       404:
   *         description: Mentor not found
   */
  async getItem(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const {id} = req.params;
    req.queryInfo.filter.id = id
    const result = await this.controller.getItem(req.body, req.queryInfo);
    this.onSuccess(res, result);
  }

  async rankMentor(req: Request, res: Response) {
    const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const {latitude, longitude, order_option, distance_order} = req.query
    const result = await this.controller.rankMentor({latitude, longitude, order_option, distance_order, user_id});
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run() ,optionalInfoMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run(),optionalInfoMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
