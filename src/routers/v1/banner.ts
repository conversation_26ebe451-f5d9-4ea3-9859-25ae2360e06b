import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { bannerController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * tags:
 *   - name: Banners
 *     description: Banner management operations
 * 
 * components:
 *   schemas:
 *     Banner:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the banner
 *         title:
 *           type: string
 *           description: Banner title
 *         image_url:
 *           type: string
 *           description: URL to the banner image
 *         link:
 *           type: string
 *           description: Link that the banner redirects to
 *         order:
 *           type: integer
 *           description: Display order of the banner
 *         status:
 *           type: boolean
 *           description: Whether the banner is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the banner was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the banner was last updated
 *       example:
 *         id: "123e4567-e89b-12d3-a456-************"
 *         title: "Summer Sale"
 *         image_url: "https://example.com/banners/summer-sale.jpg"
 *         link: "/summer-sale"
 *         order: 1
 *         status: true
 *         createdAt: "2022-01-01T00:00:00Z"
 *         updatedAt: "2022-01-01T00:00:00Z"
 */

export default class BannerRouter extends CrudRouter<typeof bannerController> {
  constructor() {
    super(bannerController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
