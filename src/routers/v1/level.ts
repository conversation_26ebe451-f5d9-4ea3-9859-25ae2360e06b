import { <PERSON><PERSON><PERSON>out<PERSON> } from "../crud";
import { Request, Response } from "../base";
import { levelController } from "@/controllers";
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from "@/middlewares";
import * as _ from "lodash";
import { uploadToS3 } from "@/utils/uploadS3";
import * as multer from "multer";

export default class LevelRouter extends CrudRouter<typeof levelController> {
  constructor() {
    super(levelController);
  }
  /**
   * @swagger
   * components:
   *   schemas:
   *     Level:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           format: uuid
   *           example: "123e4567-e89b-12d3-a456-************"
   *         name:
   *           type: string
   *           example: "Silver"
   *         icon_url:
   *           type: string
   *           format: uri
   *           example: "https://cdn.example.com/icon/silver.png"
   *         exp_min:
   *           type: integer
   *           example: 1000
   *         exp_max:
   *           type: integer
   *           example: 4999
   *         how_to_obtain:
   *           type: array
   *           items:
   *             type: string
   *           example: ["Complete 10 posts", "Receive 20 likes"]
   *         exp_payment:
   *           type: integer
   *           example: 50
   *         point_award:
   *           type: integer
   *           example: 10
   *         bbs_access_right:
   *           type: array
   *           items:
   *             type: string
   *           example: ["general", "event", "market"]
   *         created_at:
   *           type: string
   *           format: date-time
   *         updated_at:
   *           type: string
   *           format: date-time
   */

  customRouting() {
    /**
 * @swagger
 * /level:
 *   get:
 *     summary: Get list of levels with optional filters
 *     tags: [Level]
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by level name
 *       - in: query
 *         name: exp_min
 *         schema:
 *           type: integer
 *         description: Minimum EXP value
 *       - in: query
 *         name: exp_max
 *         schema:
 *           type: integer
 *         description: Maximum EXP value
 *     responses:
 *       200:
 *         description: List of levels
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Level'

 *   post:
 *     summary: Create a new level
 *     tags: [Level]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               icon_url:
 *                 type: string
 *                 format: binary
 *               exp_min:
 *                 type: integer
 *               exp_max:
 *                 type: integer
 *               how_to_obtain:
 *                 type: array
 *                 items:
 *                   type: string
 *               exp_payment:
 *                 type: integer
 *               point_award:
 *                 type: integer
 *               bbs_access_right:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: Created level
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Level'

 
 * /level/{id}:
 *   get:
 *     summary: Get level by ID
 *     tags: [Level]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Found level
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Level'
 *       404:
 *         description: Not found
*   put:
 *     summary: Update a level
 *     tags: [Level]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               name:
 *                 type: string
 *               icon_url:
 *                 type: string
 *                 format: binary
 *               exp_min:
 *                 type: integer
 *               exp_max:
 *                 type: integer
 *               how_to_obtain:
 *                 type: array
 *                 items:
 *                   type: string
 *               exp_payment:
 *                 type: integer
 *               point_award:
 *                 type: integer
 *               bbs_access_right:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Updated level
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Level'

 *   delete:
 *     summary: Delete a level (soft delete)
 *     tags: [Level]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       204:
 *         description: Deleted
 */

    const upload = multer({ dest: "uploads/" });
    this.router.post(
      "/",
      upload.single("icon_url"),
      this.createMiddlewares(),
      this.route(this.create)
    );
    this.router.put(
      "/",
      upload.single("image_url"),
      this.updateMiddlewares(),
      this.route(this.update)
    );
  }

  async create(req: Request, res: Response) {
    if (req.file) {
      const uploadResult = await uploadToS3(req.file);
      req.body.image_url = uploadResult.url;
    }
    const result = await this.controller.create(req.body);
    this.onSuccess(res, result);
  }
  async update(req: Request, res: Response) {
    if (req.file) {
      const uploadResult = await uploadToS3(req.file);
      req.body.image_url = uploadResult.url;
    }
    const { id } = req.params;
    const result = await this.controller.update(req.body, {
      filter: { id },
    });
    this.onSuccess(res, result);
  }
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }

  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }

  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()];
  }
}
