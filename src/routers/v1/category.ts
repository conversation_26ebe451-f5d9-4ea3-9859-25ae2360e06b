import { <PERSON><PERSON>Router } from '../crud';
import { Request, Response } from '../base';
import { categoryController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';

/**
 * @swagger
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the category
 *         name:
 *           type: string
 *           description: Category name
 *         parent_id:
 *           type: string
 *           description: Parent category ID for hierarchical categories
 *         order:
 *           type: integer
 *           description: Display order of the category
 *         icon:
 *           type: string
 *           description: Icon representing the category
 *         status:
 *           type: boolean
 *           description: Whether the category is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the category was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the category was last updated
 * 
 * tags:
 *   name: Categories
 *   description: Category management operations
 */

export default class CategoryRouter extends CrudRouter<
  typeof categoryController
> {
  constructor() {
    super(categoryController);
  }

  customRouting() {
    /**
     * @swagger
     * /category/v2:
     *   get:
     *     summary: Get categories (version 2)
     *     tags: [Categories]
     *     parameters:
     *       - $ref: '#/components/parameters/PaginationLimit'
     *       - $ref: '#/components/parameters/PaginationOffset'
     *       - $ref: '#/components/parameters/FilterParam'
     *       - $ref: '#/components/parameters/SortParam'
     *     responses:
     *       200:
     *         description: List of categories
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     count:
     *                       type: integer
     *                     rows:
     *                       type: array
     *                       items:
     *                         $ref: '#/components/schemas/Category'
     */
    this.router.get(
      '/v2',
      this.getListMiddlewares(),
      this.route(this.getListV2)
    );

    /**
     * @swagger
     * /category/order-category/{id}:
     *   put:
     *     summary: Change category order by drag and drop
     *     tags: [Categories]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         required: true
     *         schema:
     *           type: string
     *         description: Category ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               order:
     *                 type: integer
     *                 description: New order for the category
     *     responses:
     *       200:
     *         description: Category order updated successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 results:
     *                   type: object
     *                   properties:
     *                     object:
     *                       $ref: '#/components/schemas/Category'
     */
    this.router.put('/order-category/:id', this.createMiddlewares(), this.route(this.dragDropCategory))
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  async getListV2(req: Request, res: Response) {
    const result = await this.controller.getListV2(req.queryInfo);
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async getList(req: Request, res: Response) {
    const employee_id = req.tokenInfo ? req.tokenInfo.payload.employee_id : undefined;
    const user_id = req.tokenInfo ? req.tokenInfo.payload.user_id : undefined;
    const result = await this.controller.getList(req.queryInfo ,employee_id,user_id)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  async dragDropCategory(req: Request, res: Response) {
    const {id} = req.params;
    const result = await this.controller.dragDropCategory(id, req.body);
    this.onSuccess(res, result);
  }
  /**
   * @swagger
   * /category:
   *   get:
   *     summary: Get a list of categories
   *     tags: [Categories]
   *     responses:
   *       200:
   *         description: List of categories
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 results:
   *                   type: object
   *                   properties:
   *                     count:
   *                       type: integer
   *                     rows:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/Category'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /category/{id}:
   *   get:
   *     summary: Get a category by ID
   *     tags: [Categories]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The category ID
   *     responses:
   *       200:
   *         description: The category details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Category'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }

  /**
   * @swagger
   * /category:
   *   post:
   *     summary: Create a new category
   *     tags: [Categories]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Category'
   *     responses:
   *       201:
   *         description: Category created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Category'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  /**
   * @swagger
   * /category/{id}:
   *   put:
   *     summary: Update a category
   *     tags: [Categories]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The category ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Category'
   *     responses:
   *       200:
   *         description: Category updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Category'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }

  /**
   * @swagger
   * /category/{id}:
   *   delete:
   *     summary: Delete a category
   *     tags: [Categories]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: The category ID to delete
   *     responses:
   *       200:
   *         description: Category deleted successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()]
  }
}
