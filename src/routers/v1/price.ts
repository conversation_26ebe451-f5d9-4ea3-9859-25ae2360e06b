import { <PERSON>rudRouter } from '../crud'
import { Request, Response } from '../base'
import { priceController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
export default class PriceRouter extends CrudRouter<typeof priceController> {
  constructor() {
    super(priceController)
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
}
