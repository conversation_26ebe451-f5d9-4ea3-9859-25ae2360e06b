import { <PERSON>rudRouter } from '../crud';
import { Request, Response } from '../base';
import { settingUserPermissionController } from '@/controllers';
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
} from '@/middlewares';
import * as _ from 'lodash';
export default class SettingUserPermissionRouter extends CrudRouter<
  typeof settingUserPermissionController
> {
  constructor() {
    super(settingUserPermissionController);
  }

  customRouting() {
    // this.router.post('/seach_video_youtube', this.route(this.seach))
    // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()];
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
  }
}
