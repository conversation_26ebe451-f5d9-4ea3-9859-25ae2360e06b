import {<PERSON>rud<PERSON>outer} from '../crud'
import {Request, Response} from '../base'
import {shopController} from '@/controllers'
import {
    authInfoMiddleware,
    queryMiddleware,
    blockMiddleware,
    superAdminTypeMiddleware,
    adminTypeMiddleware,
    checkAuthMiddleware,
    optionalInfoMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
import {ROLE} from '@/const'
import moment = require('moment')
import multer = require('multer')

const FILE_IMAGE_PATH = 'image/'
export default class ShopRouter extends CrudRouter<typeof shopController> {
    constructor() {
        super(shopController)
    }

    customRouting() {
        const storage = multer.diskStorage({
            destination: function (req: Request, file: any, cb: any) {
                cb(null, FILE_IMAGE_PATH)
            },
            filename: function (req: Request, file: any, cb: any) {
                const parts = file.originalname.split('.')
                const type = parts[parts.length - 1]
                cb(null, file.fieldname + '-' + Date.now() + '.' + type)
            },
        })
        // this.router.post('/seach_video_youtube', this.route(this.seach))
        // this.router.post('/get_youtube_comment', this.route(this.getYoutubeComment))
        this.router.post(
            '/create_shop',
            this.createMiddlewares(),
            this.route(this.createShop)
        )
        this.router.post(
            '/view_shop/:id',
            this.getItemMiddlewares(),
            this.route(this.viewShop)
        )
        this.router.put(
            '/update_expiration_date/:id',
            this.updateExpirationDateMiddlewares(),
            this.route(this.updateExpirationDateFunc)
        )
        this.router.put(
            '/like_shop/:id',
            this.updateMiddlewares(),
            this.route(this.likeShopFunc)
        )
        this.router.put(
            '/unlike_shop/:id',
            this.updateMiddlewares(),
            this.route(this.unlikeShopFunc)
        )
        this.router.post(
            '/get_lists',
            this.getFilterListMiddlewares(),
            this.route(this.getListShop)
        )
        this.router.post(
            '/get_lists_v2',
            this.getFilterListMiddlewares(),
            this.route(this.getListShop2)
        )
        this.router.post(
            '/get_master_shop',
            this.getFilterListMiddlewares(),
            this.route(this.getMasterShop)
        )
        this.router.post(
            '/approve',
            this.deleteAllMiddlewares(),
            this.route(this.approveAll)
        )
        this.router.post(
            '/reject',
            this.deleteAllMiddlewares(),
            this.route(this.rejectAll)
        )
        this.router.post(
            '/search_total',
            this.getFilterListMiddlewares(),
            this.route(this.getSearchTotal)
        )
        this.router.post(
            '/search_total_v2',
            this.getFilterListMiddlewares(),
            this.route(this.getSearchTotalV2)
        )
        this.router.post(
            '/set_recommen_shop',
            this.updateMiddlewares(),
            this.route(this.setReShop)
        )
        this.router.post(
            '/get_near_shop',
            this.getFilterListMiddlewares(),
            this.route(this.getNearShop)
        )
        this.router.post(
            '/get_order_jump_up_shop',
            this.getFilterListMiddlewares(),
            this.route(this.getNearJumpUpShop)
        )
        this.router.post(
            '/get_near_shop_map',
            this.getFilterListMiddlewares(),
            this.route(this.getNearShopMap)
        )
        this.router.put(
            '/update_expiration_date_multiple',
            this.updateExpirationDateMiddlewares(),
            this.route(this.updateExpirationDateMultiple)
        )
        this.router.put(
            '/force_expired_multiple',
            this.updateExpirationDateMiddlewares(),
            this.route(this.forceExpiredMultiple)
        )
        this.router.post(
            '/clone_shop/:id',
            this.createMiddlewares(),
            this.route(this.cloneShopFunc)
        )
        this.router.delete(
            '/hard_delete/:id',
            this.deleteMiddlewares(),
            this.route(this.hardDelete)
        )
        this.router.put(
            '/setup_jump_up/:id',
            this.updateMiddlewares(),
            this.route(this.setupJumpUpAPI)
        )
        this.router.put(
            '/turn_on_jump_up/:id',
            this.updateMiddlewares(),
            this.route(this.turnOnJumpUpAPI)
        )
        this.router.put(
            '/turn_off_jump_up/:id',
            this.updateMiddlewares(),
            this.route(this.turnOffJumpUpAPI)
        )
        this.router.put(
            '/remove_jump_up/:id',
            this.updateMiddlewares(),
            this.route(this.removeJumpUp)
        )
        this.router.put(
            '/test_change_expired_date/:id',
            this.updateMiddlewares(),
            this.route(this.testChangeExpiredDate)
        )
        this.router.get(
            '/download_excel',
            this.getFilterListMiddlewares(),
            this.route(this.downloadExcel)
        )
        const upload = multer({storage: storage})
        this.router.post(
            '/import_excel',
            this.createMiddlewares(),
            upload.single('file'),
            this.route(this.importExcel)
        )
        this.router.get(
            '/count_shop',
            this.route(this.countShop)
        )
        this.router.post('/call/:id', this.route(this.callShop))
        this.router.get('/statistic', this.updateMiddlewares(), this.route(this.statisticShop))
        this.router.put(
            '/destroy/expired',
            this.route(this.destroyShopExpired)
        ),
        this.router.get(
                '/rank/top',
                this.route(this.rankShop)
        ),
        this.router.put(
            '/update_multi_shop',
            this.updateMiddlewares(),
            this.route(this.updateMultiShop)
        )
        this.router.put(
            '/reject_multiple_pending',
            this.updateMiddlewares(),
            this.route(this.rejectMultiShop)
        )
    }

    async rankShop(req: Request, res: Response) {
        const {endDate, startDate , search_value} = req.query as any
        let {limit, page, sort, sortBy } = req.query as any
        limit = limit ? Number(limit) : 20
        page = page ? Number(page) : 1
        sort = sort || 'DESC'
        sortBy = sortBy || 'callShop'
        const result = await this.controller.rankShop({limit, page, sort, sortBy, endDate, startDate , search_value})
        this.onSuccessAsList(res, result, undefined, {
            limit,
            offset: (page - 1) * limit,
        })
    }

    async destroyShopExpired(req: Request, res: Response) {
        const result = await this.controller.destroyShopExpired()
        this.onSuccess(res, result)
    }

    async getList(req: Request, res: Response) {
        const {search_value , duplicate} = req.query
        const result = await this.controller.getList({search_value , duplicate}, req.queryInfo)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async importExcel(req: Request, res: Response) {
        console.log('@#$%^$#', req.file)
        const url = FILE_IMAGE_PATH + req.file.filename
        req.body.url = url

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.importExcel(req.body)
        this.onSuccess(res, result)
    }

    async downloadExcel(req: Request, res: Response) {
        const {id} = req.params
        const {search_value , duplicate} = req.query

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.downloadExcel({search_value , duplicate},req.queryInfo)
        // res.attachment('shop' + moment().valueOf() + '.xlsx');
        // res.send(result);
        setTimeout(() => {
            this.onSuccess(res, result)
        }, 1000 * 3)
    }

    async testChangeExpiredDate(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const {id} = req.params
        const result = await this.controller.testChangeExpiredDate(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async removeJumpUp(req: Request, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.removeJumpUp(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async turnOnJumpUpAPI(req: Request, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.turnOnJumpUpAPI(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async turnOffJumpUpAPI(req: Request, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.turnOffJumpUpAPI(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async setupJumpUpAPI(req: Request, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.setupJumpUpAPI(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async updateExpirationDateMultiple(req: any, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        ///
        if (_.has(req.query, 'items')) {
            req.query.items = JSON.parse(req.query.items) || {}
        }

        await this.validateJSON(req.query, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
            },
            required: ['items'],
            additionalProperties: false,
        })
        const {items} = req.query

        const result = await this.controller.updateExpirationDateMultiple(
            req.body,
            {
                filter: {id: {$in: items}},
            }
        )
        this.onSuccess(res, result)
    }

    async forceExpiredMultiple(req: any, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        ///
        if (_.has(req.query, 'items')) {
            req.query.items = JSON.parse(req.query.items) || {}
        }

        await this.validateJSON(req.query, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
            },
            required: ['items'],
            additionalProperties: false,
        })
        const {items} = req.query

        const result = await this.controller.forceExpiredMultiple(req.body, {
            filter: {id: {$in: items}},
        })
        this.onSuccess(res, result)
    }

    async getNearShop(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        if (req && req.body && req.body.search_value) {
            req.params.search_value = req.body.search_value
        }
        if (req && req.body && req.body.initial_shop_id) {
            req.params.initial_shop_id = req.body.initial_shop_id
        }
        if (req && req.body && req.body.categories) {
            req.params.categories = req.body.categories
        }
        if (req && req.body && req.body.tags) {
            req.params.tags = req.body.tags
        }
        const result = await this.controller.getNearShop(req.params, req.queryInfo)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async getNearJumpUpShop(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.categories) {
            req.params.categories = req.body.categories
        }
        if (req && req.body && req.body.tags) {
            req.params.tags = req.body.tags
        }
        if (req && req.body && req.body.initial_shop_id) {
            req.params.initial_shop_id = req.body.initial_shop_id
        }
        const result = await this.controller.getNearJumpUpShop(
            req.params,
            req.queryInfo
        )
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async getNearShopMap(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.distance) {
            req.params.distance = req.body.distance
        }
        if (req && req.body && req.body.radius) {
            req.params.radius = req.body.radius
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        const result = await this.controller.getNearShopMap(
            req.params,
            req.queryInfo
        )
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async setReShop(req: any, res: Response) {
        if (_.has(req.query, 'items')) {
            req.query.items = JSON.parse(req.query.items) || {}
        }
        await this.validateJSON(req.query, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
            },
            required: ['items'],
            additionalProperties: false,
        })
        const {items} = req.query
        const result = await this.controller.setReShop(
            {is_random_20_shop: req.body.is_random_20_shop},
            {
                filter: {id: {$in: items}},
            }
        )
        this.onSuccess(res, result)
    }

    async getSearchTotal(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        if (req && req.body && req.body.search_value) {
            req.params.search_value = req.body.search_value
        }
        const result = await this.controller.getSearchTotal(
            req.params,
            req.queryInfo
        )
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async getSearchTotalV2(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        if (req && req.body && req.body.search_value) {
            req.params.search_value = req.body.search_value
        }
        console.log('29148 tui search nua na')
        const result = await this.controller.getSearchTotalV2(
            req.params,
            req.queryInfo
        )
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async approveAll(req: any, res: Response) {
        if (_.has(req.query, 'items')) {
            req.query.items = JSON.parse(req.query.items) || {}
        }
        await this.validateJSON(req.query, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
            },
            required: ['items'],
            additionalProperties: false,
        })
        const {items} = req.query
        const result = await this.controller.approveAll(req.body, {
            filter: {id: {$in: items}},
        })
        this.onSuccess(res, result)
    }

    async rejectAll(req: any, res: Response) {
        if (_.has(req.query, 'items')) {
            req.query.items = JSON.parse(req.query.items) || {}
        }
        await this.validateJSON(req.query, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
            },
            required: ['items'],
            additionalProperties: false,
        })
        const {items} = req.query
        const result = await this.controller.rejectAll(req.body, {
            filter: {id: {$in: items}},
        })
        this.onSuccess(res, result)
    }

    async getListShop2(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        const result = await this.controller.getListShop2(req.params, req.queryInfo)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async getListShop(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        if (req && req.body && req.body.radius) {
            req.params.radius = req.body.radius
        }
        const result = await this.controller.getListShop(req.params, req.queryInfo)
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async getMasterShop(req: Request, res: Response) {
        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        if (req && req.body && req.body.latitude) {
            req.params.latitude = req.body.latitude
        }
        if (req && req.body && req.body.longitude) {
            req.params.longitude = req.body.longitude
        }
        if (req && req.body && req.body.order_option) {
            req.params.order_option = req.body.order_option
        }
        if (req && req.body && req.body.thema_id) {
            req.params.thema_id = req.body.thema_id
        }
        if (req && req.body && req.body.distance_order) {
            req.params.distance_order = req.body.distance_order
        }
        const result = await this.controller.getMasterShop(
            req.params,
            req.queryInfo
        )
        this.onSuccessAsList(res, result, undefined, req.queryInfo)
    }

    async likeShopFunc(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const {id} = req.params
        const result = await this.controller.likeShop(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async unlikeShopFunc(req: Request, res: Response) {
        req.body.user_id = req.tokenInfo.payload.user_id
        const {id} = req.params
        const result = await this.controller.unlikeShop(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async cloneShopFunc(req: Request, res: Response) {
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.employee_id
        ) {
            req.body.employee_id = req.tokenInfo.payload.employee_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type
        const {id} = req.params
        req.body.shop_id = id

        const result = await this.controller.cloneShop(req.body)
        this.onSuccess(res, result)
    }

    async viewShop(req: Request, res: Response) {
        const {id} = req.params

        req.params.user_id =
            req &&
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id

        req.queryInfo.filter.id = id
        const result = await this.controller.viewShop(req.params, req.queryInfo)
        this.onSuccess(res, result)
    }

    async updateExpirationDateFunc(req: Request, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type

        const result = await this.controller.updateExpirationDateFunc(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async update(req: Request, res: Response) {
        const {id} = req.params

        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
        }
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.employee_id
        ) {
            req.body.employee_id = req.tokenInfo.payload.employee_id
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type
        console.log('29148 : ShopRouter -> update -> req.body', req.body)

        const result = await this.controller.update(req.body, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async createShop(req: Request, res: Response) {
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.user_id
        ) {
            req.body.user_id = req.tokenInfo.payload.user_id
            req.body.created_by_admin = false
        }
        if (
            req.tokenInfo &&
            req.tokenInfo.payload &&
            req.tokenInfo.payload.employee_id
        ) {
            req.body.employee_id = req.tokenInfo.payload.employee_id
            req.body.created_by_admin = true
        }
        req.body.user_role = req.tokenInfo.role
        req.body.account_type = req.tokenInfo.payload.account_type
        if (req.body.user_role === ROLE.USER) {
            await this.validateJSON(req.body, {
                required: [
                    'title',
                    'description',
                    // 'min_price',
                    'opening_hours',
                    'images',
                    // 'videos',
                    'contact_phone',
                    'latitude',
                    'longitude',
                    'address',
                    'tag_ids',
                ],
            })
        } else {
            await this.validateJSON(req.body, {
                required: [
                    'title',
                    'description',
                    // 'min_price',
                    'opening_hours',
                    'images',
                    // 'videos',
                    'contact_phone',
                    'address',
                    'tag_ids',
                ],
            })
        }
        const result = await this.controller.createShop(req.body)
        this.onSuccess(res, result)
    }

    async delete(req: Request, res: Response) {
        const {id} = req.params
        req.params.account_type = req.tokenInfo.payload.account_type
        req.params.user_role = req.tokenInfo.role

        const result = await this.controller.delete(req.params, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async hardDelete(req: Request, res: Response) {
        const {id} = req.params
        req.params.account_type = req.tokenInfo.payload.account_type
        req.params.user_role = req.tokenInfo.role

        const result = await this.controller.hardDelete(req.params, {
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async countShop(req: Request, res: Response) {
        const {search_value, thema_id} = req.query
        const result = await this.controller.countShop({search_value, thema_id})
        this.onSuccess(res, result)
    }

    async callShop(req: Request, res: Response) {
        const {id} = req.params
        const result = await this.controller.callShop({
            filter: {id},
        })
        this.onSuccess(res, result)
    }

    async statisticShop(req: Request, res: Response) {
        const user_id = req.tokenInfo.payload.user_id
        const {day} = req.query
        const result = await this.controller.statisticShop(user_id, day as string)
        this.onSuccess(res, result)
    }

    async updateMultiShop(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
            },
            required: ['items'],
        })
        const result = await this.controller.updateMultiShop(req.body)
        this.onSuccess(res, result)
    }

    async rejectMultiShop(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                items: {
                    type: 'array',
                    uniqueItems: true,
                    minItems: 1,
                    items: {type: 'string'},
                },
                denied_message: { type: 'string' }
            },
            required: ['items']
        })

        const result = await this.controller.rejectMultiShop(req.body)
        this.onSuccess(res, result)
    }

    getFilterListMiddlewares(): any[] {
        return [queryMiddleware.run(), optionalInfoMiddleware.run()]
    }

    getListMiddlewares(): any[] {
        return [queryMiddleware.run()]
    }

    getItemMiddlewares(): any[] {
        return [queryMiddleware.run(), optionalInfoMiddleware.run()]
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }

    updateExpirationDateMiddlewares(): any[] {
        return [authInfoMiddleware.run()]
    }
}
