import { Request, Response, BaseRouter } from '../base'
import {
  queryMiddleware,
  authInfoMiddleware,
  blockMiddleware,
} from '@/middlewares'
import * as express from 'express'
import * as multer from 'multer'
import * as sharp from 'sharp'
import * as probe from 'probe-image-size'
import * as fs from 'fs'
import * as _ from 'lodash'
import * as uuid from 'uuid'
import * as gifsicle from 'gifsicle'
import { execFile } from 'child_process'
import { config } from '@/config'
import { errorService } from '@/services'
import { S3 } from 'aws-sdk'

const IMAGE_URL_SERVER_FOR_PRODUCTION =
  `https://${config.server.host}:9877` + '/api/v1/image/get/'

let TYPE_IMAGE = '.png'
const FILE_IMAGE_PATH = 'image/'
const KEY_CLOUDIMAGE = 'ce8391bac'
const ID = process.env.AWS_ID
const SECRET = process.env.AWS_SECRET_KEY
const BUCKET_NAME = process.env.AWS_BUCKET_NAME

export default class ImageRouter extends BaseRouter {
  router: express.Router
  constructor() {
    super()
    this.router = express.Router()
    const storage = multer.diskStorage({
      destination: function (req: Request, file: any, cb: any) {
        if (file && file.originalname && file.originalname.includes('.gif')) {
          TYPE_IMAGE = '.gif'
        } else {
          TYPE_IMAGE = '.png'
        }
        cb(null, FILE_IMAGE_PATH)
      },
      filename: function (req: Request, file: any, cb: any) {
        cb(null, file.fieldname + '-' + Date.now() + TYPE_IMAGE)
      },
    })

    const storageMultiple = multer.diskStorage({
      destination: function (req: Request, file: any, cb: any) {
        if (file && file.originalname && file.originalname.includes('.gif')) {
          TYPE_IMAGE = '.gif'
        } else {
          TYPE_IMAGE = '.png'
        }
        cb(null, FILE_IMAGE_PATH)
      },
      filename: function (req: Request, file: any, cb: any) {
        cb(null, file.fieldname + '-' + `${uuid.v4()}` + TYPE_IMAGE)
      },
    })
    const upload = multer({ storage: storage })
    const uploadMultiple = multer({ storage: storageMultiple })

    /**
     * @swagger
     * /image/get/{filename}:
     *   get:
     *     tags:
     *       - Image
     *     summary: Get image by filename
     *     description: Retrieve an image file by its filename
     *     parameters:
     *       - in: path
     *         name: filename
     *         required: true
     *         schema:
     *           type: string
     *         description: Name of the image file
     *     responses:
     *       200:
     *         description: Image retrieved successfully
     *         content:
     *           image/*:
     *             schema:
     *               type: string
     *               format: binary
     *       404:
     *         description: Image not found
     *       500:
     *         description: Server error
     */
    this.router.get('/get/:filename', this.route(this.getImage))

    /**
     * @swagger
     * /image/cloudimg/{operation}/{size}/{filter}/{filename}:
     *   get:
     *     tags:
     *       - Image
     *     summary: Get processed image via Cloudimage
     *     description: Get an image processed through Cloudimage with specified operations
     *     parameters:
     *       - in: path
     *         name: operation
     *         required: true
     *         schema:
     *           type: string
     *         description: Image operation to perform
     *       - in: path
     *         name: size
     *         required: true
     *         schema:
     *           type: string
     *         description: Desired image size
     *       - in: path
     *         name: filter
     *         required: true
     *         schema:
     *           type: string
     *         description: Filter to apply to the image
     *       - in: path
     *         name: filename
     *         required: true
     *         schema:
     *           type: string
     *         description: Original image filename
     *     responses:
     *       302:
     *         description: Redirect to processed image
     *       500:
     *         description: Server error
     */
    this.router.get('/cloudimg/:operation/:size/:filter/:filename', this.route(this.cloudimg))

    /**
     * @swagger
     * /image/upload:
     *   post:
     *     tags:
     *       - Image
     *     summary: Upload single image
     *     description: Upload a single image file and optionally resize it
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               image:
     *                 type: string
     *                 format: binary
     *                 description: Image file to upload
     *     responses:
     *       200:
     *         description: Image uploaded successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: object
     *                   properties:
     *                     url:
     *                       type: string
     *                     newFileName:
     *                       type: string
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post('/upload', this.updateImageMiddlewares(), upload.single('image'), this.route(this.updateAvatar))

    /**
     * @swagger
     * /image/upload/{size}:
     *   post:
     *     tags:
     *       - Image
     *     summary: Upload and resize image
     *     description: Upload an image and resize it to specified dimensions
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: size
     *         required: true
     *         schema:
     *           type: integer
     *         description: Target size for the image
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               image:
     *                 type: string
     *                 format: binary
     *                 description: Image file to upload
     *     responses:
     *       200:
     *         description: Image uploaded and resized successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: object
     *                   properties:
     *                     url:
     *                       type: string
     *                     newFileName:
     *                       type: string
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post('/upload/:size', upload.single('image'), this.route(this.updateAvatar))

    /**
     * @swagger
     * /image/get_image_from_s3/{filename}:
     *   get:
     *     tags:
     *       - Image
     *     summary: Get image from S3
     *     description: Retrieve an image stored in AWS S3 by filename
     *     parameters:
     *       - in: path
     *         name: filename
     *         required: true
     *         schema:
     *           type: string
     *         description: Name of the file in S3
     *     responses:
     *       200:
     *         description: Image retrieved successfully
     *         content:
     *           image/*:
     *             schema:
     *               type: string
     *               format: binary
     *       404:
     *         description: Image not found
     *       500:
     *         description: Server error
     */
    this.router.get('/get_image_from_s3/:filename', this.route(this.getFromS3))

    /**
     * @swagger
     * /image/upload_multiple/{size}/{high_size}:
     *   post:
     *     tags:
     *       - Image
     *     summary: Upload multiple images
     *     description: Upload multiple images and create both low and high quality versions
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: size
     *         required: true
     *         schema:
     *           type: integer
     *         description: Size for low quality version
     *       - in: path
     *         name: high_size
     *         required: true
     *         schema:
     *           type: integer
     *         description: Size for high quality version
     *     requestBody:
     *       required: true
     *       content:
     *         multipart/form-data:
     *           schema:
     *             type: object
     *             properties:
     *               images:
     *                 type: array
     *                 items:
     *                   type: string
     *                   format: binary
     *                 description: Image files to upload
     *     responses:
     *       200:
     *         description: Images uploaded successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: object
     *                   properties:
     *                     low_quality_images:
     *                       type: array
     *                       items:
     *                         type: object
     *                         properties:
     *                           url:
     *                             type: string
     *                           file_name:
     *                             type: string
     *                     high_quality_images:
     *                       type: array
     *                       items:
     *                         type: object
     *                         properties:
     *                           url:
     *                             type: string
     *                           file_name:
     *                             type: string
     *       401:
     *         description: Unauthorized
     *       500:
     *         description: Server error
     */
    this.router.post('/upload_multiple/:size/:high_size', uploadMultiple.array('images', 10), this.route(this.updateAvatarMultiple))
  }
  async getFromS3(req: Request, res: Response) {
    const s3 = new S3({
      accessKeyId: ID,
      secretAccessKey: SECRET,
    })
    const filename = req.params && req.params.filename
    const getTypeImage =
      filename && filename.includes('.gif') ? 'image/gif' : 'image/png'

    s3.getObject({ Bucket: BUCKET_NAME, Key: `${filename}` })
      .promise()
      .then((data: any) => {
        res.writeHead(200, { 'Content-Type': getTypeImage })
        res.end(data.Body)
        return
      })
      .catch((err: any) => {
        throw err
      })
  }
  async updateAvatarMultiple(req: Request, res: Response) {
    const s3 = new S3({
      accessKeyId: ID,
      secretAccessKey: SECRET,
    })
    const high_quality_images: any = []
    const low_quality_images: any = []
    try {
      const dataSource: any = req.files
      for (let i = 0; i < req.files.length; i++) {
        let lowFilename = dataSource[i].filename
        let highFilename = dataSource[i].filename

        const high_size = req.params.high_size
          ? parseInt(req.params.high_size)
          : 1920
        const size = req.params.size ? parseInt(req.params.size) : 300
        lowFilename =
          'resized-' +
          size +
          '-' +
          dataSource[i].filename.substring(
            0,
            dataSource[i].filename.lastIndexOf('.')
          ) +
          '.webp'
        highFilename =
          'resized-' +
          high_size +
          '-' +
          dataSource[i].filename.substring(
            0,
            dataSource[i].filename.lastIndexOf('.')
          ) +
          '.webp'
        await this.resizeImage(
          dataSource[i].path,
          FILE_IMAGE_PATH + lowFilename,
          size
        )
        await this.resizeImage(
          dataSource[i].path,
          FILE_IMAGE_PATH + highFilename,
          high_size
        )
        const fileContentHigh = fs.readFileSync(FILE_IMAGE_PATH + highFilename)
        const fileContentLow = fs.readFileSync(FILE_IMAGE_PATH + lowFilename)

        await s3
          .upload({
            Bucket: BUCKET_NAME,
            Key: lowFilename,
            Body: fileContentLow,
            ACL: 'public-read',
          })
          .promise()
        await s3
          .upload({
            Bucket: BUCKET_NAME,
            Key: highFilename,
            Body: fileContentHigh,
            ACL: 'public-read',
          })
          .promise()

        const urls3High = `https://${process.env.IMAGE_URL}/${highFilename}`
        const urls3Low = `https://${process.env.IMAGE_URL}/${lowFilename}`

        if (dataSource[i].path.includes('.gif')) {
          await Promise.all([
            new Promise((resolve, reject) => {
              fs.unlink(
                `${FILE_IMAGE_PATH}${highFilename.substring(
                  0,
                  highFilename.lastIndexOf('.')
                )}.gif`,
                (err) => {
                  err ? reject(err) : resolve('')
                }
              )
            }),
            new Promise((resolve, reject) => {
              fs.unlink(
                `${FILE_IMAGE_PATH}${lowFilename.substring(
                  0,
                  lowFilename.lastIndexOf('.')
                )}.gif`,
                (err) => {
                  err ? reject(err) : resolve('')
                }
              )
            }),
          ])
        }

        await Promise.all([
          new Promise((resolve, reject) => {
            fs.unlink(FILE_IMAGE_PATH + highFilename, (err) => {
              err ? reject(err) : resolve('')
            })
          }),

          new Promise((resolve, reject) => {
            fs.unlink(FILE_IMAGE_PATH + lowFilename, (err) => {
              err ? reject(err) : resolve('')
            })
          }),
          new Promise((resolve, reject) => {
            fs.unlink(dataSource[i].path, (err) => {
              err ? reject(err) : resolve('')
            })
          }),
        ])

        high_quality_images.push({ url: urls3High, file_name: highFilename })
        low_quality_images.push({ url: urls3Low, file_name: lowFilename })
      }
      this.onSuccess(res, { low_quality_images, high_quality_images })
    } catch (error) {
      console.log('29148 upload multiple error', error)
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
    return
  }

  async updateAvatar(req: Request, res: Response) {
    try {
      const s3 = new S3({
        accessKeyId: ID,
        secretAccessKey: SECRET,
        apiVersion: '2006-03-01',
      })
      const fileName = req.file.filename
      const filePath = FILE_IMAGE_PATH + fileName
      const size = req.params.size
      let newFileName = ''
      if (size) {
        newFileName = fileName.substring(0, fileName.lastIndexOf('.')) + '.webp'
        const newFilePath = FILE_IMAGE_PATH + newFileName
        await this.resizeImage(filePath, newFilePath, parseInt(size))
      } else {
        newFileName = await this.toWebp(filePath)
      }
      const fileContent = await new Promise((resolve, reject) => {
        fs.readFile(FILE_IMAGE_PATH + newFileName, (err, data) =>
          err ? reject(err) : resolve(data)
        )
      })

      await s3
        .upload({
          Bucket: BUCKET_NAME,
          Key: newFileName,
          Body: fileContent,
          ACL: 'public-read',
          // ContentType: 'image/webp',
        })
        .promise()
      const urls3 = `https://${process.env.IMAGE_URL}/${newFileName}`

      await Promise.all([
        new Promise((resolve, reject) => {
          fs.unlink(filePath.toString(), (err) => {
            err ? reject(err) : resolve('')
          })
        }),
        new Promise((resolve, reject) => {
          fs.unlink(`${FILE_IMAGE_PATH}${newFileName}`, (err) =>
            err ? reject(err) : resolve('')
          )
        }),
      ])

      this.onSuccess(res, { url: urls3, newFileName })
    } catch (error) {
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
    return
  }

  resizeImage(
    originalFilePath: string,
    newFilePath: string,
    maxSize: number
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        sharp.cache(false)
        const input = require('fs').createReadStream(originalFilePath)
        await probe(input).then(async (result: any) => {
          let newWidth = maxSize
          let newHeight = maxSize
          if (result.width > result.height) {
            if (result.width < newWidth) {
              newWidth = result.width
            }
            newHeight = Math.round((newWidth * result.height) / result.width)
          } else {
            if (result.height < newHeight) {
              newHeight = result.height
            }
            newWidth = Math.round((newHeight * result.width) / result.height)
          }
          if (originalFilePath && originalFilePath.includes('.gif')) {
            const gifPath =
              newFilePath.substring(0, newFilePath.lastIndexOf('.')) + '.gif'
            await new Promise((resolve, reject) => {
              execFile(
                gifsicle,
                [
                  '--resize-fit-width',
                  `${maxSize}`,
                  '-o',
                  gifPath,
                  originalFilePath,
                ],
                (err: any) => {
                  err ? reject(err) : resolve('')
                }
              )
            })
            const newFilePathV2: any = await new Promise((resolve, reject) => {
              fs.readFile(gifPath, (err, data) =>
                err ? reject(err) : resolve(data)
              )
            })
            sharp(newFilePathV2, { pages: -1 })
              .webp()
              .toFile(newFilePath, (err: any) => {
                if (err) {
                  return reject(err)
                }
                console.error('resize image successfully')
                resolve(true)
              })
          } else {
            sharp(originalFilePath, { pages: -1 })
              .resize(newWidth, newHeight)
              .webp()
              .toFile(newFilePath, (err: any) => {
                if (err) {
                  return reject(err)
                }
                console.error('resize image successfully')
                resolve(true)
              })
          }
          input.destroy()
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  async cloudimg(req: Request, res: Response) {
    const { operation, size, filter, filename } = req.params
    const imageUrl = IMAGE_URL_SERVER_FOR_PRODUCTION
    const UrlImage =
      'https://' +
      KEY_CLOUDIMAGE +
      '.cloudimg.io/' +
      operation +
      '/' +
      size +
      '/' +
      filter +
      '/' +
      imageUrl +
      filename
    res.redirect(UrlImage)
  }

  async toWebp(filePath: any) {
    const filename = filePath.includes('/')
      ? filePath.substring(filePath.lastIndexOf('/') + 1)
      : filePath
    const newFileName =
      filename.substring(0, filename.lastIndexOf('.')) + '.webp'

    await sharp(filePath, { pages: -1 })
      .webp()
      .toFile(`${FILE_IMAGE_PATH}/${newFileName}`)
    return newFileName
  }
  async getImage(req: Request, res: Response) {
    const { filename } = req.params
    try {
      fs.exists(FILE_IMAGE_PATH + filename, function (exists) {
        if (exists) {
          fs.readFile(FILE_IMAGE_PATH + filename, function (err, data) {
            if (err) {
              console.log('!!! error reading file')
              res.writeHead(500, { 'Content-Type': 'application/json' })
              res.write(`{
                              "code": 500,
                              "type": "database_exception_query_fail",
                              "message": "Image does not exist"
                          }`)
              res.end()
            } else {
              res.writeHead(200, { 'Content-Type': 'image/png' })
              res.end(data)
            }
          })
        } else {
          res.writeHead(500, { 'Content-Type': 'application/json' })
          res.write(`{
                      "code": 500,
                      "type": "database_exception_query_fail",
                      "message": "Image does not exist"
                  }`)
          res.end()
        }
      })
    } catch (error) {
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
    return
  }

  updateImageMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return []
  }
  deleteAllMiddlewares(): any[] {
    return [blockMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [blockMiddleware.run()]
  }
}
