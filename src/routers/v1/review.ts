import { CrudRouter } from '../crud'
import { Request, Response } from '../base'
import { reviewController } from '@/controllers'
import {
  authInfoMiddleware,
  queryMiddleware,
  blockMiddleware,
  superAdminTypeMiddleware,
  adminTypeMiddleware,
  optionalInfoMiddleware,
} from '@/middlewares'
import * as _ from 'lodash'
import { ROLE, USER_TYPE } from '@/const'
export default class ReviewRouter extends CrudRouter<typeof reviewController> {
  constructor() {
    super(reviewController)
  }

  customRouting() {
    this.router.delete(
      '/soft_delete_all',
      this.deleteAllMiddlewares(),
      this.route(this.softDeleteAllFunc)
    )
    this.router.delete(
      '/soft_delete/:id',
      this.deleteMiddlewares(),
      this.route(this.softDeleteFunc)
    )
    this.router.get(
      '/get_list_include_block',
      this.getFilterListMiddlewares(),
      this.route(this.getListIncludeBlock)
    )
    this.router.get(
      '/get_lists',
      this.getFilterListMiddlewares(),
      this.route(this.getListReview)
    )
    this.router.get(
      '/get_list_for_shop',
      this.getFilterListMiddlewares(),
      this.route(this.getListForShop)
    )
    this.router.get(
      '/get_list_admin',
      this.getListByAdminMiddlewares(),
      this.route(this.getListByAdmin)
    )
    this.router.put(
      '/report/:id',
      this.updateMiddlewares(),
      this.route(this.reportFunc)
    )
    this.router.put(
      '/unreport/:id',
      this.updateMiddlewares(),
      this.route(this.unreportFunc)
    )
    this.router.put(
        '/like_review/:id',
        this.updateMiddlewares(),
        this.route(this.likeReviewFunc)
    );
    this.router.put(
        '/unlike_review/:id',
        this.updateMiddlewares(),
        this.route(this.unlikeReviewFunc)
    );
    this.router.put(
        '/dislike_review/:id',
        this.updateMiddlewares(),
        this.route(this.dislikeReviewFunc)
    );
    this.router.put(
        '/undislike_review/:id',
        this.updateMiddlewares(),
        this.route(this.undislikeReviewFunc)
    );
    this.router.get('/count/user', [authInfoMiddleware.run()], this.route(this.countByUser))
  }
  async unreportFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const result = await this.controller.unreportFunc(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async reportFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    const { id } = req.params
    const result = await this.controller.reportFunc(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }

  async softDeleteFunc(req: Request, res: Response) {
    const result = await this.controller.softDeleteOne(req.params)
    this.onSuccess(res, result)
  }

  async softDeleteAllFunc(req: Request, res: Response) {
    const user_id =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id

    const result = await this.controller.softDeleteAll(user_id, {
      status: false,
    })
    this.onSuccess(res, result)
  }

  async getListByAdmin(req: Request, res: Response) {
    const result = await this.controller.getListByAdmin(req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  async create(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id
    if (req.body.recruit_id) {
      await this.validateJSON(req.body, {
        required: ['content', 'recruit_id'],
      })
    } else if (req.body.post_id) {
      await this.validateJSON(req.body, {
        required: ['content', 'post_id'],
      })
    } else {
      await this.validateJSON(req.body, {
        required: ['content', 'shop_id'],
      })
    }
    const result = await this.controller.create(req.body)
    this.onSuccess(res, result)
  }

  async getListReview(req: Request, res: Response) {
    req.params.user_id =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id

    const role =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.role

    if (
      role !== ROLE.OPERATOR &&
      role !== ROLE.ADMIN &&
      role !== ROLE.SUPERADMIN
    ) {
      if (!req.queryInfo.filter) {
        req.queryInfo.filter = {}
      }
      req.queryInfo.filter.status = true
    }

    const result = await this.controller.getListFilter(
      req.params,
      req.queryInfo
    )
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  async getListIncludeBlock(req: Request, res: Response) {
    req.params.user_id =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id

    const result = await this.controller.getListIncludeBlock(
      req.params,
      req.queryInfo
    )
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  async getListForShop(req: Request, res: Response) {
    req.params.user_id =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.user_id

    const role =
      req &&
      req.tokenInfo &&
      req.tokenInfo.payload &&
      req.tokenInfo.payload.role

    if (
      role !== ROLE.OPERATOR &&
      role !== ROLE.ADMIN &&
      role !== ROLE.SUPERADMIN
    ) {
      if (!req.queryInfo.filter) {
        req.queryInfo.filter = {}
      }
      req.queryInfo.filter.status = true
    }

    const result = await this.controller.getListForShop(
      req.params,
      req.queryInfo
    )
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }

  async dislikeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.dislikeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async undislikeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.undislikeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async likeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.likeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async unlikeReviewFunc(req: Request, res: Response) {
    req.body.user_id = req.tokenInfo.payload.user_id;
    const {id} = req.params;
    const result = await this.controller.unlikeReview(req.body, {
      filter: {id},
    });
    this.onSuccess(res, result);
  }

  async countByUser(req: Request, res: Response) {
    const user_id = req.tokenInfo.payload.user_id;
    const result = await this.controller.countByUser(user_id);
    this.onSuccess(res, result);
  }

  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  getFilterListMiddlewares(): any[] {
    return [queryMiddleware.run(), optionalInfoMiddleware.run()]
  }
  getListByAdminMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
}
