import * as express from 'express'
import * as _ from 'lodash'
import { BaseRouter, Request, Response } from './base'
import { errorService } from '@/services'
import { CrudController } from '@/controllers'
import { authInfoMiddleware, queryMiddleware } from '@/middlewares'

/**
 * @swagger
 * components:
 *   parameters:
 *     IdParam:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *       description: Resource ID
 *     PaginationLimit:
 *       in: query
 *       name: limit
 *       schema:
 *         type: integer
 *         default: 10
 *       description: Maximum number of items to return
 *     PaginationOffset:
 *       in: query
 *       name: offset
 *       schema:
 *         type: integer
 *         default: 0
 *       description: Number of items to skip
 *     FilterParam:
 *       in: query
 *       name: filter
 *       schema:
 *         type: string
 *       description: JSON string for filtering results
 *     SortParam:
 *       in: query
 *       name: sort
 *       schema:
 *         type: string
 *       description: Field to sort by (prefix with - for descending order)
 */

export class CrudRouter<T extends CrudController<any>> extends BaseRouter {
  constructor(controller: T) {
    super()
    this.controller = controller
    this.router = express.Router()
    this.customRouting()
    this.defaultRouting()
  }
  controller: T
  router: express.Router
  
  /**
   * Setup default CRUD routes
   * Each of these routes will be documented via Swagger
   */
  defaultRouting() {
    this.router.get('/', this.getListMiddlewares(), this.route(this.getList))
    this.router.get('/:id', this.getItemMiddlewares(), this.route(this.getItem))
    this.router.post('/', this.createMiddlewares(), this.route(this.create))
    this.router.put('/:id', this.updateMiddlewares(), this.route(this.update))
    this.router.delete(
      '/:id',
      this.deleteMiddlewares(),
      this.route(this.delete)
    )
    this.router.delete(
      '/',
      this.deleteAllMiddlewares(),
      this.route(this.deleteAll)
    )
  }
  
  customRouting() {}
  
  checkGetMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  
  /**
   * @swagger
   * /{resource}:
   *   get:
   *     summary: Get a list of resources
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *     responses:
   *       200:
   *         description: A list of resources
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     type: object
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  getListMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  
  async getList(req: Request, res: Response) {
    const result = await this.controller.getList(req.queryInfo)
    this.onSuccessAsList(res, result, undefined, req.queryInfo)
  }
  
  /**
   * @swagger
   * /{resource}/{id}:
   *   get:
   *     summary: Get a resource by ID
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Resource details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *       404:
   *         description: Resource not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  getItemMiddlewares(): any[] {
    return [queryMiddleware.run()]
  }
  
  async getItem(req: Request, res: Response) {
    const { id } = req.params
    req.queryInfo.filter.id = id
    const result = await this.controller.getItem(req.queryInfo)
    this.onSuccess(res, result)
  }
  
  /**
   * @swagger
   * /{resource}:
   *   post:
   *     summary: Create a new resource
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       201:
   *         description: Resource created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *       400:
   *         description: Invalid input
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  createMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  
  async create(req: Request, res: Response) {
    const result = await this.controller.create(req.body)
    this.onSuccess(res, result)
  }
  
  /**
   * @swagger
   * /{resource}/{id}:
   *   put:
   *     summary: Update a resource
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Resource updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *       400:
   *         description: Invalid input
   *       404:
   *         description: Resource not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  updateMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  
  async update(req: Request, res: Response) {
    const { id } = req.params
    const result = await this.controller.update(req.body, {
      filter: { id },
    })
    this.onSuccess(res, result)
  }
  
  /**
   * @swagger
   * /{resource}/{id}:
   *   delete:
   *     summary: Delete a resource
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/IdParam'
   *     responses:
   *       200:
   *         description: Resource deleted successfully
   *       404:
   *         description: Resource not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  
  async delete(req: Request, res: Response) {
    const { id } = req.params
    const result = await this.controller.delete({
      filter: { id },
    })
    this.onSuccess(res, result)
  }
  
  /**
   * @swagger
   * /{resource}:
   *   delete:
   *     summary: Delete multiple resources
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: items
   *         required: true
   *         schema:
   *           type: string
   *         description: JSON array of IDs to delete
   *     responses:
   *       200:
   *         description: Resources deleted successfully
   *       400:
   *         description: Invalid input
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  deleteAllMiddlewares(): any[] {
    return [authInfoMiddleware.run()]
  }
  
  async deleteAll(req: any, res: Response) {
    if (_.has(req.query, 'items')) {
      req.query.items = JSON.parse(req.query.items) || {}
    }
    await this.validateJSON(req.query, {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          uniqueItems: true,
          minItems: 1,
          items: { type: 'string' },
        },
      },
      required: ['items'],
      additionalProperties: false,
    })
    const { items } = req.query
    const result = await this.controller.deleteAll({
      filter: { id: { $in: items } },
    })
    this.onSuccess(res, result)
  }
}
