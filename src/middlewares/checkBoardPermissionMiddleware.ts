import { BaseMiddleware } from './base'
import { errorService } from '@/services'
import { User, BoardPermission } from '@/models'
import * as express from 'express'
import { Request, Response } from '@/routers/base'

export class CheckBoardPermissionMiddleware extends BaseMiddleware {
  constructor(private permissionType: 'read' | 'write' = 'read') {
    super()
  }

  async use(req: Request, res: Response, next: express.NextFunction): Promise<void> {
    const userId = req.user?.id
    const boardId = req.params.board_id || req.body.board_id || req.query.board_id

    if (!userId || !boardId) {
      throw errorService.router.badRequest()
    }

    const user = await User.findByPk(userId, {
      attributes: ['level_id'],
    })

    if (!user || !user.level_id) {
      throw errorService.database.recordNotFound()
    }

    const whereClause: any = {
      board_id: boardId,
      level_id: user.level_id,
    }

    if (this.permissionType === 'read') {
      whereClause.can_read = true
    } else {
      whereClause.can_write = true
    }

    const allowed = await BoardPermission.findOne({ where: whereClause })

    if (!allowed) {
      throw errorService.database.recordNotFound()
    }

    next()
  }
}
