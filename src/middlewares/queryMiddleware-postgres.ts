import { BaseMiddleware } from './base'
import * as express from 'express'
import { config } from '@/config'
import { ICrudOption } from '@/services'
import { Request, Response } from '@/routers/base'
import * as _ from 'lodash'
export class QueryMiddleware extends BaseMiddleware {
  async use(req: any, res: Response, next: express.NextFunction) {
    const filter = this._parseFilter(req)
    const order = this._parseOrder(req)
    const page = req.query['page'] && !isNaN(Number(req.query['page'])) ?  Number(req.query['page']) : 1
    const limit = req.query['limit'] && !isNaN(Number(req.query['limit'])) ?  Number(req.query['limit']) : config.database.defaultPageSize
    const offset = parseInt(req.query['offset']) || (page - 1) * limit
    const fields = this._parseFields(req)

    if (fields.attributes != undefined) {
      fields.attributes = _.union(['id', 'updated_at'], fields.attributes)
    }

    // const originalUrl = req.originalUrl;
    // if (originalUrl.includes('/api/v1/shop?') || originalUrl.includes('/api/v1/shop/?') || originalUrl.includes('/api/v1/shop/get')) {
    //   //  /api/v1/shop? ==> default get list api of SHOP
    //   //  /api/v1/shop/get ==> all api get list of SHOP

    //   fields.attributes = {
    //     exclude: ['description', 'old_shop.description', 'denied_shop.description', 'params_update.description']
    //   }
    //   fields.scope = { method: ['excludeDescription', 'excludeOldShop', 'excludeDeniedShop', 'excludeParamsUpdate'] };
    // }
    // if (originalUrl.includes('%20{%22shop%22:%20[%22$all')) {
    //   const newIncludes = fields.include.map((e: any) => {
    //     if (e.association === 'shop') {
    //       fields.attributes = {
    //         exclude: ['description', 'old_shop.description', 'denied_shop.description', 'params_update.description']
    //       }
    //       e.scope = { method: ['excludeDescription', 'excludeOldShop', 'excludeDeniedShop', 'excludeParamsUpdate'] };
    //     }
    //     return e;
    //   });
    //   fields.include = newIncludes;
    // }

    req.queryInfo = _.merge(
        {
          filter,
          limit,
          page,
          offset,
          order,
        },
        fields
    )
    next()
  }
  /**
   * Filter param only accept <and> query. <or> will be supported later
   * Format: [[key, operator, value], [key, operator, value]]
   */
  _parseFilter(req: any): any {
    let filter = req.query['filter']
    try {
      filter = JSON.parse(filter)
    } catch (ignore) {
      filter = undefined
    }
    return filter || {}
  }
  /**
   * Format: [[key, order], [key, order]]
   */
  _parseOrder(req: any): any {
    let order = req.query['order']
    try {
      order = JSON.parse(order)
    } catch (ignore) {
      order = undefined
    }
    return order || [['updated_at', 'desc']]
  }
  _parseFields(req: any): any {
    let fields = req.query['fields']
    try {
      fields = JSON.parse(fields)
    } catch (ignore) {
      fields = []
    }
    try {
      return this._parseAttribute(fields)
    } catch (err) {
      return null
    }
  }
  _parseAttribute(attrs: any) {
    const attributes: any[] = []
    const includes: any[] = []
    let isGetAll = false
    let isSetParanoid = false
    let isSeparate = false
    let isRequire = false
    let where: any = undefined
    let order: any = undefined
    let limit: any = undefined
    _.forEach(attrs, function (f) {
      if (typeof f === 'string') {
        switch (f) {
          case '$all':
            isGetAll = true
            break
          case '$paranoid':
            isSetParanoid = true
            break
          case '$separate':
            isSeparate = true
            break
          case '$require':
            isRequire = true
            break
          default:
            attributes.push(f)
        }
      } else if (typeof f === 'object' && !Array.isArray(f)) {
        _.forEach(
            f,
            ((value: any, name: string) => {
              switch (name) {
                case '$filter':
                  where = _.merge({}, where, value)
                  break
                case '$order':
                  order = value
                  break
                case '$limit':
                  limit = value
                  break
                default:
                  includes.push({
                    [name]: value,
                  })
              }
            }).bind(this)
        )
      }
    })
    const include = this._parseInclude(includes)
    const result: any = {
      include: include,
      distinct: includes ? true : false,
    }
    if (where) result.where = where
    if (order) result.order = order
    if (limit) result.limit = limit
    if (!isGetAll) {
      result.attributes = attributes
    }
    if (isSetParanoid) {
      result.paranoid = false
    }
    if (isSeparate) {
      result.separate = true
    }
    if (isRequire) {
      result.required = true
    }
    // result.order = [["created_at_unix_timestamp","DESC"]]
    return result
  }

  _parseInclude(includes: any) {
    if (includes.length === 0) return includes

    const associates: any[] = []
    _.forEach(
        includes,
        ((i: any) => {
          _.forEach(
              i,
              ((attrs: any, name: string) => {
                const associate = Object.assign(
                    {
                      association: name,
                    },
                    this._parseAttribute(attrs)
                )
                associates.push(associate)
              }).bind(this)
          )
        }).bind(this)
    )
    return associates
  }
}
