import { BaseMiddleware } from './base'
import { errorService } from '@/services'
import * as express from 'express'
import { Request, Response } from '@/routers/base'

export class CheckUserModeMiddleware extends BaseMiddleware {
  async use(req: Request, res: Response, next: express.NextFunction) {
    const mode = req.body?.mode

    if (!mode) {
      throw errorService.auth.permissionDeny('Mode is required in request body')
    }

    if (mode !== 'user') {
      throw errorService.auth.permissionDeny('Only users can perform this action')
    }

    return next()
  }
}
export class CheckExpertModeMiddleware extends BaseMiddleware {
    async use(req: Request, res: Response, next: express.NextFunction) {
      const mode = req.body?.mode
  
      if (!mode) {
        throw errorService.auth.permissionDeny('Mode is required in request body')
      }
  
      if (mode !== 'expert') {
        throw errorService.auth.permissionDeny('Only Experts can perform this action')
      }
  
      return next()
    }
  }