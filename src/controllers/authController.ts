import { BaseController } from './baseController'
import * as _ from 'lodash'
import * as admin from 'firebase-admin'
import { tokenService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: Authentication endpoints
 */

export class AuthController extends BaseController {
    constructor() {
        super()
    }

    /**
     * @swagger
     * /auth/login:
     *   post:
     *     summary: Authenticate a user and get access token
     *     tags: [Auth]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - firebaseUserInfo
     *             properties:
     *               firebaseUserInfo:
     *                 type: object
     *                 description: Firebase decoded ID token
     *     responses:
     *       200:
     *         description: Authentication successful
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 accessToken:
     *                   type: string
     *                   description: JWT access token
     *       401:
     *         description: Authentication failed
     */
    async login(params: { firebaseUserInfo: admin.auth.DecodedIdToken }) {
        // Tra ve token
        const token = await tokenService.getAdminToken()
        return { 
            accessToken: token 
        }
    }
}