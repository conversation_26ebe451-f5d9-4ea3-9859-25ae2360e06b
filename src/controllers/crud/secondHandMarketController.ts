import { <PERSON>rudController } from '../crudController';
import {ICrudOption, secondHandMarketService} from '@/services';
export class SecondHandMarketController extends CrudController<typeof secondHandMarketService> {
  constructor() {
    super(secondHandMarketService);
  }

  async call(option?: ICrudOption) {
    return await this.service.call(option)
  }

  async likeProduct(params: any, option?: ICrudOption) {
    return await this.service.likeProduct(params, option)
  }
  async unlikeProduct(params: any, option?: ICrudOption) {
    return await this.service.unlikeProduct(params, option)
  }

  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option);
  }
  async getItem(params: any, option?: ICrudOption) {
    return await this.service.getItem(params, option);
  }

  async updateState(id : string ,state : string , user_id : string) {
    return this.service.updateState(id, state, user_id)
  }
  async count(user_id?: string , thema_id?: string) {
    return this.service.count(user_id , thema_id)
  }
  async putItemOnTop(id: string) {
    return this.service.putItemOnTop(id)
  }
}
