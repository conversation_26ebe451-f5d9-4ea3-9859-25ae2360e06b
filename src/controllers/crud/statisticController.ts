import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, statisticService } from '@/services';

export class StatisticController extends CrudController<
  typeof statisticService
> {
  constructor() {
    super(statisticService);
  }
  //   async statisticNewMember(params: any) {
  //     return await this.service.statisticNewMember(params);
  //   }
  async statisticDashboardChart(params: any) {
    return await this.service.statisticDashboardChart(params);
  }
  async statisticVisitor(params: any) {
    return await this.service.statisticVisitor(params);
  }
  async statisticVisitorPageView(params: any) {
    return await this.service.statisticVisitorPageView(params);
  }
  async statisticTraffic(params: any) {
    return await this.service.statisticTraffic(params);
  }
  async statisticByPeriod(params: any) {
    return await this.service.statisticByPeriod(params);
  }

  //   async statisticHourlyVisitor(params: any, option: ICrudOption) {
  //     return await this.service.statisticHourlyVisitor(params, option);
  //   }
  //   async statisticDashboard(params: any) {
  //     return await this.service.statisticDashboard(params);
  //   }
}
