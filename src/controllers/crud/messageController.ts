import { CrudController } from '../crudController'
import { messageService, ICrudOption } from '@/services'

export class MessageController extends CrudController<typeof messageService> {
  constructor() {
    super(messageService)
  }

  async getListByConversationId(params: string, option?: ICrudOption) {
    return await this.service.getListByConversationId(params, option)
  }

  async unreadMessage(params: { user_id: string , type : any }) {
    return await this.service.unreadMessage(params)
  }
}
