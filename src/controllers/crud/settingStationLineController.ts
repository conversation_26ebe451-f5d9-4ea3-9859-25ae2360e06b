import { CrudController } from '../crudController'
import {settingStationLineService} from '@/services'
export class SettingStationLineController extends CrudController<typeof settingStationLineService> {
    constructor() {
        super(settingStationLineService)
    }
    async importExcel(params: any) {
        return await this.service.importExcel(params)
    }
    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}