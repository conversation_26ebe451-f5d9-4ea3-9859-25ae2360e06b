import { CrudController } from '../crudController'
import { ICrudOption, popupWindowService } from '@/services'
export class PopupWindowController extends CrudController<typeof popupWindowService> {
    constructor() {
        super(popupWindowService)
    }
    async getListV2(option?: ICrudOption) {
        return await this.service.getListV2(option)
    }
    async updateOrder(params: any, option?: ICrudOption) {
        return await this.service.updateOrder(params, option)
    }
}