import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, favoriteJobService } from '@/services'
export class FavoriteJobController extends CrudController<
  typeof favoriteJobService
> {
  constructor() {
    super(favoriteJobService)
  }
  async createFavorite(params: any, option?: ICrudOption) {
    return this.service.createFavorite(params, option)
 }
 async removeFavorite(params: any, option?: ICrudOption) {
  return this.service.removeFavorite(params, option)
}
}
