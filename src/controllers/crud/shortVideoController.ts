import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, shortVideoService } from '@/services'
export class ShortVideoController extends CrudController<
  typeof shortVideoService
> {
  constructor() {
    super(shortVideoService)
  }

  async settingIndex(params: any, option?: ICrudOption) {
    return await this.service.settingIndex(params, option)
  }

  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option)
  }

  async likeShortVideo(params: any, option?: ICrudOption) {
    return await this.service.likeShortVideo(params, option)
  }

  async unlikeShortVideo(params: any, option?: ICrudOption) {
    return await this.service.unlikeShortVideo(params, option)
  }
}
