import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, linkService } from '@/services';
export class LinkController extends CrudController<typeof linkService> {
  constructor() {
    super(linkService);
  }

  async delete(params: any, option?: ICrudOption) {
    return await this.service.delete(params, option);
  }
  async dragDropLink(id : string , params : any){
    return this.service.dragDropLink(id,params)
  }
  async getList(option?: ICrudOption ,employee_id ? : string , user_id?:string) {
    return await this.service.getList(option,employee_id,user_id)
  }
}
