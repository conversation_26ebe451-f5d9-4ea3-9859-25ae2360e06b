import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, notificationService } from '@/services';
export class NotificationController extends <PERSON>rudController<
  typeof notificationService
> {
  constructor() {
    super(notificationService);
  }

  async deleteAllUserNoti(params: any) {
    return await this.service.deleteAllUserNoti(params);
  }

  async viewNotification(id: string) {
    return this.service.update({ is_view: true }, { filter: { id } })
  }
  async updateMultiView(option: ICrudOption, is_view: boolean){
    return this.service.updateMultiView(option, is_view);
  }
}
