import { <PERSON>rudController } from '../crudController'
import { ICrudOption, jobRequestService } from '@/services'
export class JobRequestController extends CrudController<typeof jobRequestService> {
    constructor() {
        super(jobRequestService)
    }
    async getListV2(params: any, option?: ICrudOption) {
        return this.service.getListV2(params, option)
     }
    async getListCountCategory(params: any, option?: ICrudOption) {
        return this.service.getListCountCategory(params, option)
    }
}