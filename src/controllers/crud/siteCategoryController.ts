import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, siteCategoryService } from '@/services'
export class SiteCategoryController extends CrudController<typeof siteCategoryService> {
    constructor() {
        super(siteCategoryService)
    }
    async updateSiteCategory(params: any) {
       return this.service.updateSiteCategory(params)
    }

    async delete(params: any,option?: ICrudOption) {
        return await this.service.delete(params,option)
    }

    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}