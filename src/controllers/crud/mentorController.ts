import { <PERSON>rudController } from '../crudController'
import { ICrudOption, mentorService } from '@/services'

export class MentorController extends <PERSON>rudController<typeof mentorService> {
  constructor() {
    super(mentorService)
  }
  async setMentor(params: any, option?: ICrudOption) {
    return await this.service.setMentor(params, option)
  }
  async likeMentor(params: any, option?: ICrudOption) {
    return await this.service.likeMentor(params, option)
  }
  async unlikeMentor(params: any, option?: ICrudOption) {
    return await this.service.unlikeMentor(params, option)
  }

  async getList(params: any,option?: ICrudOption) {
    return await this.service.getList(params,option)
  }
  async getItem(params: any, option?: ICrudOption) {
    return await this.service.getItem(params, option,true);
  }
  async rankMentor(params:any){
    return this.service.rankMentor(params);
  }
  async getListV2(params: any, option?: ICrudOption) {
    return await this.service.getListV2(params, option);
  }
}
