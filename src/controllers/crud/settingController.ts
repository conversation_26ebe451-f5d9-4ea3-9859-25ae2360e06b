import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, settingService } from '@/services';
export class SettingController extends CrudController<typeof settingService> {
  constructor() {
    super(settingService);
  }
  async geoCode (address: string) {
    return this.service.geoCode(address)
  }
  async sendSms(content: string) {
    return this.service.sendSms(content)
  }

  async blogsPerPage(params: {desktop?: number, mobile?: number}) {
    return this.service.blogsPerPage(params);
  }
}
