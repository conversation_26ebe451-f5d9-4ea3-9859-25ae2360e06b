import { <PERSON><PERSON><PERSON>ontroller } from '../crudController';
import { ICrudOption, errorService, commentService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Comments
 *   description: Comment management and operations
 */

export class CommentController extends <PERSON>rud<PERSON>ontroller<typeof commentService> {
  constructor() {
    super(commentService);
  }

  /**
   * @swagger
   * /comments:
   *   get:
   *     summary: Get list of comments
   *     tags: [Comments]
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - in: query
   *         name: post_id
   *         schema:
   *           type: string
   *         description: Filter comments by post ID
   *       - in: query
   *         name: user_id
   *         schema:
   *           type: string
   *         description: Filter comments by user ID
   *       - in: query
   *         name: parent_id
   *         schema:
   *           type: string
   *         description: Filter comments by parent comment ID (for replies)
   *     responses:
   *       200:
   *         description: List of comments
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Comment'
   *                 count:
   *                   type: integer
   */
  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option);
  }

  /**
   * @swagger
   * /comments/admin:
   *   get:
   *     summary: Get list of comments for admin
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *     responses:
   *       200:
   *         description: List of comments for admin
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Comment'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getListAdmin(option?: ICrudOption) {
    return await this.service.getListAdmin(option);
  }

  /**
   * @swagger
   * /comments/admin:
   *   post:
   *     summary: Create a comment as admin
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - content
   *               - post_id
   *             properties:
   *               content:
   *                 type: string
   *                 description: Comment text content
   *               post_id:
   *                 type: string
   *                 description: The post ID to comment on
   *               parent_id:
   *                 type: string
   *                 description: Parent comment ID for replies
   *               image:
   *                 type: string
   *                 description: URL to image attached to the comment
   *     responses:
   *       201:
   *         description: Comment created successfully
   *       400:
   *         description: Invalid input
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async createByAdmin(params: any, option?: ICrudOption){
    return this.service.createByAdmin(params,option)
  }

  /**
   * @swagger
   * /comments/admin/list:
   *   get:
   *     summary: Get filtered list of comments for admin
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - in: query
   *         name: post_id
   *         schema:
   *           type: string
   *         description: Filter comments by post ID
   *       - in: query
   *         name: user_id
   *         schema:
   *           type: string
   *         description: Filter comments by user ID
   *       - in: query
   *         name: status
   *         schema:
   *           type: boolean
   *         description: Filter by comment status
   *     responses:
   *       200:
   *         description: Filtered list of comments for admin
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Comment'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getListByAdmin(params: any, option?: ICrudOption) {
    return await this.service.getListByAdmin(params, option);
  }

  /**
   * @swagger
   * /comments/{id}/like:
   *   post:
   *     summary: Like a review comment
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Comment ID
   *     responses:
   *       200:
   *         description: Comment liked successfully
   *       404:
   *         description: Comment not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async likeReview(params: any, option?: ICrudOption) {
    return await this.service.likeReview(params, option);
  }

  /**
   * @swagger
   * /comments/{id}/unlike:
   *   post:
   *     summary: Unlike a review comment
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Comment ID
   *     responses:
   *       200:
   *         description: Comment unliked successfully
   *       404:
   *         description: Comment not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async unlikeReview(params: any, option?: ICrudOption) {
    return await this.service.unlikeReview(params, option);
  }

  /**
   * @swagger
   * /comments/{id}/dislike:
   *   post:
   *     summary: Dislike a review comment
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Comment ID
   *     responses:
   *       200:
   *         description: Comment disliked successfully
   *       404:
   *         description: Comment not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async dislikeReview(params: any, option?: ICrudOption) {
    return await this.service.dislikeReview(params, option);
  }

  /**
   * @swagger
   * /comments/{id}/undislike:
   *   post:
   *     summary: Remove dislike from a review comment
   *     tags: [Comments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Comment ID
   *     responses:
   *       200:
   *         description: Dislike removed successfully
   *       404:
   *         description: Comment not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async undislikeReview(params: any, option?: ICrudOption) {
    return await this.service.undislikeReview(params, option);
  }
}
