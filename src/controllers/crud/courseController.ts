import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, courseService } from '@/services'
export class CourseController extends CrudController<typeof courseService> {
  constructor() {
    super(courseService)
  }
  async setRecommended(params: any, option?: ICrudOption) {
    return await this.service.setRecommended(params, option)
  }
  async setCourses(params: any, option?: ICrudOption) {
    return await this.service.setCourses(params, option)
  }
}
