import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, groupService } from '@/services'
export class GroupController extends CrudController<typeof groupService> {
    constructor() {
        super(groupService)
    }
    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }

    async delete(params: any,option?: ICrudOption) {
        return await this.service.delete(params,option)
    }
}