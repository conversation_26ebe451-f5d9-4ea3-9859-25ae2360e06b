import { CrudController } from '../crudController'
import { keywordService} from '@/services'

export class KeywordController extends CrudController<typeof keywordService> {
  constructor() {
    super(keywordService)
  }
  async importExcel(params: any , query: any) {
    return await this.service.importExcel(params , query)
  }
  async dragDropItem(id: string, params: any) {
    return this.service.dragDropItem(id ,params)
  }
  async downloadExcel() {
    return await this.service.downloadExcel()
  }
}
