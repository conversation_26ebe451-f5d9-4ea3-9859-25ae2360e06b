import { <PERSON>rud<PERSON>ontroller } from '../crudController'
import { pointProductHistoryService, ICrudOption } from '@/services'

export class PointProductHistoryController extends CrudController<
  typeof pointProductHistoryService
> {
  constructor() {
    super(pointProductHistoryService)
  }

  async confirm(id: string) {
    return this.service.confirm(id)
  }

  async sent(id: string) {
    return this.service.sent(id)
  }

  async downloadExcel(option?: ICrudOption) {
    return await this.service.downloadExcel(option)
  }

  async count(filter:ICrudOption['filter']) {
    return this.service.count(filter)
  }
}
