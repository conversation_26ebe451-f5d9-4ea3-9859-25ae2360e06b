import { <PERSON><PERSON><PERSON><PERSON>roller } from '../crudController'
import { ICrudOption, errorService, userService } from '@/services'

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the user
 *         email:
 *           type: string
 *           description: The user email
 *         password:
 *           type: string
 *           description: The user password
 *         phone:
 *           type: string
 *           description: The user phone number
 *         username:
 *           type: string
 *           description: The username
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The creation date of the user account
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The last update date of the user account
 *         isActive:
 *           type: boolean
 *           description: Whether the user account is active
 */

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management and operations
 */

export class UserController extends CrudController<typeof userService> {
  constructor() {
    super(userService)
  }

  /**
   * @swagger
   * /users:
   *   get:
   *     summary: Get all users
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Maximum number of users to return
   *       - in: query
   *         name: offset
   *         schema:
   *           type: integer
   *           default: 0
   *         description: Number of users to skip
   *     responses:
   *       200:
   *         description: List of users
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/User'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /users/{id}:
   *   get:
   *     summary: Get user by ID
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: User ID
   *     responses:
   *       200:
   *         description: User details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/User'
   *       404:
   *         description: User not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /users/login:
   *   post:
   *     summary: Login user
   *     tags: [Users]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - email
   *               - password
   *             properties:
   *               email:
   *                 type: string
   *               password:
   *                 type: string
   *     responses:
   *       200:
   *         description: Login successful
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 token:
   *                   type: string
   *                   description: JWT token
   *                 user:
   *                   $ref: '#/components/schemas/User'
   *       401:
   *         description: Invalid credentials
   */
  async findUser(option?: ICrudOption) {
    return await this.service.findUser(option)
  }

  /**
   * @swagger
   * /users/roll-gift:
   *   post:
   *     summary: Roll gift for user
   *     tags: [Users]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               userId:
   *                 type: string
   *     responses:
   *       200:
   *         description: Gift rolled successfully
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async reward(params: any, option?: ICrudOption) {
    return await this.service.reward(params, option)
  } 

  async doRollGift(params: any) {
    return await this.service.doRollGift(params)
  }

  async doSecretJobFunc(params: any, option?: ICrudOption) {
    return await this.service.doSecretJobFunc(params, option)
  }
  async testChangeExpiredDate(params: any, option?: ICrudOption) {
    return await this.service.testChangeExpiredDate(params, option)
  }
  async delete(params: any, option?: ICrudOption) {
    return await this.service.delete(params, option)
  }
  async requestFacebookDelete(params: any, option?: ICrudOption) {
    return await this.service.requestFacebookDelete(params)
  }

  /**
   * @swagger
   * /users/check-phone:
   *   post:
   *     summary: Check if phone number exists
   *     tags: [Users]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - phone
   *             properties:
   *               phone:
   *                 type: string
   *                 description: Phone number to check
   *     responses:
   *       200:
   *         description: Check result
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 exists:
   *                   type: boolean
   */
  async checkExistPhoneNumber(params: any) {
    return await this.service.checkExistPhoneNumber(params)
  }

  async sendNotification(params: any, option?: ICrudOption) {
    return await this.service.sendNotification(params, option)
  }
  async changePassword(params: any) {
    return await this.service.changePassword(params)
  }
  async getPassword(params: any) {
    return await this.service.getPassword(params)
  }
  async checkLoginWithGoogle(params: any, option?: ICrudOption) {
    return await this.service.checkLoginWithGoogle(params, option)
  }
  async checkLoginWithFacebook(params: any, option?: ICrudOption) {
    return await this.service.checkLoginWithFacebook(params, option)
  }
  async checkLogin(params: any, option?: ICrudOption) {
    return await this.service.checkLogin(params, option)
  }

  async checkLoginBiz(params: any, option?: ICrudOption) {
    return await this.service.checkLoginBiz(params, option)
  }

  async changeLanguage(params: any, option?: ICrudOption) {
    return await this.service.updateJumpLimitAPI(params, option)
  }
  async subscribeToTopic(params: any) {
    return await this.service.subscribeToTopic(params)
  }
  async unsubscribeFromTopic(params: any) {
    return await this.service.unsubscribeFromTopic(params)
  }

  async syncImagesUrl(params: any) {
    return await this.service.syncImagesUrl(params)
  }
  async syncPostLimitAndPostStateCounter(params: any) {
    return await this.service.syncPostLimitAndPostStateCounter()
  }
  async loginWithNaver(params: any, option?: ICrudOption) {
    return await this.service.checkLoginWithNaver(params, option)
  }
  async checkLoginWithKakaotalk(params: any, option?: ICrudOption) {
    return await this.service.checkLoginWithKakaotalk(params, option)
  }
  async checkLoginWithApple(params: any, option?: ICrudOption) {
    return await this.service.checkLoginWithApple(params, option)
  }
  async getLocationInfoFunc(params: any) {
    return await this.service.getLocationInfoFunc(params)
  }
  async getPlacesInfoFunc(params: any) {
    return await this.service.getPlacesInfoFunc(params)
  }
  async updateJumpLimitAPI(params: any, option?: ICrudOption) {
    return await this.service.updateJumpLimitAPI(params, option)
  }
  async updateNoticeMessengerStatus(params: any, options: ICrudOption) {
    return await this.service.updateNoticeMessengerStatus(params, options)
  }

  async referralCode(user_id: string) {
    return await this.service.referralCode(user_id)
  }
  async createWithReferral(params: any, option?: ICrudOption) {
    return this.service.createWithReferral(params, option)
  }

  async findUserByPhone(phone: string) {
    return await this.service.findUserByPhone(phone)
  }
  async countUser(group_id ? : string) {
    return await this.service.countUser(group_id)
  }
  async getList(params : any,option?: ICrudOption) {
    return await this.service.getList(params,option)
  }

  /**
   * @swagger
   * /users/send-otp:
   *   post:
   *     summary: Send OTP to phone number
   *     tags: [Users]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - phone
   *             properties:
   *               phone:
   *                 type: string
   *                 description: Phone number to send OTP
   *     responses:
   *       200:
   *         description: OTP sent successfully
   *       400:
   *         description: Invalid phone number
   */
  async sendOtp(phone: string) {
    return await this.service.sendOtp(phone)
  }

  /**
   * @swagger
   * /users/verify-otp:
   *   post:
   *     summary: Verify OTP code
   *     tags: [Users]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - phone
   *               - otp
   *             properties:
   *               phone:
   *                 type: string
   *                 description: Phone number
   *               otp:
   *                 type: string
   *                 description: OTP code
   *     responses:
   *       200:
   *         description: OTP verified successfully
   *       400:
   *         description: Invalid OTP
   */
  async verifyOtp(phone: string , otp : string) {
    return await this.service.verifyOtp(phone,otp)
  }

  async countActivity(user_id : string){
    return await this.service.countActivity(user_id)
  }

  async updateAutoApproveStatus(params: any, options: ICrudOption) {
    return await this.service.updateAutoApproveStatus(params, options)
  }

  async updateReservationStatus(params: any, options: ICrudOption) {
    return await this.service.updateReservationStatus(params, options)
  }
  async checkReferralCode(code: string) {
    return await this.service.checkReferralCode(code)
  }

  async checkUserByPhoneAndUsername(phone: string, username: string , secret_answer: string) {
    return await this.service.checkUserByPhoneAndUsername(phone, username , secret_answer)
  }

  async changePasswordWithPhoneAndUsername(params: any){
    return await this.service.changePasswordWithPhoneAndUsername(params)
  }
}
