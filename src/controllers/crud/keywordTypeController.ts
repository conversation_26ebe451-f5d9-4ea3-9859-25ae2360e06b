import { <PERSON>rud<PERSON>ontroller } from '../crudController'
import { ICrudOption, errorService, keywordTypeService } from '@/services'
export class KeywordTypeController extends CrudController<typeof keywordTypeService> {
    constructor() {
        super(keywordTypeService)
    }
    async updateKeywordType(params: any) {
       return this.service.updateKeywordType(params)
    }

    async delete(params: any,option?: ICrudOption) {
        return await this.service.delete(params,option)
    }

    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}