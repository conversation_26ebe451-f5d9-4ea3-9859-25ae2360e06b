import { <PERSON>rud<PERSON>ontroller } from '../crudController'
import {ICrudOption, pointProductService} from '@/services'

export class PointProductController extends CrudController<
  typeof pointProductService
> {
  constructor() {
    super(pointProductService)
  }

  async buyPointProduct(user_id: string, id: string) {
    return this.service.buyPointProduct(user_id, id)
  }
  async count(filter:ICrudOption['filter']) {
    return this.service.count(filter)
  }
}
