import { CrudController } from '../crudController'
import { ICrudOption, errorService, seoSsrService } from '@/services'

export class SeoSsrController extends CrudController<typeof seoSsrService> {
  constructor() {
    super(seoSsrService)
  }

  async get() {
    return await this.service.get()
  }

  async update(params: any) {
    return await this.service.update(params)
  }
  getSeo(province: string, district: string, thema_id: string) {
    return this.service.getSeo(province, district, thema_id)
  }
}
