import { CrudController } from '../crudController'
import {settingDistrictService} from '@/services'
export class SettingDistrictController extends CrudController<typeof settingDistrictService> {
    constructor() {
        super(settingDistrictService)
    }
    async importExcel(params: any) {
        return await this.service.importExcel(params)
    }
    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}