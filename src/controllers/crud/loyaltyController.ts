import { CrudController } from '../crudController'
import { loyaltyService, ICrudOption } from '@/services'

export class LoyaltyController extends CrudController<typeof loyaltyService> {
  constructor() {
    super(loyaltyService)
  }

  async useVoucher(params: any, option?: ICrudOption) {
    return await this.service.useVoucher(params, option)
  }

  async getSearchList(params: any, option?: ICrudOption) {
    return await this.service.getSearchList(params, option)
  }
}
