import {<PERSON>rudController} from '../crudController';
import {ICrudOption, errorService, shopService} from '@/services'; //
export class ShopController extends CrudController<typeof shopService> {
    constructor() {
        super(shopService);
    }

    async downloadExcel(params : any , option?: ICrudOption) {
        return await this.service.downloadExcel(params,option)
    }

    async importExcel(params: any) {
        return await this.service.importExcel(params)
    }

    async setReShop(params: any, option?: ICrudOption) {
        return await this.service.setReShop(params, option);
    }

    async approveAll(params: any, option?: ICrudOption) {
        return await this.service.approveAll(params, option);
    }

    async rejectAll(params: any, option?: ICrudOption) {
        return await this.service.rejectAll(params, option);
    }

    async getListShop(params: any, option?: ICrudOption) {
        return await this.service.getListShop(params, option);
    }

    async getListShop2(params: any, option?: ICrudOption) {
        return await this.service.getListShop(params, option);
    }

    async getNearShop(params: any, option?: ICrudOption) {
        return await this.service.getNearShop(params, option);
    }

    async getNearJumpUpShop(params: any, option?: ICrudOption) {
        return await this.service.getNearJumpUpShop(params, option);
    }

    async getNearShopMap(params: any, option?: ICrudOption) {
        return await this.service.getNearShopMap(params, option);
    }

    async getSearchTotal(params: any, option?: ICrudOption) {
        return await this.service.getSearchTotal(params, option);
    }

    async getSearchTotalV2(params: any, option?: ICrudOption) {
        return await this.service.getSearchTotalV2(params, option);
    }

    async getMasterShop(params: any, option?: ICrudOption) {
        return await this.service.getMasterShop(params, option);
    }

    async createShop(params: any) {
        return await this.service.createShop(params);
    }

    async viewShop(params: any, option?: ICrudOption) {
        return await this.service.viewShop(params, option);
    }

    async delete(params: any, option?: ICrudOption) {
        return await this.service.delete(params, option);
    }

    async hardDelete(params: any, option?: ICrudOption) {
        return await this.service.hardDelete(params, option);
    }

    async likeShop(params: any, option?: ICrudOption) {
        return await this.service.likeShop(params, option);
    }

    async unlikeShop(params: any, option?: ICrudOption) {
        return await this.service.unlikeShop(params, option);
    }

    async setupJumpUpAPI(params: any, option?: ICrudOption) {
        return await this.service.setupJumpUpAPI(params, option);
    }

    async turnOnJumpUpAPI(params: any, option?: ICrudOption) {
        return await this.service.turnOnJumpUpAPI(params, option);
    }

    async removeJumpUp(params: any, option?: ICrudOption) {
        return await this.service.removeJumpUp(params, option);
    }

    async turnOffJumpUpAPI(params: any, option?: ICrudOption) {
        return await this.service.turnOffJumpUpAPI(params, option);
    }

    async updateExpirationDateFunc(params: any, option?: ICrudOption) {
        return await this.service.updateExpirationDateFunc(params, option);
    }

    async testChangeExpiredDate(params: any, option?: ICrudOption) {
        return await this.service.testChangeExpiredDate(params, option);
    }

    async updateExpirationDateMultiple(params: any, option?: ICrudOption) {
        return await this.service.updateExpirationDateMultiple(params, option);
    }

    async forceExpiredMultiple(params: any, option?: ICrudOption) {
        return await this.service.forceExpiredMultiple(params, option);
    }

    async cloneShop(params: any) {

        return await this.service.cloneShop(params);
    }

    async countShop(params: any) {
        return this.service.countShop(params)
    }

    async callShop(option?: ICrudOption) {
        return await this.service.callShop(option)
    }

    async statisticShop(user_id: string, day: string) {
        return this.service.statisticShop(user_id, day)
    }

    async getItem(option?: ICrudOption) {
        return await this.service.getItem(option, true)
    }

    async getList(params: any, option?: ICrudOption) {
        return await this.service.getList(params, option)
    }

    async destroyShopExpired() {
        return this.service.destroyShopExpired()
    }

    async rankShop(option: {
        limit?:  number,
        page?:  number,
        sort?: 'ASC' | 'DESC',
        sortBy?: 'callShop' | 'viewShop' | 'bookShop',
        startDate?: string,
        endDate?: string,
        search_value?: string,
    }) {
        return this.service.rankShop(option)
    }

    //updateMultiShop
    async updateMultiShop(params: any) {
        return await this.service.updateMultiShop(params);
    }
    async rejectMultiShop(params: any) {
        return await this.service.rejectMultiShop(params);
    }
}
