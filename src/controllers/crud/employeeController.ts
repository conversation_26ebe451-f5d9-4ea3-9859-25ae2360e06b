import { <PERSON>rud<PERSON>ontroller } from '../crudController'
import { ICrudOption, errorService, employeeService } from '@/services'
export class EmployeeController extends CrudController<typeof employeeService> {
    constructor() {
        super(employeeService)
    }
    async getPassword(params: any) {
        return await this.service.getPassword(params)
    }
    async checkUsername(params: any, option?: ICrudOption) {
        return await this.service.checkUsername(params, option)
    }
    async checkLogin(params: any, option?: ICrudOption) {
        return await this.service.checkLogin(params, option)
    }
    async findAdminByPhone(phone: string) {
        return await this.service.findAdminByPhone(phone)
    }
    async sendOtp(phone: string) {
        return await this.service.sendOtp(phone)
    }
    async verifyOtp(phone: string , otp : string) {
        return await this.service.verifyOtp(phone,otp)
    }
    // async createAccount(params: ICreateAccountEmployee){
    //     return await this.service.createAccount(params);
    // }

}
