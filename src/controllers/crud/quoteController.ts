import { <PERSON>rudController } from '../crudController'
import { ICrudOption, quoteService } from '@/services'
export class QuoteController extends CrudController<typeof quoteService> {
  constructor() {
    super(quoteService);
  }
  async getListV2(params: any, option?: ICrudOption) {
    return this.service.getListV2(params, option);
  }
  async toggleConfirmQuote(params: any, option?: ICrudOption) {
    return this.service.toggleConfirmQuote(params, option);
  }
  async getListRelated(params: any, option?: ICrudOption) {
    return this.service.getListRelated(params, option);
  }
}
