import { <PERSON>rudController } from '../crudController'
import { ICrudOption, siteService } from '@/services'

export class SiteController extends CrudController<typeof siteService> {
  constructor() {
    super(siteService)
  }
  async dragDropItem(id: string, params: any) {
    return this.service.dragDropItem(id ,params)
  }
  async getItem(option?: ICrudOption , count = false , user_id?: string) {
    return this.service.getItem(option , count , user_id)
  }
  async getList(option?: ICrudOption , isAdmin = false) {
    return await this.service.getList(option , isAdmin)
  }
}
