import { <PERSON>rud<PERSON>ontroller } from '../crudController'
import { ICrudOption, errorService, reservationService } from '@/services'
export class ReservationController extends <PERSON>rud<PERSON>ontroller<
  typeof reservationService
> {
  constructor() {
    super(reservationService)
  }

  async createReserve(params: any, option?: ICrudOption) {
    return await this.service.createReserve(params, option)
  }
  async updateReserveStatus(params: any, option?: ICrudOption) {
    return await this.service.updateReserveStatus(params, option)
  }
  async deleteReserve(params: any, option?: ICrudOption) {
    return await this.service.deleteReserve(params, option)
  }
  async deleteByTypeReserve(params: any, option?: ICrudOption) {
    return await this.service.deleteByTypeReserve(params, option)
  }
  async countByState(option?: ICrudOption) {
    return await this.service.countByState(option)
  }
  async getShopRanking(option?: ICrudOption) {
    return await this.service.getShopRanking(option)
  }
  async feedbackAvailable(params: any) {
    return await this.service.feedbackAvailable(params)
  }
  async getList(option?: ICrudOption , search_value ?: string) {
    return await this.service.getList(option,search_value)
  }
}
