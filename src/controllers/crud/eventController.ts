import { <PERSON>rudController } from '../crudController'
import { ICrudOption, eventService } from '@/services'
export class EventController extends CrudController<typeof eventService> {
  constructor() {
    super(eventService)
  }

  async createEvent(params: any) {
    return await this.service.createEvent(params)
  }
  async delete(params: any, option?: ICrudOption) {
    return await this.service.delete(params, option)
  }

  async getListEvent(params: any, option?: ICrudOption) {
    return await this.service.getListEvent(params, option)
  }
}
