import { <PERSON><PERSON><PERSON>ontroller } from '../crudController';
import { ICrudOption, errorService, postService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Posts
 *   description: Post management and operations
 */

export class PostController extends CrudController<typeof postService> {
  constructor() {
    super(postService);
  }

  /**
   * @swagger
   * /posts/{id}/view:
   *   post:
   *     summary: Increment view count for a post
   *     tags: [Posts]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: View count incremented successfully
   *       404:
   *         description: Post not found
   */
  async viewPost(params: any, option?: ICrudOption) {
    return await this.service.viewPost(params, option);
  }

  /**
   * @swagger
   * /posts/list:
   *   get:
   *     summary: Get filtered list of posts
   *     tags: [Posts]
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Maximum number of posts to return
   *       - in: query
   *         name: offset
   *         schema:
   *           type: integer
   *           default: 0
   *         description: Number of posts to skip
   *       - in: query
   *         name: category_id
   *         schema:
   *           type: string
   *         description: Filter by category ID
   *       - in: query
   *         name: user_id
   *         schema:
   *           type: string
   *         description: Filter by user ID
   *     responses:
   *       200:
   *         description: List of posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Post'
   *                 count:
   *                   type: integer
   */
  async getListPost(params: any, option?: ICrudOption) {
    return await this.service.getListPost(params, option);
  }

  /**
   * @swagger
   * /posts:
   *   get:
   *     summary: Get list of posts
   *     tags: [Posts]
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *     responses:
   *       200:
   *         description: List of posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Post'
   *                 count:
   *                   type: integer
   */
  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option);
  }

  /**
   * @swagger
   * /posts/{id}:
   *   delete:
   *     summary: Delete a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Post deleted successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async delete(params: any, option?: ICrudOption) {
    return await this.service.delete(params, option);
  }

  /**
   * @swagger
   * /posts/{id}/like:
   *   post:
   *     summary: Like a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Post liked successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async likePost(params: any, option?: ICrudOption) {
    return await this.service.likePost(params, option);
  }

  /**
   * @swagger
   * /posts/{id}/unlike:
   *   post:
   *     summary: Unlike a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Post unliked successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async unlikePost(params: any, option?: ICrudOption) {
    return await this.service.unlikePost(params, option);
  }

  /**
   * @swagger
   * /posts/{id}/report:
   *   post:
   *     summary: Report a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               reason:
   *                 type: string
   *                 description: Reason for reporting
   *     responses:
   *       200:
   *         description: Post reported successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async reportFunc(params: any, option?: ICrudOption) {
    return await this.service.reportFunc(params, option);
  }

  /**
   * @swagger
   * /posts/{id}/unreport:
   *   post:
   *     summary: Remove report from a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Report removed successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async unreportFunc(params: any, option?: ICrudOption) {
    return await this.service.unreportFunc(params, option);
  }

  /**
   * @swagger
   * /posts/{id}/dislike:
   *   post:
   *     summary: Dislike a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Post disliked successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async dislikePost(params: any, option?: ICrudOption) {
    return await this.service.dislikePost(params, option);
  }

  /**
   * @swagger
   * /posts/{id}:
   *   get:
   *     summary: Get a post by ID
   *     tags: [Posts]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Post details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Post'
   *       404:
   *         description: Post not found
   */
  async getItem(params: any, option?: ICrudOption) {
    return await this.service.getItem(params, option);
  }

  /**
   * @swagger
   * /posts/{id}/undislike:
   *   post:
   *     summary: Remove dislike from a post
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Post ID
   *     responses:
   *       200:
   *         description: Dislike removed successfully
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async undislikePost(params: any, option?: ICrudOption) {
    return await this.service.undislikePost(params, option);
  }

  /**
   * @swagger
   * /posts/admin:
   *   post:
   *     summary: Create a post as admin
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/Post'
   *     responses:
   *       201:
   *         description: Post created successfully
   *       400:
   *         description: Invalid input
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async createByAdmin(params: any, option?: ICrudOption){
    return this.service.createByAdmin(params,option)
  }

  /**
   * @swagger
   * /posts/admin:
   *   get:
   *     summary: Get list of posts for admin
   *     tags: [Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *     responses:
   *       200:
   *         description: List of posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Post'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getListByAdmin(params: any, option?: ICrudOption) {
    return await this.service.getListByAdmin(params, option);
  }

  /**
   * @swagger
   * /posts/rank:
   *   get:
   *     summary: Get ranked posts
   *     tags: [Posts]
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Maximum number of posts to return
   *     responses:
   *       200:
   *         description: List of ranked posts
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/Post'
   */
  async rankPost(params: any) {
    return await this.service.rankPost(params);
  }

  /**
   * @swagger
   * /posts/rank/v2:
   *   get:
   *     summary: Get ranked posts (version 2)
   *     tags: [Posts]
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Maximum number of posts to return
   *     responses:
   *       200:
   *         description: List of ranked posts
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/Post'
   */
  async rankPostV2(params: any) {
    return await this.service.rankPostV2(params);
  }


  /**
   * @swagger
   * /post/count/user:
   *   get:
   *     summary: Count posts by user
   *     tags: [Posts]
   *     responses:
   *       200:
   *         description: Count of posts by the specified user
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 totalPost:
   *                   type: integer
   */


  async countByUser(user_id: string) {
    return await this.service.countByUser(user_id);
  }
}
