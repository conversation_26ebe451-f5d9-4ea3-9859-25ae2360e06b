import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, faqCategoryService } from '@/services'
export class FaqCategoryController extends CrudController<typeof faqCategoryService> {
    constructor() {
        super(faqCategoryService)
    }
    async updateFagCategory(params: any) {
       return this.service.updateFagCategory(params)
    }

    async delete(params: any,option?: ICrudOption) {
        return await this.service.delete(params,option)
    }
}