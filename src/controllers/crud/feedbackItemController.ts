import { feedbackItemService } from '@/services'
import { CrudController } from '../crudController'

export class FeedbackItemController extends CrudController<
	typeof feedbackItemService
> {
	constructor() {
		super(feedbackItemService)
	}

	async bulkCreate(params: any) {
		return await this.service.bulkCreate(params)
	}

	async themaFeedbackItem(params: any) {
		return await this.service.themaFeedbackItem(params)
	}
}
