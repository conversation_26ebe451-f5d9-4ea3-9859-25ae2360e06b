import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, keywordCategoryService } from '@/services'
export class KeywordCategoryController extends CrudController<typeof keywordCategoryService> {
    constructor() {
        super(keywordCategoryService)
    }
    async updateKeywordCategory(params: any) {
       return this.service.updateKeywordCategory(params)
    }

    async delete(params: any,option?: ICrudOption) {
        return await this.service.delete(params,option)
    }

    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}