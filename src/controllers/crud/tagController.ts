import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, tagService } from '@/services';
export class TagController extends CrudController<typeof tagService> {
  constructor() {
    super(tagService);
  }
  async dragDrop(id : string , params : any){
    return this.service.dragDrop(id,params)
  }

  async getList(option?: ICrudOption ,employee_id ? : string , user_id?:string) {
    return await this.service.getList(option,employee_id,user_id)
  }
}
