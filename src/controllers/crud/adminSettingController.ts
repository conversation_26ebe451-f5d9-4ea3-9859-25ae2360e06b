import { CrudController } from '../crudController'
import { adminSettingService } from '@/services'

export class AdminSettingController extends CrudController<
  typeof adminSettingService
> {
  constructor() {
    super(adminSettingService)
  }
  async getAdminSetting() {
    return await this.service.getAdminSetting()
  }

  async updateAdminSetting() {
    return await this.service.updateAdminSetting()
  }

  async updateMentorStatus() {
    return await this.service.updateMentorStatus()
  }

  async updateReportLimit(params : any) {
    return await this.service.updateReportLimit(params)
  }
}
