import {blogService, ICrudOption} from '@/services'
import { CrudController } from '../crudController'

export class BlogController extends CrudController<typeof blogService> {
  constructor() {
    super(blogService)
  }
  async statisticBlog(id: string, startDate: Date, endDate: Date) {
    return this.service.statisticBlog(id, startDate, endDate)
  }
  async createByClient(data: any , option?: ICrudOption) {
    return this.service.createByClient(data ,option)
  }

  async getItem(params: any, option?: ICrudOption) {
    return await this.service.getItem(params, option);
  }
}
