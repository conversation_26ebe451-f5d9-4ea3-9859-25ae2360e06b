import { CrudController } from '../crudController'
import {settingStationSubwayService} from '@/services'
export class SettingStationSubwayController extends CrudController<typeof settingStationSubwayService> {
    constructor() {
        super(settingStationSubwayService)
    }
    async importExcel(params: any) {
        return await this.service.importExcel(params)
    }
    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}