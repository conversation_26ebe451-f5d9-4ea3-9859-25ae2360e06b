import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, pushNotificationService } from '@/services'


export class PushNotificationController extends CrudController<typeof pushNotificationService> {
    constructor() {
        super(pushNotificationService)
    }
    async sendFcmToGroup(params: any) {
        const notification = await this.service.getItem({
            filter: {
                id: params.id
            }
        });
       return await this.service.sendFcmToUser(notification)
    }
    async sendFcmToUser(params: any) {
        const notification = await this.service.getItem({
            filter: {
                id: params.id
            }
        });
       return await this.service.sendFcmToUser(notification)
    }
}
