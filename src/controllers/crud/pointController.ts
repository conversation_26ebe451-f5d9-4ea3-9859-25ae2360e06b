import { <PERSON>rud<PERSON>ontroller } from '../crudController'
import {ICrudOption, pointService} from '@/services'

export class PointController extends CrudController<typeof pointService> {
  constructor() {
    super(pointService)
  }

  async attendance(user_id: string) {
    return this.service.attendance(user_id)
  }

  async lottery(user_id: string) {
    return this.service.lottery(user_id)
  }
  async count(filter:ICrudOption['filter']) {
    return this.service.count(filter)
  }
}
