import { <PERSON><PERSON><PERSON>ontroller } from '../crudController';
import { ICrudOption, errorService, categoryService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Categories
 *   description: Category management and operations
 */

export class CategoryController extends <PERSON>rud<PERSON>ontroller<typeof categoryService> {
  constructor() {
    super(categoryService);
  }

  /**
   * @swagger
   * /categories/v2:
   *   get:
   *     summary: Get list of categories (version 2)
   *     tags: [Categories]
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *     responses:
   *       200:
   *         description: List of categories
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Category'
   *                 count:
   *                   type: integer
   */
  async getListV2(option?: ICrudOption) {
    console.log('29148 getListV2 v2', option);
    return await this.service.getListV2(option)
  }

  /**
   * @swagger
   * /categories/{id}/reorder:
   *   put:
   *     summary: Reorder a category (drag and drop)
   *     tags: [Categories]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Category ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               index:
   *                 type: integer
   *                 description: New position index
   *     responses:
   *       200:
   *         description: Category reordered successfully
   *       404:
   *         description: Category not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async dragDropCategory(id : string , params : any){
    return this.service.dragDropCategory(id,params)
  }

  /**
   * @swagger
   * /categories:
   *   get:
   *     summary: Get list of categories
   *     tags: [Categories]
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - in: query
   *         name: employee_id
   *         schema:
   *           type: string
   *         description: Filter by employee ID
   *       - in: query
   *         name: user_id
   *         schema:
   *           type: string
   *         description: Filter by user ID
   *     responses:
   *       200:
   *         description: List of categories
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Category'
   *                 count:
   *                   type: integer
   */
  async getList(option?: ICrudOption ,employee_id ? : string , user_id?:string) {
    return await this.service.getList(option,employee_id,user_id)
  }
}
