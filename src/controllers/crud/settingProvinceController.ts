import { CrudController } from '../crudController'
import {settingProvinceService} from '@/services'
export class SettingProvinceController extends CrudController<typeof settingProvinceService> {
    constructor() {
        super(settingProvinceService)
    }
    async importExcel(params: any) {
        return await this.service.importExcel(params)
    }
    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}