import { <PERSON>rudController } from '../crudController'
import { ICrudOption, reviewService } from '@/services'
export class ReviewController extends CrudController<typeof reviewService> {
  constructor() {
    super(reviewService)
  }
  async unreportFunc(params: any, option?: ICrudOption) {
    return await this.service.unreportFunc(params, option)
  }
  async reportFunc(params: any, option?: ICrudOption) {
    return await this.service.reportFunc(params, option)
  }
  async softDeleteOne(params: any) {
    return await this.service.softDeleteOne(params)
  }
  async softDeleteAll(user_id: string, params: any) {
    return await this.service.softDeleteAll(user_id, params)
  }
  async getListIncludeBlock(params: any, option?: ICrudOption) {
    return await this.service.getListIncludeBlock(params, option)
  }

  async getListFilter(params: any, option?: ICrudOption) {
    return await this.service.getListFilter(params, option)
  }
  async getListForShop(params: any, option?: ICrudOption) {
    return await this.service.getListForShop(params, option)
  }
  async getListByAdmin(option?: ICrudOption) {
    return await this.service.getListByAdmin(option)
  }
  async likeReview(params: any, option?: ICrudOption) {
    return await this.service.likeReview(params, option);
  }
  async unlikeReview(params: any, option?: ICrudOption) {
    return await this.service.unlikeReview(params, option);
  }
  async dislikeReview(params: any, option?: ICrudOption) {
    return await this.service.dislikeReview(params, option);
  }
  async undislikeReview(params: any, option?: ICrudOption) {
    return await this.service.undislikeReview(params, option);
  }

  async countByUser(user_id: string) {
    return await this.service.countByUser(user_id);
  }
}
