import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, recruitService } from '@/services';
export class RecruitController extends CrudController<typeof recruitService> {
  constructor() {
    super(recruitService);
  }

  async likeRecruit(params: any, option?: ICrudOption) {
    return await this.service.likeRecruit(params, option);
  }
  async unlikeRecruit(params: any, option?: ICrudOption) {
    return await this.service.unlikeRecruit(params, option);
  }
   async getListRecruit(params: any, option?: ICrudOption) {
    return await this.service.getListRecruit(params, option);
  }
  async view(params: any, option?: ICrudOption) {
    return await this.service.view(params, option)
}
async getNear(params: any, option?: ICrudOption) {
  return await this.service.getNear(params, option);
}
async getRelate(params: any, option?: ICrudOption) {
  return await this.service.getRelate(params, option);
}
async deleteByType(option?: ICrudOption) {
  return await this.service.deleteAll(option);
}
}
