import { <PERSON>rudController } from '../crudController'
import { conversationService, ICrudOption } from '@/services'

export class ConversationController extends <PERSON>rudController<
  typeof conversationService
> {
  constructor() {
    super(conversationService)
  }

  async findOrCreate(params: any, option?: ICrudOption) {
    return await this.service.findOrCreate(params, option)
  }

  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option)
  }

  async getItem(params: any, option?: ICrudOption) {
    return await this.service.getItem(params, option)
  }

  async delete(params: any, option?: ICrudOption) {
    return await this.service.delete(params, option)
  }

  async reportFunc(params: any, option?: ICrudOption) {
    return await this.service.reportFunc(params, option);
  }
  async unreportFunc(params: any, option?: ICrudOption) {
    return await this.service.unreportFunc(params, option);
  }
}
