import { <PERSON>rudController } from '../crudController'
import { questionService, ICrudOption } from '@/services'

export class QuestionController extends <PERSON>rudController<typeof questionService> {
  constructor() {
    super(questionService)
  }

  async answerQuestion(params: any, option?: ICrudOption) {
    return await this.service.answerQuestion(params, option)
  }

  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option)
  }

  async adminGetList(params: any, option?: ICrudOption) {
    return await this.service.adminGetList(params, option)
  }

  async countUnread() {
    return await this.service.countUnread()
  }
}
