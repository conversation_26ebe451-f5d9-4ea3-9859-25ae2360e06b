import { <PERSON><PERSON><PERSON><PERSON>roller } from '../crudController';
import { ICrudOption, errorService, usedCarPostService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Used Car Posts
 *   description: Used car post management and operations
 */

export class UsedCarPostController extends CrudController<typeof usedCarPostService> {
  constructor() {
    super(usedCarPostService);
  }

  /**
   * @swagger
   * /used-car-posts/{id}/view:
   *   post:
   *     summary: Increment view count for a used car post
   *     tags: [Used Car Posts]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID
   *     responses:
   *       200:
   *         description: View count incremented successfully
   *       404:
   *         description: Used car post not found
   */
  async viewPost(params: any, option?: ICrudOption) {
    return await this.service.viewPost(params, option);
  }

  /**
   * @swagger
   * /used-car-posts/list:
   *   post:
   *     summary: Get filtered list of used car posts
   *     tags: [Used Car Posts]
   *     requestBody:
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               min_price:
   *                 type: integer
   *                 description: Minimum price filter
   *               max_price:
   *                 type: integer
   *                 description: Maximum price filter
   *               min_year:
   *                 type: integer
   *                 description: Minimum year filter
   *               max_year:
   *                 type: integer
   *                 description: Maximum year filter
   *               state:
   *                 type: string
   *                 enum: [available, pending, sold, withdrawn]
   *                 description: Post state filter
   *               manufacturer_id:
   *                 type: string
   *                 description: Car manufacturer ID filter
   *               model_id:
   *                 type: string
   *                 description: Car model ID filter
   *               fuel_type:
   *                 type: string
   *                 enum: [gasoline, diesel, hybrid, electric, lpg]
   *                 description: Fuel type filter
   *               transmission:
   *                 type: string
   *                 enum: [manual, automatic, cvt]
   *                 description: Transmission type filter
   *               body_type:
   *                 type: string
   *                 enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
   *                 description: Body type filter
   *               condition:
   *                 type: string
   *                 enum: [excellent, very_good, good, fair, poor]
   *                 description: Car condition filter
   *               latitude:
   *                 type: number
   *                 description: Latitude for location-based search
   *               longitude:
   *                 type: number
   *                 description: Longitude for location-based search
   *               radius:
   *                 type: number
   *                 description: Search radius in meters for location-based search
   *               sort_by:
   *                 type: string
   *                 enum: [price, year, mileage, created_at, view_count]
   *                 description: Sort field
   *               sort_order:
   *                 type: string
   *                 enum: [asc, desc]
   *                 description: Sort order
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Maximum number of posts to return
   *       - in: query
   *         name: offset
   *         schema:
   *           type: integer
   *           default: 0
   *         description: Number of posts to skip
   *     responses:
   *       200:
   *         description: List of used car posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/UsedCarPost'
   *                 count:
   *                   type: integer
   */
  async getListUsedCarPosts(params: any, option?: ICrudOption) {
    return await this.service.getListUsedCarPosts(params, option);
  }

  /**
   * @swagger
   * /used-car-posts:
   *   get:
   *     summary: Get list of used car posts
   *     tags: [Used Car Posts]
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *     responses:
   *       200:
   *         description: List of used car posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/UsedCarPost'
   *                 count:
   *                   type: integer
   */
  async getList(params: any, option?: ICrudOption) {
    return await this.service.getList(params, option);
  }

  /**
   * @swagger
   * /used-car-posts:
   *   post:
   *     summary: Create a new used car post
   *     tags: [Used Car Posts]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - title
   *               - price
   *               - year
   *               - mileage
   *               - contact_phone
   *               - latitude
   *               - longitude
   *               - address
   *               - manufacturer_id
   *               - model_id
   *             properties:
   *               title:
   *                 type: string
   *                 description: Car listing title
   *               description:
   *                 type: string
   *                 description: Detailed description of the car
   *               price:
   *                 type: integer
   *                 description: Asking price for the car
   *               negotiable:
   *                 type: boolean
   *                 description: Whether price is negotiable
   *               year:
   *                 type: integer
   *                 description: Manufacturing year of the car
   *               mileage:
   *                 type: integer
   *                 description: Current mileage in kilometers
   *               fuel_type:
   *                 type: string
   *                 enum: [gasoline, diesel, hybrid, electric, lpg]
   *                 description: Type of fuel the car uses
   *               transmission:
   *                 type: string
   *                 enum: [manual, automatic, cvt]
   *                 description: Transmission type
   *               body_type:
   *                 type: string
   *                 enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
   *                 description: Body type of the car
   *               exterior_color:
   *                 type: string
   *                 description: Exterior color of the car
   *               interior_color:
   *                 type: string
   *                 description: Interior color of the car
   *               condition:
   *                 type: string
   *                 enum: [excellent, very_good, good, fair, poor]
   *                 description: Overall condition of the car
   *               accident_history:
   *                 type: boolean
   *                 description: Whether the car has been in an accident
   *               number_of_owners:
   *                 type: integer
   *                 description: Number of previous owners
   *               images:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: URLs to car images
   *               contact_phone:
   *                 type: string
   *                 description: Contact phone number
   *               contact_email:
   *                 type: string
   *                 description: Contact email address
   *               latitude:
   *                 type: number
   *                 description: Location latitude
   *               longitude:
   *                 type: number
   *                 description: Location longitude
   *               address:
   *                 type: string
   *                 description: Full address
   *               short_address:
   *                 type: string
   *                 description: Short address
   *               manufacturer_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the car manufacturer
   *               model_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the car model
   *               category_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the post category
   *     responses:
   *       201:
   *         description: Used car post created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarPost'
   *       400:
   *         description: Invalid input data
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async create(params: any, option?: ICrudOption) {
    return await this.service.create(params, option);
  }

  /**
   * @swagger
   * /used-car-posts/{id}:
   *   put:
   *     summary: Update a used car post
   *     tags: [Used Car Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UsedCarPost'
   *     responses:
   *       200:
   *         description: Used car post updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarPost'
   *       404:
   *         description: Used car post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async update(params: any, option?: ICrudOption) {
    return await this.service.update(params, option);
  }

  /**
   * @swagger
   * /used-car-posts/{id}:
   *   delete:
   *     summary: Delete a used car post
   *     tags: [Used Car Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID
   *     responses:
   *       200:
   *         description: Used car post deleted successfully
   *       404:
   *         description: Used car post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async delete(params: any, option?: ICrudOption) {
    return await this.service.delete(params, option);
  }

  /**
   * @swagger
   * /used-car-posts/{id}:
   *   get:
   *     summary: Get used car post details
   *     tags: [Used Car Posts]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID
   *     responses:
   *       200:
   *         description: Used car post details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarPost'
   *       404:
   *         description: Used car post not found
   */
  async getItem(params: any, option?: ICrudOption) {
    return await this.service.getItem(params, option);
  }

  /**
   * @swagger
   * /used-car-posts/{id}/state:
   *   put:
   *     summary: Update used car post state
   *     tags: [Used Car Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - state
   *             properties:
   *               state:
   *                 type: string
   *                 enum: [available, pending, sold, withdrawn]
   *                 description: New state for the post
   *     responses:
   *       200:
   *         description: State updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarPost'
   *       404:
   *         description: Used car post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async updateState(params: any, option?: ICrudOption) {
    return await this.service.updateState(params, option);
  }

  /**
   * @swagger
   * /used-car-posts/count/user:
   *   get:
   *     summary: Get count of used car posts by user
   *     tags: [Used Car Posts]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Used car post count
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async countByUser(user_id: string) {
    return await this.service.countByUser(user_id);
  }
}

export const usedCarPostController = new UsedCarPostController(); 