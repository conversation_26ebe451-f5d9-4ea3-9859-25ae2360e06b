import { CrudController } from '../crudController'
import {settingStationService} from '@/services'
export class SettingStationController extends CrudController<typeof settingStationService> {
    constructor() {
        super(settingStationService)
    }
    async importExcel(params: any) {
        return await this.service.importExcel(params)
    }
    async dragDropItem(id: string, params: any) {
        return this.service.dragDropItem(id ,params)
    }
}