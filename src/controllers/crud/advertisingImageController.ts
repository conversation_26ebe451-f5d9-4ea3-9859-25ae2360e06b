import { <PERSON>rudController } from '../crudController'
import { ICrudOption, errorService, advertisingImageService } from '@/services'
export class AdvertisingImageController extends CrudController<typeof advertisingImageService> {
    constructor() {
        super(advertisingImageService)
    }
    async getList(params: any, option?: ICrudOption) {
        return this.service.getList(params, option)
     }
}