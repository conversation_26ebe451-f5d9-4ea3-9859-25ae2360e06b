import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, recentReadingService } from '@/services';
export class RecentReadingController extends CrudController<
  typeof recentReadingService
> {
  constructor() {
    super(recentReadingService);
  }
  async getListRecentReading(params: any, option?: ICrudOption) {
    return await this.service.getListRecentReading(params, option);
  }
}
