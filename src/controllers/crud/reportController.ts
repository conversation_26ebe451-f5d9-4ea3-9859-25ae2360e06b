import { <PERSON>rudController } from '../crudController';
import { ICrudOption, errorService, reportService } from '@/services';
export class ReportController extends CrudController<typeof reportService> {
  constructor() {
    super(reportService);
  }

  async restoreMultiplesFunc(option?: ICrudOption) {
    return await this.service.restoreMultiplesFunc(option)
  }
  
  async deleteMultiplesFunc(option?: ICrudOption) {
    return await this.service.deleteMultiplesFunc(option)
  }
  async countReport() {
    return await this.service.countReport()
  }
}
