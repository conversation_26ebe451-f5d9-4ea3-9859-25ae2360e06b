import * as http from 'http'
import * as io from 'socket.io'
import * as express from 'express'
import * as _ from 'lodash'
import * as moment from 'moment'

import { SOCKET_EVENTS } from '@/services/socketService/constant/event'
import { config } from '@/config'
import {
  IConnection,
  IRoom,
  CallInfo,
  requestCallSchema,
  RequestCallSchema,
  AcceptCallSchema,
  acceptCallSchema,
  callIdSchema,
} from '@/services/socketService/schema'
import { tokenService, messageService } from '@/services'
import { utilService } from '@/services'

const CALL_TIMEOUT = 45 * 1000

interface SocketIO extends io.Socket {
  tokenInfo: any
}

export class SocketService {
  init(app: express.Application) {
    // 1. Create Server
    this.server = http.createServer(app)
    this.socketServer = io(this.server)
    // 2. Auth middleware
    this.socketServer.use(this.authMiddleware.bind(this))
    // 3. Handle Connection
    this.socketServer.on(SOCKET_EVENTS.CONNECTION, this.onConnection.bind(this))

    // 4. Run Socket Server
    this.server.listen(config.socket.port, () => {
      console.log(`Socket Server is running on port ${config.socket.port}`)
    })
  }
  private server: http.Server
  private socketServer: io.Server
  private sockets: { [x: string]: io.Socket } = {}
  connections: { [x: string]: IConnection } = {}
  rooms: { [x: string]: IRoom } = {}
  calls: Map<string, CallInfo> = new Map()
  private async authMiddleware(socket: SocketIO, next: any) {
    const { token } = socket.handshake.query
    try {
      // 1. Validate token with user
      if (!token) return next(new Error('authentication error'))
      const result = await tokenService.decodeToken(token)
      if (!result || !result.payload) return next(new Error('bad token'))
      socket.tokenInfo = result
      const uid = result.payload.user_id ? result.payload.user_id : result.payload.employee_id
      // 2. Cache connection
      if (!this.connections[uid])
        this.connections[uid] = { uid, sockets: [], conversation_ids: [] }
      this.connections[uid].sockets.push(socket.id)
      this.sockets[socket.id] = socket
      _.set(socket, 'uid', uid)
      // this.autoSubscribeUserConversation(uid);
      // this.notifyToUserAboutConnectedClients(uid);
      this.notifyUserConnectionStateToConnectedClients(
        this.connections[uid],
        true
      )
      return next()
    } catch (err) {
      return next(err)
    }
  }

  private getSocketsFromUserId(userId: string): string[] {
    const sockets = this.connections[userId]
      ? this.connections[userId].sockets
      : []
    return this.eliminateDuplicates(sockets)
  }

  public emitToUser(
    userId: string,
    key: string,
    data: any,
    exclude: string[] = []
  ) {
    console.log('bambi emit to user', userId, key)
    const socketIds = this.getSocketsFromUserId(userId).filter(
      (socketId) => !exclude.includes(socketId)
    )
    socketIds.forEach((socketId) => {
      const clientSocket = this.socketServer.sockets.connected[socketId]
      if (clientSocket) clientSocket.emit(key, data)
    })
  }
  public emitToUserWithCallback(
    userId: string,
    key: string,
    data: any,
    callback: any
  ) {
    const socketIds = this.getSocketsFromUserId(userId)
    socketIds.forEach((socketId) => {
      const clientSocket = this.socketServer.sockets.connected[socketId]
      clientSocket.emit(key, data, callback)
      console.log('bambi emit to user success', userId, socketId, key)
    })
  }

  private onConnection(socket: SocketIO) {
    //Handle message
    socket.on(
      SOCKET_EVENTS.READ_MESSAGE,
      async (data: { conversation_id: string }) => {
        if (data && data.conversation_id) {
          try {
            await messageService.readMessage({
              conversation_id: data.conversation_id,
              user_id: socket.tokenInfo.payload.user_id,
            })
          } catch (err) {
            console.error(`${SOCKET_EVENTS} ${err}`)
          }
        }
      }
    )

    //handle log out
    socket.on(
        SOCKET_EVENTS.LOG_OUT,
        async () => {
          this.emitToUser(
              socket.tokenInfo.payload.employee_id,
              SOCKET_EVENTS.LOG_OUT,
              {user : socket.tokenInfo.payload.employee_id}
          )
        }
    )

    //Handle call
    //Request a call
    socket.on(SOCKET_EVENTS.REQUEST_CALL, (data: RequestCallSchema) => {
      const validate = utilService.validateJSON(requestCallSchema, data)
      if (validate.isValid) {
        const { call_info, offer } = data
        this.calls.set(call_info.id, call_info)
        this.emitToUser(data.call_info.to_user, SOCKET_EVENTS.INCOMING_CALL, {
          offer,
          call_info,
        })
        setTimeout(() => {
          const call = this.calls.get(call_info.id)
          if (call && !call.created_at) {
            this.emitToUser(
              socket.tokenInfo.payload.user_id,
              SOCKET_EVENTS.CALL_HAS_BEEN_REJECTED,
              call.id
            )
            messageService.create({
              user_id: call.from_user,
              conversation_id: call.conversation_id,
              is_call: true,
              call_status: false,
            })
            this.calls.delete(call.id)
          }
        }, CALL_TIMEOUT)
      }
    })
    //Accept a call
    socket.on(SOCKET_EVENTS.ACCEPT_CALL, (data: AcceptCallSchema) => {
      const validate = utilService.validateJSON(acceptCallSchema, data)
      if (validate.isValid) {
        const call = this.calls.get(data.call_id)
        if (call) {
          this.emitToUser(
            call.from_user,
            SOCKET_EVENTS.CALL_HAS_BEEN_ACCEPTED,
            { offer: data.offer, call_id: call.id }
          )
          this.emitToUser(
            call.to_user,
            SOCKET_EVENTS.HANDLER_CALL_BY_OTHER_DEVICE,
            call.id,
            [socket.id]
          )
          this.calls.set(call.id, { ...call, created_at: new Date() })
        }
      }
    })
    //Reject a call
    socket.on(SOCKET_EVENTS.REJECT_CALL, (call_id: string) => {
      if (call_id) {
        const call = this.calls.get(call_id)
        if (call) {
          this.emitToUser(
            call.from_user,
            SOCKET_EVENTS.CALL_HAS_BEEN_REJECTED,
            call.id
          )
          this.emitToUser(
            call.to_user,
            SOCKET_EVENTS.HANDLER_CALL_BY_OTHER_DEVICE,
            call.id,
            [socket.id]
          )
          messageService.create({
            user_id: call.from_user,
            conversation_id: call.conversation_id,
            is_call: true,
            call_status: false,
          })
          this.calls.delete(call.id)
        }
      }
    })
    //Cancel a call
    socket.on(SOCKET_EVENTS.CANCEL_CALL, (call_id: string) => {
      if (call_id) {
        const call = this.calls.get(call_id)
        if (call) {
          this.emitToUser(
            call.to_user,
            SOCKET_EVENTS.CALL_HAS_BEEN_CANCELED,
            call.id
          )
          this.emitToUser(
            call.from_user,
            SOCKET_EVENTS.HANDLER_CALL_BY_OTHER_DEVICE,

            call.id,
            [socket.id]
          )
          messageService.create({
            user_id: call.from_user,
            conversation_id: call.conversation_id,
            is_call: true,
            call_status: true,
            duration: 0,
          })
          this.calls.delete(call.id)
        }
      }
    })
    //End a call
    socket.on(SOCKET_EVENTS.END_CALL, (call_id: string) => {
      if (call_id) {
        const call = this.calls.get(call_id)
        if (call) {
          let to: string
          if (socket.tokenInfo.payload.user_id === call.from_user) {
            to = call.to_user
          } else {
            to = call.from_user
          }
          this.emitToUser(to, SOCKET_EVENTS.CALL_HAS_BEEN_ENDED, call.id)
          const now = moment().valueOf()
          const duration = (now - moment(call.created_at).valueOf()) / 1000
          messageService.create({
            user_id: call.from_user,
            conversation_id: call.conversation_id,
            is_call: true,
            call_status: true,
            duration: duration,
          })
          this.calls.delete(call.id)
        }
      }
    })

    //Handle disconnect
    socket.on(
      SOCKET_EVENTS.DISCONNECT,
      this.handleSocketEvent(this.onClientDisconnect, socket)
    )

    //Handle change reservation status
    socket.on(
        SOCKET_EVENTS.CHANGE_RESERVATION_STATUS,
        async (data: { status: boolean }) => {
          if (data) {
            try {
              this.emitToUser(
                  socket.tokenInfo.payload.user_id,
                  SOCKET_EVENTS.CHANGE_RESERVATION_STATUS,
                  data
              )
            } catch (err) {
              console.error(`${SOCKET_EVENTS} ${err}`)
            }
          }
        }
    )
  }

  private handleSocketEvent(
    func: (socket: io.Socket, ...params: any[]) => any,
    socket: io.Socket
  ) {
    return (...args: any[]) => func.bind(this)(socket, ...args)
  }
  public getConnectionFromSocketId(socketId: string): IConnection {
    const uids = Object.keys(this.connections)
    let result = undefined
    uids.forEach((uid) => {
      this.connections[uid].sockets.forEach((e) => {
        if (e === socketId) {
          result = this.connections[uid]
        }
      })
    })

    return result
  }
  notifyUserConnectionStateToConnectedClientsByUid(
    uid: string,
    isConnected: boolean
  ) {
    this.notifyUserConnectionStateToConnectedClients(
      this.connections[uid],
      true
    )
  }

  notifyUserConnectionStateToConnectedClients(
    connection: IConnection,
    isConnected: boolean
  ) {
    const connectedUids = Object.keys(this.connections)
    let listUidToNotify: string[] = []
    connectedUids.forEach((connectedUid) => {
      this.connections[connectedUid].conversation_ids.forEach(
        (connectedConversationId) => {
          // List conversation đang ket noi
          connection.conversation_ids.forEach((myConversationId) => {
            // List conversation tui dang subscribe
            if (connectedConversationId === myConversationId) {
              listUidToNotify.push(connectedUid)
            }
          })
        }
      )
    })

    listUidToNotify = this.eliminateDuplicates(listUidToNotify)
    listUidToNotify.forEach((uid) => {
      this.sendToChannel(
        uid,
        { user_ids: [connection.uid], is_connected: isConnected },
        'connection_state'
      )
    })
  }
  private onClientDisconnect(socket: io.Socket) {
    // 1. Clear socket
    console.log('disconnecting user')
    delete this.sockets[socket.id]
    const connection = this.getConnectionFromSocketId(socket.id)
    if (connection) {
      _.remove(connection.sockets, (s) => s == socket.id)
      this.notifyUserConnectionStateToConnectedClients(connection, false)
    }
  }
  private onSubcribe(socket: io.Socket, channel: string, uid: string) {
    // Join socket to channel
    if (socket) {
      socket.join(channel)

      if (!this.rooms[channel]) {
        this.rooms[channel] = { sockets: [], uids: [] }
      }
      this.rooms[channel].sockets.push(socket.id)
      this.rooms[channel].uids.push(uid)
    }
  }
  private onUnsubscribe(socket: io.Socket, channel: string, uid: string) {
    // Clear socket from channel
    if (socket) {
      socket.leave(channel)

      if (this.rooms[channel]) {
        const indexSocket = this.rooms[channel].sockets.indexOf(socket.id)
        if (indexSocket > -1) {
          this.rooms[channel].sockets.splice(indexSocket, 1)
        }
        const indexUid = this.rooms[channel].uids.indexOf(uid)
        if (indexUid > -1) {
          this.rooms[channel].uids.splice(indexUid, 1)
        }
      }
    }
  }
  sendToChannel(channel: string, msg: any, event?: string) {
    this.socketServer.to(channel).emit(event || 'message', msg)
  }
  sendToUsers(uids: string[], msg: any, event?: string) {
    uids.forEach((uid) => {
      const connection = this.connections[uid]
      if (connection) {
        connection.sockets.forEach((s) => {
          this.sendToChannel(s, msg, event)
        })
      }
    })
  }
  addUsersToChannel(uids: string[], channel: string) {
    uids.forEach((uid) => {
      const connection = this.connections[uid]
      if (connection) {
        connection.sockets.forEach((s) =>
          this.onSubcribe(this.sockets[s], channel, connection.uid)
        )
      }
    })
  }
  removeUsersFromChannel(uids: string[], channel: string) {
    for (const uid of uids) {
      const connection = this.connections[uid]
      if (connection) {
        for (const s of connection.sockets) {
          this.onUnsubscribe(this.sockets[s], channel, connection.uid)
        }
      }
    }
  }
  // private async autoSubscribeUserConversation(uid: string) {
  //   const { rows: conversations } = await userInConversationService.getList({
  //     filter: { user_id: uid }
  //   });
  //   for (const c of conversations) {
  //     this.addUsersToChannel([uid], c.conversation_id);
  //     this.connections[uid].conversation_ids.push(c.conversation_id);
  //   }

  //   // Subscribe a channel that === uid -> so that server can
  //   // intentionally send message to a user
  //   this.addUsersToChannel([uid], uid);
  // }
  // public async notifyToUserAboutConnectedClients(uid: string) {
  //   const { rows: conversations } = await userInConversationService.getList({
  //     filter: { user_id: uid }
  //   });
  //   let listConnectedUids: string[] = [];
  //   for (const c of conversations) {
  //     if (this.rooms[c.conversation_id]) {
  //       const uids = this.rooms[c.conversation_id].uids;
  //       if (uids) {
  //         listConnectedUids = [...listConnectedUids, ...uids];
  //       }
  //     }
  //   }

  //   listConnectedUids = this.eliminateDuplicates(listConnectedUids);
  //   this.sendToChannel(
  //     uid,
  //     { user_ids: listConnectedUids, is_connected: true },
  //     'connection_state'
  //   );
  // }

  public notifyVNPayCompleted(uid: string, isSuccess: boolean) {
    this.sendToChannel(uid, { is_success: isSuccess }, 'vnpay_completed')
  }
  public notifyCalling(uid: string, url: string) {
    this.sendToChannel(uid, { url: url }, 'calling')
  }
  public notifyCancelCall(uid: string) {
    this.sendToChannel(uid, {}, 'cancel_call')
  }
  private eliminateDuplicates(arr: string[]) {
    const len = arr.length,
      out = [],
      obj: any = {}
    for (let i = 0; i < len; i++) {
      obj[arr[i]] = 0
    }
    for (const i in obj) {
      out.push(i)
    }
    return out
  }
}
