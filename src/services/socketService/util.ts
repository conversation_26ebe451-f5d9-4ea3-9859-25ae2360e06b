import { EARTH_RADIUS } from '../../const';

export const deg2rad = (deg: number) => {
  return deg * (Math.PI / 180);
};

export const calcDistance = (fromPos: any, toPos: any) => {
  const dLat = deg2rad(fromPos.latitude - toPos.latitude); // deg2rad below
  const dLon = deg2rad(fromPos.longitude - toPos.longitude);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(toPos.latitude)) *
      Math.cos(deg2rad(fromPos.latitude)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return EARTH_RADIUS * c; // distance in km
};
