export interface IConnection {
  uid: string
  sockets: string[]

  conversation_ids: string[]
}
export interface IRoom {
  sockets: string[]
  uids: string[]
}
export interface ISocket {
  socketId: string
  room: string
  callerId: string
  receiverId: string

  caller?: any
  receiver?: any
  connectedTime: number
  allowToReceiveCash: boolean
  isTollFree: boolean
}

export interface CallInfo {
  id: string
  conversation_id: string
  from_user: string
  to_user: string
  from: {
    name: string
    avatar: string
  }
  [key: string]: any
}

export interface RequestCallSchema {
  call_info: CallInfo
  offer: any
}

export const requestCallSchema = {
  type: 'object',
  properties: {
    call_info: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
        },
        conversation_id: {
          type: 'string',
        },
        from: {
          type: 'object',
          properties: {
            avatar: {
              type: 'string',
            },
            name: {
              type: 'string',
            },
          },
          required: ['avatar', 'name'],
        },
        from_user: {
          type: 'string',
        },
        to_user: {
          type: 'string',
        },
      },
      required: ['id', 'conversation_id', 'from', 'from_user', 'to_user'],
    },
    offer: {},
  },
  required: ['call_info', 'offer'],
}

export interface AcceptCallSchema {
  call_id: string
  offer: any
}

export const acceptCallSchema = {
  type: 'object',
  properties: {
    call_id: {
      type: 'string',
    },
    offer: {},
  },
  required: ['call_id', 'offer'],
}

export const callIdSchema = {
  type: 'string',
}
