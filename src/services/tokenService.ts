import { errorService, employeeService, userService, rewardService } from '@/services'
import { config } from '@/config'
import * as moment from 'moment'
import * as jwt from 'jwt-simple'
import { ROLE, USER_TYPE } from '@/const'
import { ActionLog, User } from '@/models'
import { Op } from 'sequelize'
export interface IGenerateTokenOption {
  exp?: moment.Moment
  secret?: string
}
export interface IDecodeTokenOption {
  secret?: string
}
export class TokenService {
  constructor() {}

  async generateToken(
    payload: any,
    role: string,
    option: IGenerateTokenOption = {
      exp: moment().add(2, 'months'),
    }
  ) {
    const secret = option.secret || config.server.secret
    return jwt.encode(
      {
        payload: payload,
        role: role,
        exp: option.exp,
      },
      secret
    )
  }
  async decodeToken(token: string, option?: IDecodeTokenOption) {
    let result = undefined
    try {
      const secret = (option && option.secret) || config.server.secret
      result = jwt.decode(token, secret)
    } catch (err) {
      throw errorService.auth.badToken()
    }
    if (result) {
      if (result.payload && result.payload.role === ROLE.USER) {
        try {
          const user = await userService.getItem({
            filter: {
              id: result.payload.user_id,
            },
          })
        } catch (error) {
          throw errorService.auth.badToken()
        }
      }

      if (new Date(result.exp).getTime() <= Date.now()) {
        throw errorService.auth.tokenExpired()
      }
      return result
    } else {
      throw errorService.auth.badToken()
    }
  }
  async getAdminToken(secret: string = '') {
    secret = secret + config.server.secret
    return await this.generateToken({}, 'admin', {
      exp: moment().add(9999, 'years'),
      secret,
    })
  }
  async getWriteToken(secret: string = '') {
    secret = secret + config.server.secret
    return await this.generateToken({}, 'write', {
      exp: moment().add(9999, 'years'),
      secret,
    })
  }
  async getReadToken(secret: string = '') {
    secret = secret + config.server.secret
    return await this.generateToken({}, 'read', {
      exp: moment().add(9999, 'years'),
      secret,
    })
  }
  // async getTokenAdmin(admin_id: string, secret: string = "") {
  //     secret = secret + config.server.secret
  //     let admin = await adminService.getItem({
  //         filter: {
  //             id: admin_id
  //         }
  //     });

  //     return await this.generateToken({
  //         admin_id,
  //     }, undefined, {
  //             exp: moment().add(2, 'months'),
  //             secret
  //         })
  // }
  async getEmployeeToken(employee_id: string, secret: string = '') {
    secret = secret + config.server.secret
    const user = await employeeService.getItem({
      filter: {
        id: employee_id,
      },
    })

    return await this.generateToken(
      {
        employee_id,
        role: user.type,
      },
      user.type,
      {
        exp: moment().add(9999, 'years'),
        secret,
      }
    )
  }
  async getUserToken(user_id: string, secret: string = '') {
    secret = secret + config.server.secret
    const user = await userService.getItem({
      filter: {
        id: user_id,
      },
    })
    if (!user) {
      throw errorService.database.queryFail(
        'User not found to generate jwt token'
      )
    }
    const today = new Date().toISOString().slice(0, 10);
    const alreadyChecked = await ActionLog.findOne({
      where: {
        user_id: user_id,
        action_type: "DAILY_ATTENDANCE",
        created_at: {
          [Op.gte]: new Date(today + "T00:00:00"),
          [Op.lt]: new Date(today + "T23:59:59"),
        },
      },
    });

    if (!alreadyChecked) {
      await rewardService.addReward(user_id, "DAILY_ATTENDANCE");
    }
    return await this.generateToken(
      {
        user_id,
        account_type: user.account_type,
        role: ROLE.USER,
      },
      ROLE.USER,
      {
        exp: moment().add(9999, 'years'),
        secret,
      }
    )
  }
  async getAdminTokenForForgetPassWord(email: string, secret: string = '') {
    secret = secret + config.server.secret
    const admin = await employeeService.getItem({
      filter: {
        email,
      },
    })
    if (!admin) {
      throw errorService.database.queryFail(
        'Employee not found to generate jwt token'
      )
    }
    return await this.generateToken(
      {
        employee_id: admin.id,
        password_md5: admin.password,
        role: admin.type,
      },
      admin.type,
      {
        exp: moment().add(9999, 'years'),
        secret,
      }
    )
  }
  async getUserTokenForForgetPassWord(email: string, secret: string = '') {
    try {
      secret = secret + config.server.secret

      const user = await userService.getItem({
        filter: {
          email,
        },
      })

      if (!user) {
        throw errorService.database.queryFail(
          'User not found to generate jwt token'
        )
      }
      return await this.generateToken(
        {
          user_id: user.id,
          password_md5: user.password,
          role: ROLE.USER,
        },
        ROLE.USER,
        {
          exp: moment().add(30, 'minutes'),
          secret,
        }
      )
    } catch (error) {
      if (error.message) {
        throw errorService.database.queryFail(error.message)
      } else {
        throw error
      }
    }
  }
}
