import { Sequelize, sequelize } from '@/models'
import {
    errorService,
    shopService,
    eventService,
    userService,
    reviewService,
    reservationService,
    notificationService,
    pushNotificationService,
    postService, blogService, messageService, keywordService, siteService
} from '@/services'

const schedule = require('node-schedule')
const SECOND_IN_60_DAY = 5184000
const MILLISECOND_IN_1_MINUTE = 1000 * 60
const MILLISECOND_IN_1_DAY = 86400000
import * as moment from 'moment'
import { TESTING_EXPIRATION_FLOW } from '@/const'
import * as process from "process";

const rule = new schedule.RecurrenceRule()
rule.dayOfWeek = [0, 1, 2, 3, 4, 5, 6]
rule.hour = 0
rule.minute = 1
rule.tz = 'Asia/Seoul'

export class ScheduleService {
    async scheduleAll() {
        this.scheduleExpiredShop()
        this.scheduleExpiredEvent()
        this.scheduleNotiBeforeExpiredShop()
        this.scheduleNotiBeforeExpiredEvent()
        this.scheduleUpdateUserNearestExpireShopDate()
        this.scheduleValidateShopViewData()
        this.scheduleUpdatePaidUserExpiration()
        this.scheduleBeforeUpdatePaidUserExpiration()

        this.scheduleJumpUp()
        this.scheduleSetUpRanking()
        this.scheduleSyncCompulsoryShopData()

        this.scheduleCompleteReservationData()
        this.scheduleDeniedReservationData()
        this.scheduleClearShopReservation()
        this.schedulePushNotification()
        this.scheduleCreatePost()
        this.scheduleCreateBlog()
        // this.scheduleReminderMessage()
        this.scheduleIndexing()
        // this.scheduleCheckSite()
    }

    scheduleSetUpRanking() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    // 0 0 */1 */1 * => daily
                    console.log('scheduleSetUpRanking start ')
                    const result = await userService.updateAllUserRanking()
                    console.log('scheduleSetUpRanking ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleJumpUp() {
        console.log('29148 start schedulejumpup ne')
        schedule.scheduleJob('*/5 * * * *', async () => {
            console.log('29148 5 phut 1 lan', moment().format('YYYY-MM-DD HH:mm'))
            shopService.periodicJumpUp()
        })
    }

    scheduleValidateShopViewData() {
        schedule.scheduleJob(rule, async () => {
            try {
                // 0 0 */1 */1 * => daily

                // update view from today to view_records:
                const result1 =
                    await shopService.scheduleUpdateShopLastThreeMonthsView()
                console.log('scheduleUpdateShopLastThreeMonthsView ', result1)

                // scheduleResetShopView phải gọi sau cùng sau khi đã cập nhật view vào các cái khác trước đó
                const result3 = await shopService.scheduleResetShopView()
                console.log('scheduleResetShopView ', result3)
            } catch (error) {
            }
        })
    }

    scheduleUpdatePaidUserExpiration() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    // 0 0 */1 */1 * => daily
                    console.log('scheduleUpdatePaidUserExpiration start ')
                    const result = await userService.scheduleUpdatePaidUserExpiration()
                    console.log('scheduleUpdatePaidUserExpiration ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleBeforeUpdatePaidUserExpiration() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    // 0 0 */1 */1 * => daily
                    console.log('scheduleUpdatePaidUserExpiration start ')
                    const result =
                        await userService.scheduleBeforeUpdatePaidUserExpiration()
                    console.log('scheduleUpdatePaidUserExpiration ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleExpiredShop() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    // 0 0 */1 */1 * => daily
                    console.log('scheduleExpiredShop start ')
                    const result = await shopService.scheduleExpiredShop()
                    console.log('scheduleExpiredShop ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleExpiredEvent() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    console.log('scheduleExpiredEvent start ')
                    const result = await eventService.scheduleExpiredEvent()
                    console.log('scheduleExpiredEvent ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleNotiBeforeExpiredShop() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    // 0 0 */1 */1 * => daily
                    console.log('scheduleNotiBeforeExpiredShop start ')
                    const result = await shopService.scheduleNotiBeforeExpiredShop()
                    console.log('scheduleNotiBeforeExpiredShop ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleNotiBeforeExpiredEvent() {
        schedule.scheduleJob(
            TESTING_EXPIRATION_FLOW ? '*/2 */1 */1 */1' : rule,
            async () => {
                try {
                    // 0 0 */1 */1 * => daily
                    console.log('scheduleNotiBeforeExpiredEvent start ')
                    const result = await eventService.scheduleNotiBeforeExpiredEvent()
                    console.log('scheduleNotiBeforeExpiredEvent ', result)
                } catch (error) {
                }
            }
        )
    }

    scheduleUpdateUserNearestExpireShopDate() {
        schedule.scheduleJob(rule, async () => {
            try {
                // 0 0 */1 */1 * => daily
                console.log('scheduleUpdateUserNearestExpireShopDate start ')
                const result =
                    await userService.scheduleUpdateUserNearestExpireShopDate()
                console.log('scheduleUpdateUserNearestExpireShopDate ', result)
            } catch (error) {
            }
        })
    }

    scheduleSyncCompulsoryShopData() {
        schedule.scheduleJob(rule, async () => {
            try {
                // 0 0 */1 */1 * => daily
                console.log('scheduleSyncCompulsoryShopData start ')
                const result = await shopService.scheduleSyncCompulsoryShopData()
                console.log('scheduleSyncCompulsoryShopData ', result)
            } catch (error) {
            }
        })
    }

    scheduleCompleteReservationData() {
        schedule.scheduleJob('*/10 * * * *', async () => {
            try {
                // */10 * * * * ===> every 10 minutes
                console.log(
                    'scheduleCompleteReservationData start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                await reservationService.scheduleCompleteReservationData()
                console.log('scheduleCompleteReservationData ')
            } catch (error) {
                console.log(
                    '29148  ~ file: scheduleService.ts ~ line 192 ~ ScheduleService ~ schedule.scheduleJob ~ error',
                    error
                )
            }
        })
    }

    scheduleDeniedReservationData() {
        schedule.scheduleJob('*/1 * * * *', async () => {
            try {
                // */10 * * * * ===> every 10 minutes
                console.log(
                    'scheduleDeniedReservationData start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                const result = await reservationService.scheduleDeniedReservationData()
                console.log('scheduleDeniedReservationData ', result)
            } catch (error) {
                console.log(
                    '29148  ~ file: scheduleService.ts ~ line 192 ~ ScheduleService ~ schedule.scheduleJob ~ error',
                    error
                )
            }
        })
    }

    scheduleClearShopReservation() {
        schedule.scheduleJob(rule, async () => {
            try {
                console.log(
                    'scheduleClearShopReservation start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                const result = await reservationService.scheduleClearShopReservation()
                console.log('scheduleClearShopReservation ', result)
            } catch (error) {
                console.log(
                    '29148  ~ file: scheduleService.ts ~ line 192 ~ ScheduleService ~ schedule.scheduleJob ~ error',
                    error
                )
            }
        })
    }

    schedulePushNotification() {
        schedule.scheduleJob('* * * * *', () => {
            pushNotificationService.pushNotification()
        })
    }

    scheduleCreatePost() {
        schedule.scheduleJob('*/30 * * * * *', () => {
            try {
                console.log(
                    'scheduleCreatePost start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                postService.runCreate()
            } catch (error) {
                console.log('error', error)
            }

        })
    }

    scheduleCreateBlog() {
        schedule.scheduleJob('*/30 * * * * *', () => {
            try {
                console.log(
                    'scheduleCreateBlog start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                blogService.runCreate()
            } catch (error) {
                console.log('error', error)
            }

        })
    }

    scheduleReminderMessage() {
        //every 5 minutes
        schedule.scheduleJob('*/5 * * * *', async () => {
            try {
                console.log(
                    'scheduleReminderMessage start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                await messageService.scheduleReminder()
            } catch (error) {
                console.log('error', error)
            }
        })
    }

    scheduleNotificationReservation() {
        //every 5 minutes
        schedule.scheduleJob('*/5 * * * *', async () => {
            try {
                console.log(
                    'scheduleNotificationReservation start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                await reservationService.scheduleNotification()
            } catch (error) {
                console.log('error', error)
            }
        })
    }

    scheduleCongratulationPostOfWeek() {
        //every week on Saturday at 21:00
        schedule.scheduleJob('0 21 * * 6', async () => {
            try {
                console.log(
                    'scheduleCongratulationPostOfWeek start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                await postService.scheduleCongratulation()
            } catch (error) {
                console.log('error', error)
            }
        })
    }

    scheduleCommentPostOfWeek() {
        //every week on Saturday at 21:00
        schedule.scheduleJob('0 21 * * 6', async () => {
            try {
                console.log(
                    'scheduleCommentPostOfWeek start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                await reviewService.scheduleCongratulation()
            } catch (error) {
                console.log('error', error)
            }
        })
    }

    scheduleIndexing() {
        //every day in 6h00 AM Korea time
        schedule.scheduleJob('0 21 * * *', async () => {
            try {
                console.log(
                    'scheduleIndexing start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                if (process.env.SEO === 'kormsg' || process.env.SEO === 'newbkshop') {
                    await keywordService.Indexing()
                }
            } catch (error) {
                console.log('error', error)
            }
        })

    }

    scheduleCheckSite() {
        schedule.scheduleJob('0 23 * * *', async () => {
            try {
                console.log(
                    'scheduleCheckSite start ',
                    moment().format('YYYY-MM-DD HH:mm:SSSS')
                )
                await siteService.checkHealth()
            } catch (error) {
                console.log('error', error)
            }
        })
    }
}
