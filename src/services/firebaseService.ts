import { errorService, userService } from '@/services'
import { config } from '@/config'
import * as admin from 'firebase-admin'
import * as _ from 'lodash'
import axios from 'axios'
import { JSON } from 'sequelize'

const NO_SOUND_STR = 'kormsg.mp3'
const NO_SOUND_STR_IOS = 'kormsg.caf'
const APP_NAME = '굿베트남'
export class FirebaseService {
  constructor() {
    admin.initializeApp({
      credential: admin.credential.cert(
        config.firebase as admin.ServiceAccount
      ),
      databaseURL: config.firebaseDbURL,
    })
  }

  get messaging() {
    return admin.messaging()
  }
  async verifyIdToken(token: string) {
    try {
      return await admin.auth().verifyIdToken(token)
    } catch (err) {
      throw errorService.firebase.cannotDecodeToken(err)
    }
  }
  async createUser(params: { email: string; password: string }) {
    try {
      return await admin.auth().createUser({
        email: params.email,
        password: params.password,
      })
    } catch (err) {
      throw err
    }
  }
  async getUserByEmail(params: { email: string }) {
    try {
      return await admin.auth().getUserByEmail(params.email)
    } catch (err) {
      throw err
    }
  }
  async getUserByPhone(params: { phone: string }) {
    try {
      return await admin.auth().getUserByPhoneNumber(params.phone)
    } catch (err) {
      throw err
    }
  }

  async sendNotification(
    user_id: string,
    message: string,
    action: string,
    entity_id: string = '',
    options: any = {
      priority: 'high',
      timeToLive: 2419200, // 28 days
    },
    sound : boolean = true
  ) {
    console.log('sound',sound)
    const soundAndroid = sound ? NO_SOUND_STR : undefined
    const soundIos = sound ? NO_SOUND_STR_IOS : undefined
    const topic = '/topics/' + user_id
    console.log('My Topic: ', topic)

    const user = await userService.getUser(user_id)
    if (!user) {
      return
    }

    const payloadForeground: any = {
      data: {
        message,
        action,
        click_action: 'FLUTTER_NOTIFICATION_CLICK',
        entity_id,
        channelId: sound ?  'kormassage_with_sound' : 'kormassage_no_sound',
      },

      topic,
    }
    if (!user.noti_sound) {
      payloadForeground.data.sound = soundAndroid
    }

    const payloadBackground: any = {
      apns: {
        headers: {
          'apns-priority': '10',
          'apns-expiration': '1604750400', // 11/07/2020, unixTimestamp
        },
        payload: {
          aps: {
            alert: {
              // title: APP_NAME,
              body: message,
            }, //
            category: action + ',' + entity_id,
            sound: soundIos,
            content_available: 1,
          },
          message,
          action,
          entity_id,
        },
      },
      android: {
        ttl: 2419200, // 28 days
        notification: {
          // title: APP_NAME,
          body: message,
          // defaultSound: true,
          notificationCount: 1,
          channelId: sound ?  'kormassage_with_sound' : 'kormassage_no_sound',
          sound: soundAndroid,
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        },
        data: {
          message,
          action,
          entity_id,
        },
        priority: options.priority,
      },
      topic,
    }
    try {
      admin.messaging().send(payloadBackground)
      admin.messaging().send(payloadForeground)
    } catch (error) {
      console.log('send notification failed', error)
    }
  }
  async sendNotificationV2(
    user_ids: any,
    title: string,
    message: string,
    action: string,
    entity_id: string = '',
    options: any = {
      priority: 'high',
      timeToLive: 2419200, // 28 days
    }
  ) {
    if (!user_ids || user_ids.length === 0) {
      return
    }
    console.log('29148 sendNotificationV2 ~ user_ids', user_ids)

    const haveNotiSoundUsers = []
    const noNotiSoundUsers = []

    if (user_ids.length === 1 && user_ids[0] && user_ids[0] === 'main') {
      console.log('29148 sendNotificationV2 nhan ne 1')
      haveNotiSoundUsers.push('main')
      noNotiSoundUsers.push('main')
    } else {
      console.log('29148 sendNotificationV2 nhan ne 2')
      for (let i = 0; i < user_ids.length; i++) {
        const user = await userService.getUser(user_ids[i])
        if (user.noti_sound) {
          haveNotiSoundUsers.push(user.id)
        } else {
          noNotiSoundUsers.push(user.id)
        }
      }
    }

    // let conditionSoundUsers = `'${haveNotiSoundUsers[0]}' in topics`;
    // haveNotiSoundUsers.forEach((id: any, index: number) => {
    //   if (index > 0) {
    //     conditionSoundUsers += ` || '${id}' in topics`;
    //   }
    // });
    // // no sound user:
    // let conditionNoSoundUsers = `'${noNotiSoundUsers[0]}' in topics`;
    // haveNotiSoundUsers.forEach((id: any, index: number) => {
    //   if (index > 0) {
    //     conditionNoSoundUsers += ` || '${id}' in topics`;
    //   }
    // });
    const conditionSoundUsers = '/topics/' + 'main'
    const conditionNoSoundUsers = '/topics/' + 'main_no_sound'

    const payloadForeground: any = {
      data: {
        message,
        action,
        title,
        click_action: 'FLUTTER_NOTIFICATION_CLICK',
        entity_id,
      },
    }

    const payloadBackground: any = {
      apns: {
        headers: {
          'apns-priority': '10',
          'apns-expiration': '1604750400', // 11/07/2020, unixTimestamp
        },
        payload: {
          aps: {
            alert: {
              title,
              body: message,
            }, //
            category: action + ',' + entity_id,
            content_available: 1,
          },
          message,
          action,
          entity_id,
        },
      },
      android: {
        ttl: 2419200, // 28 days
        notification: {
          title,
          body: message,
          defaultSound: true,
          notificationCount: 1,
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        },
        data: {
          message,
          action,
          entity_id,
        },
        priority: options.priority,
      },
    }

    const payloadForegroundWithSound = { ...payloadForeground }
    payloadForegroundWithSound.data.channelId = 'kormassage_with_sound'
    payloadForegroundWithSound.topic = conditionSoundUsers

    const payloadForegroundNoSound = { ...payloadForeground }
    payloadForegroundNoSound.data.channelId = 'kormassage_no_sound'
    payloadForegroundNoSound.topic = conditionNoSoundUsers
    payloadForegroundNoSound.data.sound = NO_SOUND_STR

    const payloadBackgroundWithSound = { ...payloadBackground }
    payloadBackgroundWithSound.apns.payload.aps.sound = 'default'
    payloadBackgroundWithSound.android.notification.channelId =
      'kormassage_with_sound'
    payloadBackgroundWithSound.android.notification.sound = 'default'
    payloadBackgroundWithSound.topic = conditionSoundUsers

    const payloadBackgroundNoSound = { ...payloadBackground }
    payloadBackgroundNoSound.apns.payload.aps.sound = ''
    payloadBackgroundNoSound.android.notification.channelId =
      'kormassage_no_sound'
    payloadBackgroundNoSound.android.notification.sound = NO_SOUND_STR
    payloadBackgroundNoSound.topic = conditionNoSoundUsers

    console.log(
      '29148  ~ file: firebaseService.ts ~ line 79 ~ FirebaseService ~ payloadForeground',
      payloadForeground
    )

    try {
      //
      admin.messaging().send(payloadForegroundWithSound)
      admin.messaging().send(payloadForegroundNoSound)
      admin.messaging().send(payloadBackgroundWithSound)
      admin.messaging().send(payloadBackgroundNoSound)
    } catch (error) {
      console.log('send notification failed', error)
      throw error
    }
  }
}
