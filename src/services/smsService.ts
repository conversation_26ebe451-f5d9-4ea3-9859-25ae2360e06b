import axios from 'axios'
import * as moment from 'moment'
import {Code} from "@/models";
import * as process from "process";

const popbill = require('popbill')
popbill.config({
    LinkID: process.env.POPBILL_LINK_ID,
    SecretKey: process.env.POPBILL_SECRET_KEY,
    IsTest: false,
    IPRestrictOnOff: false,
    defaultErrorHandler: function (Error: any) {
        console.log('Error Occur : [' + Error.code + '] ' + Error.message)
    },
})

export enum MessageType {
    'USER_RESERVATION',
    'SHOP_RESERVATION',
    'USER_CANCEL',
    'SHOP_REJECT',
    'SHOP_APPROVE'
}

export type BodyMessage = {
    shop_title: string
    quantity: number
    course_name: string
    date: string
    price: string
    username: string
    nickname: string
    shop_phone_number: string
    user_phone_number: string
}


export class SmsService {
    constructor() {
        this.message = popbill.MessageService()
    }

    message: any

    async sendSms(phonenumber: string, content: string) {
        try {
            const {data} = await axios.post('http://*************:8000/sms', {
                phone: phonenumber,
                message: content
            })
            return data
        } catch (error) {
            console.log('error', error);
        }
    }

    async generateCode(): Promise<number> {
        const code = Math.floor(100000 + Math.random() * 900000);
        const checkCode = await Code.findOne({
            where: {code: code.toString()}
        });
        if (checkCode) {
            return this.generateCode();
        }
        return code;
    }

    async sendOtp(phonenumber: string) {
        try {
            const code = await this.generateCode();
            const message = `[${process.env.TEXT_SMS}] 본인확인을 위해 인증번호 ${code}를 입력해 주세요`;
            await Code.create({
                code: code.toString(),
                phonenumber
            } as any);
            return this.sendSms(phonenumber, message);
        } catch (e) {
            console.log('error', e);
            throw e;
        }
    }

    async verifyOtp(phonenumber: string, otp: string): Promise<boolean> {
        const checkCode = await Code.findOne({
            where: {code: otp, phonenumber}
        });
        if (!checkCode) {
            return false;
        }
        const now = moment()
        const diff = moment.duration(now.diff(checkCode.createdAt));
        const timeVerify = diff.asSeconds()
        await Code.destroy({where: {code: otp, phonenumber}});
        return timeVerify < 5 * 60;
    }

    async sendLongMessage(content: string = '', receiver: string) {
        this.message.sendLMS(
            process.env.POPBILL_CORP_NUMBER,
            process.env.POPBILL_SENDER,
            receiver,
            '',
            `${process.env.TITLE_SMS} 예약관련 안내`,
            content,
            '',
            false,
            '',
            '',
            (response: any) => {
                console.log(response)
            },
            (error: any) => {
                console.log(error)
            }
        )
    }

    async sendMessageToBoss(content: string = '') {
        this.message.sendLMS(
            process.env.POPBILL_CORP_NUMBER,
            process.env.POPBILL_SENDER,
            '01067525666',
            '',
            `Google Indexing Result`,
            content,
            '',
            false,
            '',
            '',
            (response: any) => {
                console.log(response)
            },
            (error: any) => {
                console.log(error)
            }
        )
    }

    async notifyToBoss(content: string = '') {
        this.message.sendLMS(
            process.env.POPBILL_CORP_NUMBER,
            process.env.POPBILL_SENDER,
            '01067525666',
            '',
            `New Contact Form`,
            content,
            '',
            false,
            '',
            '',
            (response: any) => {
                console.log(response)
            },
            (error: any) => {
                console.log(error)
            }
        )
    }


}

export function createMessage(type: MessageType, body: BodyMessage) {
    let message = ''
    switch (type) {
        case MessageType.USER_RESERVATION: {
            message = `
예약 안내
아래의 업체가 예약이 접수되었습니다.

매장명: ${body.shop_title}
예약인원수: ${body.quantity}명 
코스명: ${body.course_name}
요금금액: ${body.price}
예약시간: ${body.date}
예약자명: ${body.nickname || body.username}
매장전화번호: ${body.shop_phone_number}

더 자세한 사항은 앱에서 확인바랍니다.
${process.env.DOMAIN}`
            break
        }
        case MessageType.SHOP_RESERVATION: {
            message = `
예약 안내
아래의 업체가 예약이 접수되었습니다.

회원닉네임: ${body.nickname}
예약인원수: ${body.quantity}명
코스명: ${body.course_name}
요금금액: ${body.price}
예약시간: ${body.date}
예약자명: ${body.nickname || body.username}
고객전화번호: ${body.user_phone_number}

더 자세한 사항은 앱에서 확인바랍니다.
${process.env.DOMAIN}`
            break
        }
        case MessageType.USER_CANCEL: {
            message = `
예약취소안내(고객취소)
아래의 업체가 예약취소되었습니다.
      
매장명: ${body.shop_title}
예약인원수: ${body.quantity}명
코스명: ${body.course_name}
요금금액: ${body.price}
예약시간: ${body.date}
예약자명: ${body.nickname || body.username}
매장전화번호: ${body.shop_phone_number}
      
더 자세한 사항은 앱에서 확인바랍니다.
${process.env.DOMAIN}`
            break
        }

        case MessageType.SHOP_REJECT: {
            message = `
예약취소안내(고객취소)
아래의 업체가 예약취소되었습니다.

회원닉네임: ${body.nickname}
예약인원수: ${body.quantity}명
코스명: ${body.course_name}
요금금액: ${body.price}
예약시간: ${body.date}
예약자명: ${body.nickname || body.username}
고객전화번호: ${body.user_phone_number}                                

더 자세한 사항은 앱에서 확인바랍니다.
${process.env.DOMAIN}`
            break
        }
        case MessageType.SHOP_APPROVE: {
            message = `
매장에서 예약을 수락하였습니다! 약속한 시간에 행복한 경험을 기대하세요~

회원닉네임: ${body.shop_title}
예약인원수: ${body.quantity}명
코스명: ${body.course_name}
요금금액: ${body.price}
예약시간: ${body.date}
예약자명: ${body.nickname || body.username}
고객전화번호: ${body.shop_phone_number}                                

더 자세한 사항은 앱에서 확인바랍니다.
${process.env.DOMAIN}`
            break
        }
        default: {
            break
        }
    }
    return message
}