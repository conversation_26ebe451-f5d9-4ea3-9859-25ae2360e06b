import { BaseError } from './base'
class DatabaseException extends BaseError {
  constructor(key: string, message: string, code?: number) {
    super({
      code: code || 500,
      type: `database_exception_${key}`,
      message,
    })
  }
}
export class DatabaseErrorService {
  recordNotFound() {
    return new DatabaseException('record_not_found', 'Record Not Found')
  }
  queryFail(message: string = 'Query Fail', code = 500) {
    return new DatabaseException('query_fail', message, code)
  }

  queryError(errorObj: any) {
    const message =
      errorObj && errorObj.message ? errorObj.message : 'Query Fail'
    const code = errorObj && errorObj.code ? errorObj.code : 500
    return new DatabaseException('query_fail', message, code)
  }
  invalidScope(message: string = 'Invalid scope', code = 500) {
    return new DatabaseException('invalid_scope', message, code)
  }
}
