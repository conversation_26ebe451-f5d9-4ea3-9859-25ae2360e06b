import { CrudService, ICrudOption } from '../crudService.pg'
import { ReservationItem } from '@/models/tables'
import { config } from '@/config'
import {
    sequelize
} from '@/models'

import {
    tokenService,
    firebaseService,
    errorService
} from '@/services'
export class ReservationItemService extends CrudService<typeof ReservationItem> {
    constructor() {
        super(ReservationItem)
    }
}