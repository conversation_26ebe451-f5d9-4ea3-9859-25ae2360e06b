import { CrudService, ICrudOption } from '../crudService.pg'
import { SeoSsr } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'

const DEFAULT_ID = 1

import { tokenService, firebaseService, errorService } from '@/services'
import { seoUrl } from "@/config/seo";
export class SeoSsrService extends CrudService<typeof SeoSsr> {
  constructor() {
    super(SeoSsr)
  }

  async get() {
    const [result] = await this.exec(
      this.model.findOrCreate({
        where: { id: DEFAULT_ID },
        defaults: { id: DEFAULT_ID },
      })
    )
    return result
  }

  async update(params: any) {
    const item = await this.get()
    await this.exec(item.update(params, { where: { id: item.id } }))
    return item
  }

   getSeo(province: string, district: string, thema_id: string) {
      if (!seoUrl) return null
    const seo = seoUrl.find((item) => item.province === province && item.district === district && item.thema_id === thema_id)
    if (!seo) return null
    return seo
  }
}
