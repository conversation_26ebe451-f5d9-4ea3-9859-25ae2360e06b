import { CrudService, ICrudOption } from '../crudService.pg';
import { SiteBanner} from '@/models/tables';

import { tokenService, firebaseService, errorService } from '@/services';
import {Sequelize} from "@/models/base.pg";
import { config } from "@/config";
export class SiteBannerService extends CrudService<typeof SiteBanner> {
  constructor() {
    super(SiteBanner);
  }

  async create(params: any, option?: ICrudOption) {
    const lastItem = await this.model.findOne({
      order: [[
        'index', 'DESC'
      ]]
    })
    params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
    return await this.exec(
        this.model.create(params, this.applyCreateOptions(option))
    )
  }

  async dragDropItem(id: string, params: any) {
    let {prev_index_number, next_index_number} = params
    let currElIndexNumber;
    if (prev_index_number === undefined) {
      currElIndexNumber = next_index_number - 512;
    } else if (next_index_number === undefined) {
      currElIndexNumber = prev_index_number + 512;
    } else {
      currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
    }
    try {
      const update = await this.model.update({index: currElIndexNumber}, {
        where: {
          id
        }
      })
      if (
          Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
          Math.abs(currElIndexNumber - next_index_number) <= 1
      ) {
        const items = await this.model.findAll({
          attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
        })
        await Promise.all(
            items.map(async (element) => {
              await this.model.update({index: element.dataValues.orderedData * 1024}, {
                where: {
                  id: element.id
                }
              })
            })
        );
      }
      return update
    } catch (e) {
      console.log(e)
    }
  }

  async getList(
      option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
        order : [['index', 'ASC']]
      }
  ) {
    return await this.exec(
        this.modelWithScope(option.scope).findAndCountAll(
            this.applyFindOptions(option)
        )
    )
  }
}
