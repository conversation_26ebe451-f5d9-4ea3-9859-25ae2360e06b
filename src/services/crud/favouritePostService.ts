import { CrudService, ICrudOption } from '../crudService.pg';
import { FavouritePost } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService, rewardService } from '@/services';
export class FavouritePostService extends CrudService<typeof FavouritePost> {
  constructor() {
    super(FavouritePost);
  }
  async create(params: any, option?: ICrudOption) {
    const transaction = await sequelize.transaction();

    try {
      const favourite = await super.create(params, { ...option, transaction });

      await rewardService.addReward(params.user_id, 'POST_LIKED');

      await transaction.commit();
      return favourite;
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }
}
