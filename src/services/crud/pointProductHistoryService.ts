import {firebaseService} from "@/services";

const AWS = require('aws-sdk')
const xlsx = require('node-xlsx')
const fs = require('fs')
import * as moment from 'moment'

import { config } from '@/config'
import {Notification, PointProductHistory, User} from '@/models'
import { CrudService, ICrudOption } from '../crudService.pg'
import {FCM_ACTIONS, FCM_ACTIONS_MESSAGE, getFcmActionMessage} from "@/const";

const ID = process.env.AWS_ID
const SECRET = process.env.AWS_SECRET_KEY
const BUCKET_NAME = process.env.AWS_BUCKET_NAME
const FILE_IMAGE_PATH = 'image/'

export class PointProductHistoryService extends CrudService<
  typeof PointProductHistory
> {
  constructor() {
    super(PointProductHistory)
  }

  async downloadExcel(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
    }
  ) {
    try {
      const result = await this.exec(
        this.model.findAndCountAll(this.applyFindOptions(option))
      )
      const s3 = new AWS.S3({
        accessKeyId: ID,
        secretAccessKey: SECRET,
      })
      const header = [
        'NO',
        '날짜',
        '아이디',
        '보유포인트',
        '신청상품',
        '차감포인트',
        '전화번호',
      ]
      const data = [header]
      for (let i = 0; i < result.rows.length; i++) {
        const pointProductHistory = result.rows[i]
        const rowData = [
          i + 1,
          moment(pointProductHistory.created_at).format('YYYY.MM.DD'),
          pointProductHistory.user_name,
          pointProductHistory.user_point,
          `${pointProductHistory.product.name} ${pointProductHistory.product.point}`,
          `-${pointProductHistory.product.point}`,
          pointProductHistory.user_phone ? pointProductHistory.user_phone : '',
        ]
        data.push(rowData)
      }
      const fileName = 'point_product_history' + moment().valueOf() + '.xlsx'
      const buffer = xlsx.build([{ name: 'data', data: data }]) // Returns a buffer
      fs.writeFileSync(FILE_IMAGE_PATH + fileName, buffer)
      const fileContent = fs.readFileSync(FILE_IMAGE_PATH + fileName)

      const awsParams = {
        Bucket: BUCKET_NAME,
        Key: fileName, // File name you want to save as in S3
        Body: fileContent,
        ACL: 'public-read',
      }
      // Uploading files to the bucket
      await s3.upload(awsParams, (err: any, data: any) => {
        if (err) {
          throw err
        }
      })
      const urls3High = `https://${process.env.IMAGE_URL}/${fileName}`

      fs.unlinkSync(FILE_IMAGE_PATH + fileName)
      return { url: urls3High, file_name: fileName }
    } catch (error) {
      throw error
    }
  }

  async confirm(id: string) {
    return this.update({ pending: true }, { filter: { id } })
  }
  async sent(id: string) {
    const pointProductHistory = await this.getItem({ filter: { id } })
    // if (!pointProductHistory.pending) {
    //   throw new Error('This point product history is not pending')
    // }
    const nameProduct = pointProductHistory.product.name
    const user = await User.findOne({where : {username: pointProductHistory.user_name}})
    const message = getFcmActionMessage(FCM_ACTIONS_MESSAGE.SENT_PRODUCT, {
        params_1: nameProduct,
    })
    const bodyNoti : any = {
      user_id: user.id,
      title: message,
      content: message,
      data: {
        message: message,
        params: {
          params_1: nameProduct,
        },
      },
      action: Number(FCM_ACTIONS.SENT_PRODUCT),
      interacting_type: 'SENT_PRODUCT',
      interacting_content_id: pointProductHistory.product.id,
    }
    await Promise.all([
      // firebaseService.sendNotification(
      //     user.id,
      //     message,
      //     FCM_ACTIONS.BUY_POINT_PRODUCT,
      //     pointProductHistory.product.id
      // ),
      this.exec(Notification.create(bodyNoti)),
    ])
    return this.update({ sent: true }, { filter: { id } })
  }

  async count(filter?:ICrudOption['filter']) {
    const total =  await this.exec(this.model.count({
      where: filter
    }))
    return {total}
  }
}
