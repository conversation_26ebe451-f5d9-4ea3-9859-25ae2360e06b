import { CrudService, ICrudOption } from '../crudService.pg';
import { RecentReading, Favourite } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService } from '@/services';
import { calcDistance } from '../socketService/util';
export class RecentReadingService extends CrudService<typeof RecentReading> {
  constructor() {
    super(RecentReading);
  }

  async getListRecentReading(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    try {
      const { latitude, longitude } = params;
    !option.include.find(e => e.association === 'shop') && option.include.push({ association: 'shop', include: [], distinct: true });
    !option.include.find(e => e.association === 'recruit') && option.include.push({ association: 'recruit', include: [], distinct: true });

    const options = this.applyFindOptions(option);
    options.distinct = true;
    const listData = await this.exec(
      this.modelWithScope(option.scope).findAndCountAll(options)
    );
    listData.total_rows = listData.count;

    for (let i = 0; i < listData.rows.length; i++) {
      const isShop = listData &&
      listData.rows[i] &&
      listData.rows[i].dataValues &&
      listData.rows[i].dataValues.shop_id;
      
      const item = isShop ? listData.rows[i].dataValues.shop.dataValues : listData.rows[i].dataValues.recruit.dataValues;

      if (isShop) {
        if (latitude && longitude) {
          item.distance = calcDistance(
            {
              latitude: latitude,
              longitude: longitude,
            },
            {
              latitude: item.latitude,
              longitude: item.longitude,
            }
          ); // Distance in km
        }
      } else {

      }

      if (params.user_id) {
        const query: any = {
          user_id: params.user_id,
        }
        if (isShop) {
          query.shop_id = item.id;
        } else {
          query.recruit_id = item.id;
        }

        const like = await this.exec(
          Favourite.findOne({
            where: query,
            attributes: ["id"],
          })
        );

        item.is_like = !!like;
      } else {
        item.is_like = false;
      }
    }
    return listData;
    } catch (error) {
      throw error;
    }
  }
}
