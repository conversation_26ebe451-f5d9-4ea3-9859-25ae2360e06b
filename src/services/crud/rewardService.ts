import { ExpRule, sequelize, User } from "@/models";
import { actionLogService } from "..";
import { Transaction } from "sequelize";

export class RewardService {
  async addReward(userId: string, actionType: string, transaction?: Transaction) {
    const isExternalTransaction = !!transaction;
    const t = transaction || await sequelize.transaction();

    try {
      const rule: any = await ExpRule.findOne({
        where: { action_type: actionType },
        transaction: t,
      });

      if (!rule) {
        throw new Error(`Reward rule not found for action type: ${actionType}`);
      }

      await actionLogService.create(
        {
          user_id: userId,
          action_type: actionType,
          exp: rule.exp,
          point: rule.point,
        },
        { transaction: t }
      );

      const [userReward, created] = await User.findOrCreate({
        where: { id: userId },
        defaults: { point: 0, exp: 0, level: 1 },
        transaction: t,
      });

      userReward.point += rule.point;
      userReward.exp += rule.exp;
      await userReward.save({ transaction: t });

      if (!isExternalTransaction) {
        await t.commit();
      }

      return userReward;
    } catch (err) {
      if (!isExternalTransaction) {
        await t.rollback();
      }
      throw err;
    }
  }
}
