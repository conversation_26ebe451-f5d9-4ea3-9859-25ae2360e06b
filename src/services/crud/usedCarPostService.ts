import { CrudService, ICrudOption } from '../crudService.pg';
import {
    CarManufacturer,
    CarModel,
    Category,
    FavouriteUsedCarPost,
    UsedCarPost,
    User,
} from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';
import * as moment from 'moment';
import { errorService } from '@/services';
import { isBase64EncodeStr } from '@/config/utils';
import { Op } from 'sequelize';

export class UsedCarPostService extends CrudService<typeof UsedCarPost> {
    constructor() {
        super(UsedCarPost);
    }

    async create(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction();
        try {
            // Filter out base64 encoded images (following postService pattern)
            if (params.images && params.images.length) {
                const filteredListImgs = params.images.filter((e: any) => !isBase64EncodeStr(e));
                params.images = filteredListImgs;
            }
            if (params.thumbnails && params.thumbnails.length) {
                const filteredListImgs = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
                params.thumbnails = filteredListImgs;
            }

            // Validate required relationships
            if (params.manufacturer_id) {
                const manufacturer = await CarManufacturer.findByPk(params.manufacturer_id);
                if (!manufacturer) {
                    throw errorService.router.badRequest('Invalid manufacturer_id');
                }
            }

            if (params.model_id) {
                const model = await CarModel.findByPk(params.model_id);
                if (!model) {
                    throw errorService.router.badRequest('Invalid model_id');
                }
            }

            const item = await this.exec(
                this.model.create(params, {
                    transaction
                })
            );

            await transaction.commit();
            return item;
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findByPk(option.filter.id), {
            allowNull: false,
        });

        // Filter out base64 encoded images
        if (params.images && params.images.length) {
            const filteredListImgs = params.images.filter((e: any) => !isBase64EncodeStr(e));
            params.images = filteredListImgs;
        }
        if (params.thumbnails && params.thumbnails.length) {
            const filteredListImgs = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
            params.thumbnails = filteredListImgs;
        }

        // Validate relationships if being updated
        if (params.manufacturer_id) {
            const manufacturer = await CarManufacturer.findByPk(params.manufacturer_id);
            if (!manufacturer) {
                throw errorService.router.badRequest('Invalid manufacturer_id');
            }
        }

        if (params.model_id) {
            const model = await CarModel.findByPk(params.model_id);
            if (!model) {
                throw errorService.router.badRequest('Invalid model_id');
            }
        }

        await this.exec(
            UsedCarPost.update(
                {
                    ...params,
                },
                {
                    where: {
                        id: item.id,
                    },
                }
            )
        );

        return await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            { allowNull: false }
        );
    }

    async getListUsedCarPosts(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        // Build filter conditions
        const whereConditions: any = { status: true };

        // Price filtering
        if (params.min_price !== undefined) {
            whereConditions.price = { ...whereConditions.price, [Op.gte]: params.min_price };
        }
        if (params.max_price !== undefined) {
            whereConditions.price = { ...whereConditions.price, [Op.lte]: params.max_price };
        }

        // Year filtering
        if (params.min_year !== undefined) {
            whereConditions.year = { ...whereConditions.year, [Op.gte]: params.min_year };
        }
        if (params.max_year !== undefined) {
            whereConditions.year = { ...whereConditions.year, [Op.lte]: params.max_year };
        }

        // State filtering
        if (params.state) {
            whereConditions.state = params.state;
        }

        // Manufacturer filtering
        if (params.manufacturer_id) {
            whereConditions.manufacturer_id = params.manufacturer_id;
        }

        // Model filtering
        if (params.model_id) {
            whereConditions.model_id = params.model_id;
        }

        // Fuel type filtering
        if (params.fuel_type) {
            whereConditions.fuel_type = params.fuel_type;
        }

        // Transmission filtering
        if (params.transmission) {
            whereConditions.transmission = params.transmission;
        }

        // Body type filtering
        if (params.body_type) {
            whereConditions.body_type = params.body_type;
        }

        // Condition filtering
        if (params.condition) {
            whereConditions.condition = params.condition;
        }

        // User filtering
        if (params.user_id) {
            whereConditions.user_id = params.user_id;
        }

        // Location-based filtering (if radius and coordinates provided)
        if (params.latitude && params.longitude && params.radius) {
            whereConditions[Op.and] = sequelize.literal(
                `ST_DWithin(position, ST_GeomFromText('POINT(${params.longitude} ${params.latitude})', 4326), ${params.radius})`
            );
        }

        const options = this.applyFindOptions({
            ...option,
            filter: { ...option.filter, ...whereConditions }
        });

        // Add relationships
        options.include = [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'avatar', 'phone'],
            },
            {
                model: CarManufacturer,
                as: 'manufacturer',
                attributes: ['id', 'name', 'logo_url', 'country'],
            },
            {
                model: CarModel,
                as: 'model',
                attributes: ['id', 'name', 'body_type', 'fuel_type', 'transmission'],
            },
        ];

        options.distinct = true;

        // Sorting
        if (params.sort_by) {
            const sortOrder = params.sort_order === 'desc' ? 'DESC' : 'ASC';
            switch (params.sort_by) {
                case 'price':
                case 'year':
                case 'mileage':
                case 'created_at':
                case 'view_count':
                    options.order = [[params.sort_by, sortOrder]];
                    break;
                default:
                    options.order = [['created_at', 'DESC']];
            }
        } else {
            options.order = [['created_at', 'DESC']];
        }

        const usedCarPostData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        );

        usedCarPostData.total_rows = usedCarPostData.count;

        // Add user-specific data if user is authenticated
        if (params.current_user_id) {
            for (let i = 0; i < usedCarPostData.rows.length; i++) {
                const favorite = await this.exec(
                    FavouriteUsedCarPost.findOne({
                        where: {
                            used_car_post_id: usedCarPostData.rows[i].dataValues.id,
                            user_id: params.current_user_id,
                        },
                        attributes: ["id"],
                    })
                );

                usedCarPostData.rows[i].dataValues.is_favorite = !!favorite;
            }
        } else {
            // Set default values for non-authenticated users
            for (let i = 0; i < usedCarPostData.rows.length; i++) {
                usedCarPostData.rows[i].dataValues.is_favorite = false;
            }
        }

        return usedCarPostData;
    }

    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        return await this.getListUsedCarPosts(params, option);
    }

    async getItem(
        params: any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const options = this.applyFindOptions(option);

        // Add relationships for detailed view
        options.include = [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'avatar', 'phone', 'email'],
            },
            {
                model: CarManufacturer,
                as: 'manufacturer',
                attributes: ['id', 'name', 'logo_url', 'country', 'description'],
            },
            {
                model: CarModel,
                as: 'model',
                attributes: ['id', 'name', 'body_type', 'fuel_type', 'transmission', 'engine_size', 'description'],
            },
        ];

        const item = await this.exec(
            this.modelWithScope(option.scope).findOne(options),
            { allowNull: false }
        );

        // Add user-specific data if user is authenticated
        if (params.current_user_id) {
            const favorite = await this.exec(
                FavouriteUsedCarPost.findOne({
                    where: {
                        used_car_post_id: item.id,
                        user_id: params.current_user_id,
                    },
                    attributes: ["id"],
                })
            );

            item.dataValues.is_favorite = !!favorite;
        } else {
            item.dataValues.is_favorite = false;
        }

        return item;
    }

    async viewPost(params: any, option?: ICrudOption) {
        // Increment view count
        await this.exec(
            UsedCarPost.update(
                {
                    view_count: sequelize.literal('view_count + 1'),
                },
                {
                    where: {
                        id: option.filter.id,
                    },
                }
            )
        );

        // Return updated post data
        return await this.getItem(params, option);
    }

    async updateState(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findByPk(option.filter.id), {
            allowNull: false,
        });

        const updateData: any = { state: params.state };

        // If marking as sold, set sold_at timestamp
        if (params.state === 'sold') {
            updateData.sold_at = new Date();
        }

        await this.exec(
            UsedCarPost.update(updateData, {
                where: {
                    id: item.id,
                },
            })
        );

        return await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            { allowNull: false }
        );
    }

    async delete(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findByPk(option.filter.id), {
            allowNull: false,
        });

        // Soft delete using paranoid
        await this.exec(
            UsedCarPost.destroy({
                where: {
                    id: item.id,
                },
            })
        );

        return { success: true, message: 'Used car post deleted successfully' };
    }

    async countByUser(user_id: string) {
        return await this.exec(
            UsedCarPost.count({
                where: {
                    user_id: user_id,
                    status: true,
                },
            })
        );
    }
}

export const usedCarPostService = new UsedCarPostService(); 