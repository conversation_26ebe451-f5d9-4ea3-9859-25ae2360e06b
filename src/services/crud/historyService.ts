import { CrudService, ICrudOption } from '../crudService.pg';
import { History, Post, Review, User } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService } from '@/services';
import { HISTORY } from '@/const';
export class HistoryService extends CrudService<typeof History> {
  constructor() {
    super(History);
  }
}
