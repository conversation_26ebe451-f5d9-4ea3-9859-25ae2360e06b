// @ts-ignore
import * as cheerio from 'cheerio';
import {CrudService, ICrudOption} from '../crudService.pg'
import {Blog} from '@/models'
import * as moment from 'moment'
import {ViewBlog} from "@/models/tables/view_blog";
import {Op} from "sequelize";
import * as uuid from 'uuid'
import {config} from "@/config";
import {generateSlug} from "@/config/utils";
import {errorService} from "@/services";

export class BlogService extends CrudService<typeof Blog> {
    constructor() {
        super(Blog)
    }

    async getItem(
        params : any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params;
        const id = option.filter.id
        const where : Record<string, any> = {}
        if (uuid.validate(id)) {
            where['id'] = id
        }
        else {
            where['slug'] = id
        }
        const blog: any = await this.model.findOne({where})
        if (!blog) {
            throw errorService.database.recordNotFound()}
        blog.view++
        blog.save()
        if (user_id) {
            await ViewBlog.create({blog_id: blog.id, user_id} as any)
        }
        option.filter = where
        const data =  await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
        if (data.content) {
            const $ = cheerio.load(data.content,{ _useHtmlParser2: true });
            // @ts-ignore
            const content = $.text()
            data.dataValues.description = content ? content.trim().split(".").slice(0, 4).join('.') : ""
        }
        return data
    }

    async statisticBlog(id: string, startDate: Date, endDate: Date) {
        const listDate = this.enumerateDaysBetweenDates(startDate, endDate)
        const result = []
        const startOfDay = moment().utc().add(9, 'hour').startOf('days').subtract(9, 'hour').toDate();
        const endOfDay = moment().utc().add(9, 'hour').endOf('days').subtract(9, 'hour').toDate();
        const totalData = await ViewBlog.count({where: {blog_id: id}})
        const totalDataToday = await ViewBlog.count({
            where: {
                blog_id: id,
                created_at: {
                    [Op.between]: [startOfDay, endOfDay]
                }
            }
        })
        for (const date of listDate) {
            const startOfDay = moment(date).utc().add(9, 'hour').startOf('days').subtract(9, 'hour').toDate();
            const endOfDay = moment(date).utc().add(9, 'hour').endOf('days').subtract(9, 'hour').toDate();
            const viewBlogs = await ViewBlog.findAll({
                where: {
                    blog_id: id,
                    created_at: {
                        [Op.between]: [startOfDay, endOfDay]
                    }
                }
            })
            result.push({
                view: viewBlogs.length,
                date: moment(date).format('YYYY-MM-DD')
            })
        }
        return {
            totalData, totalDataToday, rangeData: result
        }
    }

    enumerateDaysBetweenDates(startDate: Date, endDate: Date): Date[] {
        const currDate = moment(startDate).startOf('d');
        const lastDate = moment(endDate).startOf('d');
        const dates: Date[] = [currDate.toDate()];
        while (currDate.add(1, 'd').diff(lastDate) <= 0) {
            dates.push(currDate.clone().toDate());
        }
        return dates;
    }

    async create(params: any, option?: ICrudOption) {
        params.status = !params.execute_at;
        params.execute_at = params.execute_at ? new Date(params.execute_at) : null
        let slug = generateSlug(params.title)
        const checkSlug = await this.model.findOne({where: {slug}})
        if (checkSlug) {
            slug = slug + '-' + uuid.v4()
        }
        params.slug = slug
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
    }

    async runCreate() {
        await this.model.update({status: true, created_at: new Date(), updated_at: new Date()}, {
            where: {
                status: false,
                execute_at: {
                    [Op.lte]: new Date()
                }
            }
        })
    }

    async createByClient( data: any, option?: ICrudOption){
        data.status = false
        let slug = generateSlug(data.title)
        const checkSlug = await this.model.findOne({where: {slug}})
        if (checkSlug) {
            slug = slug + '-' + uuid.v4()
        }
        data.slug = slug
        return await this.exec(
            this.model.create(data, this.applyCreateOptions(option))
        )
    }

    async rankBlog(){
        return await this.model.findAll({
            order: [['view', 'DESC']],
            limit: 10
        })
    }

    async delete(option?: ICrudOption) {
        const item = await this.exec(this.getItem({},option), { allowNull: false })
        return await this.exec(item.destroy())
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        if (params.title && item.title !== params.title) {
            let slug = generateSlug(params.title)
            const checkSlug = await this.model.findOne({where: {slug}})
            if (checkSlug) {
                slug = slug + '-' + uuid.v4()
            }
            params.slug = slug
        }
        await this.exec(item.update(params))
        return await this.getItem({},option)
    }
}

