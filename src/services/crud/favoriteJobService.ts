import { CrudService, ICrudOption } from "../crudService.pg";
import { FavoriteJob, JobRequest } from "@/models/tables";
import { config } from "@/config";
import { sequelize } from "@/models";
import { errorService } from "@/services";
export class FavoriteJobService extends CrudService<typeof FavoriteJob> {
  constructor() {
    super(FavoriteJob);
  }
  async createFavorite(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const { user_id, job_id } = params;

    const exists = await this.model.findOne({
      where: {
        user_id: user_id,
        job_id: job_id,
      },
    });

    if (exists) {
      throw errorService.database.queryFail(
        "You have already favored this job."
      );
    }
    const data = {
      user_id: user_id,
      job_id: job_id,
    } as any;
    const favorite = await this.model.create(data);
    await JobRequest.update(
      { is_favorite: true },
      { where: { id: data.job_id } }
    );

    return favorite;
  }
  async removeFavorite(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const { user_id, job_id } = params;
    await this.model.destroy({
      where: { job_id, user_id },
    });
    await JobRequest.update({ is_favorite: false }, { where: { id: job_id } });

    return { success: true };
  }
}
