import { CrudService, ICrudOption } from '../crudService.pg'
import { AdvertisingImage } from '@/models/tables'
import { config } from '@/config'
export class AdvertisingImageService extends CrudService<typeof AdvertisingImage> {
    constructor() {
        super(AdvertisingImage)
    }
    async getList(params: any, option: ICrudOption = {
          limit: config.database.defaultPageSize,
          offset: 0,
          scope: ['defaultScope'],
        }) {
          const filter: any = {};
         
          if (option.filter && option.filter.position) {
            filter.position = option.filter.position;
          }    
          if (option.filter && option.filter.page_key) {
            filter.page_key = option.filter.page_key;
          }  
          option.filter = {
            ...option.filter,
            ...filter,
          };
        
          return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
              this.applyFindOptions(option)
            )
          );
        }
}