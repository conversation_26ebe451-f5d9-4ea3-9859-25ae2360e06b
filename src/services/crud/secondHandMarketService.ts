import {CrudService, ICrudOption} from '../crudService.pg'
import { CallShop, FavouriteMentor, FavouriteSecondHand, SecondHandMarket, ShopTag } from '@/models/tables'
import {sequelize} from "@/models";
import { config } from "@/config";
import { PRODUCT_STATE } from "@/const";
import { errorService } from "@/services";

export class SecondHandMarketService extends CrudService<typeof SecondHandMarket> {
    constructor() {
        super(SecondHandMarket)
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update(params))
        return await this.getItem(params,option)
    }
    async delete(option?: ICrudOption) {
        const item = await this.exec(this.getItem({},option), { allowNull: false })
        return await this.exec(item.destroy())
    }

    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params
        const data =  await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        for (const item of data.rows) {
            const favourite = await this.exec(
                FavouriteSecondHand.findOne({
                    where: {
                        user_id,
                        second_hand_market_id: item.id
                    }
                })
            )
            item.dataValues.is_like = !!favourite
        }
        return data
    }

    async getItem(
        params: any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params
        const item = await this.exec(this.model.findOne(this.applyFindOptions(option)), {
            allowNull: false,
        })
        const favourite = await this.exec(
            FavouriteSecondHand.findOne({
                where: {
                    user_id,
                    second_hand_market_id: item.id
                }
            })
        )
        item.dataValues.is_like = !!favourite
        return item
    }

    async call(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update({call: item.call + 1}))
        return await this.getItem(option)
    }

    onChangeProductLikeStatus = async (
        user_id: string,
        second_hand_market_id: string,
        status: boolean
    ) => {
        const transaction = await sequelize.transaction()
        try {
            const bodyFS: any = {user_id: user_id, second_hand_market_id: second_hand_market_id}

            const tempFind = await this.exec(
                FavouriteSecondHand.findOne({
                    where: bodyFS,
                    attributes: ['id'],
                })
            )

            if (status) {
                if (!tempFind)
                    await this.exec(FavouriteSecondHand.create(bodyFS, {transaction}))
            } else {
                if (tempFind) {
                    await this.exec(
                        FavouriteSecondHand.destroy({
                            where: {
                                id: tempFind.id,
                            },
                            transaction,
                        })
                    )
                }
            }

            const product = await this.exec(this.model.findById(second_hand_market_id), {
                allowNull: false,
            })

            const like_count = status ? product.like + 1 : product.like - 1
            product.like = like_count

            const body: any = {
                like: like_count,
            }

            await this.exec(
                this.model.update(body, {
                    where: {
                        id: product.id,
                    },
                    transaction: transaction,
                })
            )

            transaction.commit()
            return {
                product: product,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async likeProduct(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // product id
        return await this.onChangeProductLikeStatus(user_id, id, true)
    }

    async unlikeProduct(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // product id
        return await this.onChangeProductLikeStatus(user_id, id, false)
    }

    async updateState(id : string ,state : string , user_id : string) {
        const product = await this.exec(this.model.findById(id), {
            allowNull: false,
        })
        if (user_id !== product.user_id) {
            throw errorService.auth.permissionDeny()
        }
        await this.exec(product.update({
            state: state,
            sell_at: state === PRODUCT_STATE.SOLD_OUT ? new Date() : null
        }))
        await this.exec(product.save())
        return product
    }
    async count(user_id?: string , thema_id?: string) {
       const [onSale, soldOut] = await Promise.all([
              this.exec(this.model.count({
                where: {
                     user_id,
                     state: PRODUCT_STATE.ON_SALE
                },
                  include: [
                      {
                          association: "category",
                          ...(thema_id ? {where: {thema_id}} : {}),
                      },
                  ],
              })),
              this.exec(this.model.count({
                where: {
                     user_id,
                     state: PRODUCT_STATE.SOLD_OUT
                },
                  include: [
                      {
                          association: "category",
                          ...(thema_id ? {where: {thema_id}} : {}),
                      },
                  ],
              }))
       ])
        return {
            onSale,
            soldOut,
            total: onSale + soldOut
        }
    }

    putItemOnTop = async (id: string) => {
        const product = await this.exec(this.model.findById(id), {
            allowNull: false,
        })
        await this.exec(product.update({
            rank: new Date().getTime()
        }))
        return product
    }
}
