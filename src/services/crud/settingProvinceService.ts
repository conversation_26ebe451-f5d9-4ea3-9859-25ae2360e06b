import {CrudService, ICrudOption} from '../crudService.pg'
import {SettingProvince} from '@/models/tables'
import {Sequelize} from "@/models/base.pg";

const fs = require('fs')
const xlsx = require('node-xlsx')

export class SettingProvinceService extends CrudService<typeof SettingProvince> {
    constructor() {
        super(SettingProvince)
    }

    async importExcel(params: any) {
        const data = await xlsx.parse(params.url)
        const dataExcel = data[0].data
        const [listKey, ...rest] = dataExcel
        const dataImport: Record<string, any> = []
        for (let i = 0; i < rest.length; i++) {
            const obj: Record<string, any> = {}
            for (const [index, key] of listKey.entries()) {
                obj[key] = rest[i][index]
            }
            dataImport.push(obj)
        }
        fs.unlinkSync(params.url)
        const t = await this.transaction()
        try {
            const created = await SettingProvince.bulkCreate(dataImport as any, {transaction: t})
            await t.commit()
            return created
        } catch (e) {
            await t.rollback()
        }
    }
    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        console.log('params',params)
        let currElIndexNumber;
        if (!prev_index_number) {
            currElIndexNumber = next_index_number - 512;
        } else if (!next_index_number) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        console.log('currElIndexNumber',currElIndexNumber)
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }
}