import {CrudService, ICrudOption} from '../crudService.pg';
import {Thema, Category, ThemaGroup} from '@/models/tables';
import {config} from '@/config';
import {sequelize} from '@/models';

import {tokenService, firebaseService, errorService} from '@/services';
import {Op} from "sequelize";

export class ThemaService extends CrudService<typeof Thema> {
    constructor() {
        super(Thema);
    }

    async delete(option?: ICrudOption) {
        const item = await this.exec(this.getItem(option), {allowNull: false});
        const categories = await this.exec(
            Category.destroy({
                where: {
                    thema_id: item.id,
                },
            })
        );
        // if (categories && categories.length) {
        //     throw errorService.database.queryFail(
        //         'You need to delete all categories belongs to this thema first.'
        //     );
        // }
        return await this.exec(item.destroy());
    }

    async create(params: any, option?: ICrudOption) {
        const {view_group_ids, post_group_ids, comment_group_ids, ...rest} = params
        const create = await this.exec(
            this.model.create(rest, this.applyCreateOptions(option))
        )
        if (view_group_ids) {
            for (const view_group_id of view_group_ids) {
                await this.exec(ThemaGroup.create({
                    view_group_id, thema_id: create.id
                } as any))
            }
        }
        if (post_group_ids) {
            for (const post_group_id of post_group_ids) {
                await this.exec(ThemaGroup.create({
                    post_group_id, thema_id: create.id
                } as any))
            }
        }
        if (comment_group_ids) {
            for (const comment_group_id of comment_group_ids) {
                await this.exec(ThemaGroup.create({
                    comment_group_id, thema_id: create.id
                } as any))
            }
        }
        return create
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        const {view_group_ids, post_group_ids, comment_group_ids, ...rest} = params
        await this.exec(item.update(rest))
        if (view_group_ids) {
            await this.exec(ThemaGroup.destroy({
                where: {
                    thema_id: option.filter.id,
                    view_group_id: {
                        [Op.not]: null
                    }
                }
            }))
            for (const view_group_id of view_group_ids) {
                await this.exec(ThemaGroup.create({
                    view_group_id, thema_id: option.filter.id
                } as any))
            }
        }
        if (post_group_ids) {
            await this.exec(ThemaGroup.destroy({
                where: {
                    thema_id: option.filter.id,
                    post_group_id: {
                        [Op.not]: null
                    }
                }
            }))
            for (const post_group_id of post_group_ids) {
                await this.exec(ThemaGroup.create({
                    post_group_id, thema_id: option.filter.id
                } as any))
            }
        }
        if (comment_group_ids) {
            await this.exec(ThemaGroup.destroy({
                where: {
                    thema_id: option.filter.id,
                    comment_group_id: {
                        [Op.not]: null
                    }
                }
            }))
            for (const comment_group_id of comment_group_ids) {
                await this.exec(ThemaGroup.create({
                    comment_group_id, thema_id: option.filter.id
                } as any))
            }
        }
        return await this.getItem(option)
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions({
                    ...option, include: [
                        ...option.include,
                        {
                            association: 'groups',
                            required: false,
                            attributes: ['id'],
                            include: [
                                {
                                    association: 'group_view',
                                    required: false
                                },
                                {
                                    association: 'group_post',
                                    required: false
                                },
                                {
                                    association: 'group_comment',
                                    required: false
                                },
                            ]
                        },
                    ]
                })
            )
        )
    }

    async getItem(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        return await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions({
                ...option, include: [
                    {
                        association: 'groups',
                        required: false,
                        attributes: ['id'],
                        include: [
                            {
                                association: 'group_view',
                                required: false
                            },
                            {
                                association: 'group_post',
                                required: false
                            },
                            {
                                association: 'group_comment',
                                required: false
                            },
                        ]
                    }
                ]
            })),
            {allowNull: false}
        )
    }

    async cloneThema(id: string){
        const transaction = await sequelize.transaction()
        const originThema = await this.exec(Thema.findOne({
            where : {id},
            include : [
                {
                    association : 'categories'
                },
                {
                    association : 'tags'
                }
            ]
        }), {allowNull : false})
        const ThemaInfo = originThema.dataValues

        delete ThemaInfo.id
        delete ThemaInfo.created_at
        delete ThemaInfo.updated_at
        delete ThemaInfo.created_at_unix_timestamp
        try {
            const thema =await this.exec(Thema.create(ThemaInfo,{
                transaction
            }))
            for (const categoryOrigin of ThemaInfo.categories) {
                const category = categoryOrigin.dataValues
                delete category.id
                delete category.created_at
                delete category.updated_at
                delete category.created_at_unix_timestamp
                await this.exec(Category.create({...category , thema_id : thema.id}, {transaction}))
            }
            for (const tagOrigin of ThemaInfo.tags) {
                const tag = tagOrigin.dataValues
                delete tag.id
                delete tag.created_at
                delete tag.updated_at
                delete tag.created_at_unix_timestamp
                await this.exec(Category.create({...tag , thema_id : thema.id}, {transaction}))
            }
           await transaction.commit()
           return thema
        }
        catch (e) {
           await transaction.rollback()
            throw e
        }
    }
}
