import {CrudService, ICrudOption} from '../crudService.pg'
import {
    AdminSetting, Conversation,
    Notification,
    Post,
    Recruit,
    Report,
    Review,
    User,
} from '@/models/tables'
import {config} from '@/config'
import {sequelize} from '@/models'

import {tokenService, firebaseService, errorService, rewardService} from '@/services'
import {FCM_ACTIONS, FCM_ACTIONS_MESSAGE} from '@/const'
import {Op} from "sequelize";

const REPORT_LIMIT_PER = 3
const MULTIPLE_SELECTION_ACTION = {
    RESTORE: 'RESTORE',
    DELETE: 'DELETE',
}

export class ReportService extends CrudService<typeof Report> {
    constructor() {
        super(Report)
    }

    async createReport(
        user_id: String,
        post_id: String,
        review_id: String,
        chat_id: String,
        transaction: any = undefined
    ) {
        const body: any = {
            user_id,
            post_id: null,
            review_id: null,
            chat_id: null,
        }

        let selectedModel = null

        if (post_id) {
            body.post_id = post_id
            selectedModel = Post
        } else if (review_id) {
            body.review_id = review_id
            selectedModel = Review
        } else if (chat_id) {
            body.chat_id = chat_id
            selectedModel = Conversation
        }

        const exist = await this.exec(
            Report.findOne({
                where: body,
                transaction,
            })
        )

        if (exist) {
            throw errorService.database.queryError({
                message: 'Report existed',
                code: 500,
            })
        }

        const reportCreate = await this.exec(Report.create(body, {transaction}))

        // add reward report spam
        await this.exec(rewardService.addReward(user_id as string, "REPORT_SPAM", transaction));

        const reportCount = await this.exec(
            Report.count({
                where: {
                    post_id: body.post_id,
                    review_id: body.review_id,
                    chat_id: body.chat_id,
                },
                transaction,
            })
        )

        const newDataBody: any = {
            report: reportCount,
        }

        await this.exec(
            selectedModel.update(newDataBody, {
                where: {
                    id: post_id || review_id || chat_id,
                },
                transaction,
            })
        )
        const adminSetting = await this.exec(AdminSetting.findOne({transaction}))
        if (reportCount >= adminSetting.report_limit) {
            await this.exec(
                Report.update({is_solved: true}, {
                    where: {
                        id: reportCreate.id
                    },
                    transaction
                })
            ),
            await this.exec(
                selectedModel.update(
                    {
                        status: false,
                    },
                    {
                    where: {
                        id: post_id || review_id || chat_id,
                    },
                    transaction,
                })
            )
            // await this.exec(
            //     selectedModel.destroy({
            //         where: {
            //             id: post_id || review_id || chat_id,
            //         },
            //         transaction,
            //     })
            // )

        }


        return reportCount
    }

    async removeReport(
        user_id: String,
        post_id: String,
        review_id: String,
        chat_id: String,
        transaction: any = undefined
    ) {
        const body: any = {
            user_id,
            post_id: null,
            review_id: null,
            chat_id: null,
        }

        let selectedModel = null

        if (post_id) {
            body.post_id = post_id
            selectedModel = Post
        } else if (review_id) {
            body.review_id = review_id
            selectedModel = Review
        } else if (chat_id) {
            body.chat_id = chat_id
            selectedModel = Conversation
        }

        await this.exec(
            Report.destroy({
                where: body,
                transaction,
            })
        )

        const reportCount = await this.exec(
            Report.count({
                where: {
                    post_id: body.post_id,
                    review_id: body.review_id,
                    chat_id: body.chat_id,
                },
                transaction,
            })
        )

        await this.exec(
            selectedModel.update(
                {
                    report: reportCount,
                },
                {
                    where: {
                        id: post_id || review_id || chat_id,
                    },
                    transaction,
                }
            )
        )

        return reportCount
    }

    async syncReportData() {
        // update post
        await this.exec(
            Post.update(
                {
                    report: 0,
                },
                {
                    where: {
                        $or: [
                            {
                                status: true,
                            },
                            {
                                status: false,
                            },
                        ],
                    },
                }
            )
        )
        // update review
        await this.exec(
            Review.update(
                {
                    report: 0,
                },
                {
                    where: {
                        $or: [
                            {
                                status: true,
                            },
                            {
                                status: false,
                            },
                        ],
                    },
                }
            )
        )
    }

    async getReportItemsForMultiplesSelection(arrayIds: any) {
        const items: any = []
        for (let i = 0; i < arrayIds.length; i++) {
            const reportRecordObj = await this.exec(
                this.model.findOne({
                    where: {
                        $or: [
                            {
                                post_id: arrayIds[i],
                            },
                            {
                                review_id: arrayIds[i],
                            },
                        ],
                    },
                    include: [
                        {
                            model: Post,
                            as: 'post',
                            paranoid: false,
                            // include: [
                            //   {
                            //     model: User,
                            //     as: 'user'
                            //   }
                            // ]
                        },
                        {
                            model: Review,
                            as: 'review',
                            paranoid: false,
                            // include: [
                            //   {
                            //     model: User,
                            //     as: 'user'
                            //   }
                            // ]
                        },
                    ],
                })
            )

            if (reportRecordObj) {
                items.push(reportRecordObj)
            }
        }
        return items
    }

    async sendResponseNotiForMultipleSelection(
        type: String,
        dataList: any,
        transaction: any = undefined
    ) {
        for (let i = 0; i < dataList.length; i++) {
            const item = dataList[i]
            let owner_user_id = null
            let message = null
            let action = null
            if (item.post_id) {
                message =
                    type === MULTIPLE_SELECTION_ACTION.RESTORE
                        ? FCM_ACTIONS_MESSAGE.YOUR_POSTING_WAS_RESTORED
                        : FCM_ACTIONS_MESSAGE.YOUR_POSTING_WAS_BANNED
                action =
                    type === MULTIPLE_SELECTION_ACTION.RESTORE
                        ? FCM_ACTIONS.YOUR_POSTING_WAS_RESTORED
                        : FCM_ACTIONS.YOUR_POSTING_WAS_BANNED
                owner_user_id = item.post.user_id
            } else if (item.review_id) {
                message =
                    type === MULTIPLE_SELECTION_ACTION.RESTORE
                        ? FCM_ACTIONS_MESSAGE.YOUR_COMMENT_WAS_RESTORED
                        : FCM_ACTIONS_MESSAGE.YOUR_COMMENT_WAS_BANNED
                action =
                    type === MULTIPLE_SELECTION_ACTION.RESTORE
                        ? FCM_ACTIONS.YOUR_COMMENT_WAS_RESTORED
                        : FCM_ACTIONS.YOUR_COMMENT_WAS_BANNED
                owner_user_id = item.review.user_id
            }

            if (owner_user_id && message && action) {
                // firebaseService.sendNotification(owner_user_id, message, action)
                const bodyNoti: any = {
                    user_id: owner_user_id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action: action,
                }
                await this.exec(
                    Notification.create(bodyNoti, {
                        transaction,
                    })
                )
            }
        }
    }

    async deleteMultiplesFunc(option?: ICrudOption) {
        const t = await this.transaction()
        option.transaction = t
        try {
            // initialize data:
            const itemIds = option.filter.id['$in']

            const reportedItems = await this.getReportItemsForMultiplesSelection(
                itemIds
            )

            const reportQueryOption: ICrudOption = {
                filter: {
                    $or: [
                        {
                            post_id: {$in: itemIds},
                        },
                        {
                            review_id: {$in: itemIds},
                        },
                        {
                            chat_id: {$in: itemIds},
                        },
                    ],
                },
                transaction: t,
            }

            // DELETE selected psotings and comments and related reports:
            // const resultPostings = await this.exec(
            //     Post.destroy(this.applyDestroyOptions(option))
            // )
            // const resultComments = await this.exec(
            //     Review.destroy(this.applyDestroyOptions(option))
            // )
            // const resultReports = await this.exec(
            //     this.model.destroy(this.applyDestroyOptions(reportQueryOption))
            // )
            await this.exec(
                Post.update(
                    {status: false , is_hide : true},
                    {
                        where: {
                            id: {
                                $in: itemIds,
                            },
                        },
                        transaction: t,
                    }
                )
            )
            await this.exec(
                Review.update({
                        status: false,
                    }, {
                        where: {
                            id: {
                                $in: itemIds,
                            },
                        },
                    }
                )
            )
            await this.exec(
                Conversation.update({
                        status: false,
                    }, {
                        where: {
                            id: {
                                $in: itemIds,
                            },
                        },
                    }
                )
            )
            await this.exec(
                Report.update({is_solved: true}, {
                    where: reportQueryOption.filter,
                    transaction: t
                })
            )

            // send notification to posting and comment:
            await this.sendResponseNotiForMultipleSelection(
                MULTIPLE_SELECTION_ACTION.DELETE,
                reportedItems,
                t
            )

            t.commit()
            return {
                status: true,
            }
        } catch (err) {
            t.rollback()
            throw err
        }
    }

    async restoreMultiplesFunc(option?: ICrudOption) {
        const t = await this.transaction()
        option.transaction = t
        try {
            // initialize data:
            const itemIds = option.filter.id['$in']

            const reportedItems = await this.getReportItemsForMultiplesSelection(
                itemIds
            )

            const reportQueryOption: ICrudOption = {
                filter: {
                    $or: [
                        {
                            post_id: {$in: itemIds},
                        },
                        {
                            review_id: {$in: itemIds},
                        },
                        {
                            chat_id: {$in: itemIds},
                        },
                    ],
                },
                transaction: t,
            }
            // await Post.restore({
            //     where: {
            //         id: {
            //             $in: itemIds,
            //         },
            //     },
            //     transaction: t,
            // })
            // await Review.restore({
            //     where: {
            //         id: {
            //             $in: itemIds,
            //         },
            //     },
            //     transaction: t,
            // })


            // RESTORE selected psotings and comments and DELETE related reports:
            const resultPostings = await this.exec(
                Post.update(
                    {report: 0, status: true , is_hide : false},
                    {
                        where: {
                            id: {
                                $in: itemIds,
                            },
                        },
                        transaction: t,
                    }
                )
            )
            const resultComments = await this.exec(
                Review.update(
                    {report: 0, status: true},
                    {
                        where: {
                            id: {
                                $in: itemIds,
                            },
                        },
                        transaction: t,
                    }
                )
            )
            const resultChats = await this.exec(
                Conversation.update(
                    {report: 0, status: true},
                    {
                        where: {
                            id: {
                                $in: itemIds,
                            },
                        },
                        transaction: t,
                    }
                )
            )
            const resultReports = await this.exec(
                this.model.destroy(this.applyDestroyOptions(reportQueryOption))
            )

            // send notification to posting and comment:
            await this.sendResponseNotiForMultipleSelection(
                MULTIPLE_SELECTION_ACTION.RESTORE,
                reportedItems,
                t
            )

            t.commit()
            return {
                status: true,
            }
        } catch (err) {
            t.rollback()
            throw err
        }
    }

    async countReport() {
        // const totalReportPost = await this.model.findAll({
        //     include: [{
        //         association: "post",
        //         required : true,
        //         paranoid: false
        //     }],
        //     paranoid: false
        // })
        // const totalReportReview = await this.model.findAll({
        //     include: [{
        //         association: "review",
        //         required : true,
        //         paranoid: false
        //     }],
        //     paranoid : false
        // })
        const [totalReportPostUnsolved, totalReportReviewUnsolved, totalReportPostSolved, totalReportReviewSolved
            , totalReportChatUnsolved, totalReportChatSolved
        ] = await Promise.all([
            this.model.count({
                where: {
                    is_solved: false
                },
                include: [{
                    association: "post",
                    required: true
                }]
            }),
            this.model.count({
                where: {
                    is_solved: false
                },
                include: [{
                    association: "review",
                    required: true
                }]
            }),
            this.model.count({
                where: {
                    is_solved: true
                },
                include: [{
                    association: "post",
                    required: true,
                }]
            }),
            this.model.count({
                where: {
                    is_solved: true
                },
                include: [{
                    association: "review",
                    required: true,
                }]
            }),
            this.model.count({
                where: {
                    is_solved: false
                },
                include: [{
                    association: "conversation",
                    required: true
                }]
            }),
            this.model.count({
                where: {
                    is_solved: true
                },
                include: [{
                    association: "conversation",
                    required: true,
                }]
            }),
        ])
        return {
            totalReportPost: totalReportPostUnsolved + totalReportPostSolved,
            totalReportReview: totalReportReviewUnsolved + totalReportReviewSolved,
            totalReportChat: totalReportChatUnsolved + totalReportChatSolved,
            totalReportPostUnsolved,
            totalReportReviewUnsolved,
            totalReportPostSolved,
            totalReportReviewSolved,
            totalReportChatUnsolved,
            totalReportChatSolved,
        }
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const reportData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        for (const row of reportData.rows) {
            let total_report = 0
            if (row.dataValues.post_id) {
                const post = await Report.count({
                    where: {
                        post_id: row.dataValues.post_id
                    }
                })
                total_report = post
            } else if (row.dataValues.review_id) {
                const review = await Report.count({
                    where: {
                        review_id: row.dataValues.review_id
                    }
                })
                total_report = review
            } else if (row.dataValues.chat_id) {
                const chat = await Report.count({
                    where: {
                        chat_id: row.dataValues.chat_id
                    }
                })
                total_report = chat
            }
            row.dataValues.total_report = total_report
        }
        return reportData
    }
}
