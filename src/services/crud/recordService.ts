import { CrudService, ICrudOption } from '../crudService.pg'
import { Record } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'
import * as moment from 'moment'

import { tokenService, firebaseService, errorService } from '@/services'
export class RecordService extends CrudService<typeof Record> {
  constructor() {
    super(Record)
  }

  async checkInApp() {
    const result = await this.increaseCurrentRecordData(
      {
        visitor_count: true,
      },
      undefined
    )
    return {
      status: !!result,
    }
  }
  async increaseCurrentRecordData(params: any, transaction: any) {
    try {
      const option: any = {
        order: [['created_at_unix_timestamp', 'DESC']],
        limit: 1,
      }
      if (transaction) {
        option.transaction = transaction
      }
      const records = await this.exec(Record.findAll(option))

      let record = records && records.length > 0 ? records[0] : undefined

      // check if latest record in database is today
      if (
        record &&
        moment(moment().valueOf()).isSame(
          moment(parseInt(record.created_at_unix_timestamp)),
          'day'
        )
      ) {
        // do nothing
      } else {
        // create new record for TODAY
        console.log('29148 KHONG CO SAME ')
        const body: any = {}
        record = await this.exec(
          Record.create(body, {
            transaction,
          })
        )
      }
      const body: any = {}
      if (params.page_view_count) {
        // xong
        body.page_view_count = record.page_view_count + 1
      }
      if (params.visitor_count) {
        // khoan lam
        body.visitor_count = record.visitor_count + 1
      }
      if (params.new_member_count) {
        // xong
        body.new_member_count = record.new_member_count + 1
      }
      if (params.partnership_inquiry_count) {
        // xong
        body.partnership_inquiry_count = record.partnership_inquiry_count + 1
      }
      if (params.new_post_count) {
        // xong
        body.new_post_count = record.new_post_count + 1
      }
      if (params.reply_count) {
        // xong
        body.reply_count = record.reply_count + 1
      }
      return await this.exec(
        Record.update(body, {
          where: {
            id: record.id,
          },
          transaction,
        })
      )
    } catch (error) {
      return null
    }
  }
}
