import { CrudService, ICrudOption } from '../crudService.pg';
import { Site, ViewSite } from '@/models/tables';
import { Sequelize } from "@/models/base.pg";
import { config } from "@/config";
import { Op } from "sequelize";
import * as moment from "moment";
import axios from "axios";

export class SiteService extends CrudService<typeof Site> {
    constructor() {
        super(Site);
    }

    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        params.health_status = await this.checkHealthUrl(params.link)
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }

    async getItem(
        option: ICrudOption = {
            scope: ['defaultScope'],
        },
        count = false,
        user_id?: string
    ) {
        if (count) {
            await ViewSite.create({site_id: option.filter.id, user_id} as any)
        }
        return this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        },
        isAdmin = false
    ) {
        const sites = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        if (isAdmin) {
            const startOfDay = moment().utc().add(9, 'hour').startOf('days').subtract(9, 'hour').toDate();
            const endOfDay = moment().utc().add(9, 'hour').endOf('days').subtract(9, 'hour').toDate();
            const startOfMonth = moment().utc().add(9, 'hour').startOf('month').subtract(9, 'hour').toDate();
            const endOfMonth = moment().utc().add(9, 'hour').endOf('month').subtract(9, 'hour').toDate();
            for (const site of sites.rows) {
                site.dataValues.view_count_in_day = await ViewSite.count({
                    where: {
                        site_id: site.id,
                        created_at: {
                            [Op.between]: [startOfDay, endOfDay]
                        }
                    }
                })
                site.dataValues.view_count_in_month = await ViewSite.count({
                    where: {
                        site_id: site.id,
                        created_at: {
                            [Op.between]: [startOfMonth, endOfMonth]
                        }
                    }
                })
            }
        }
        return sites
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        if (params.link) {
            params.health_status = await this.checkHealthUrl(params.link)
        }
        await this.exec(item.update(params))
        return await this.getItem(option)
    }

    async checkHealthUrl(url: string) {
        process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0'
        try {
            const response = await axios.get(url, {
                timeout: 5000,
                headers: {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/237.84.2.178 Safari/537.36",
                },
            })
            return (response.status === 200 || response.status === 201)
        } catch (error) {
            return false
        }
    }

    delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

    checkHealth = () => {
        this.model.findAll().then(async (sites) => {
            await Promise.all(
                sites.map(async (site) => {
                    await this.delay(1000)
                    site.health_status = await this.checkHealthUrl(site.link)
                    await site.save()
                })
            )
        })
    }
}
