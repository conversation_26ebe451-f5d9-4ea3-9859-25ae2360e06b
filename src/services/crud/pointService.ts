import { PointHistory, User, Sequelize, sequelize } from '@/models'

import {CrudService, ICrudOption} from '../crudService'
import { errorService } from '@/services'
import { POINT_ACTION } from '@/const'

interface ChangePointBody {
  user_id: string
  action: string
  di_object?: {
    id: string
    name: string
    image: string
  }
  point: number
}

export class PointService extends CrudService<typeof PointHistory> {
  constructor() {
    super(PointHistory)
  }

  async attendance(user_id: string) {
    const transaction = await sequelize.transaction()
    try {
      await this.checkExitToday(user_id, POINT_ACTION.ATTENDANCE, transaction)
      const point = this.randomPoint()
      await this.changePoint(
        { user_id, action: POINT_ACTION.ATTENDANCE, point },
        transaction
      )
      await transaction.commit()
      return { point }
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  }

  async lottery(user_id: string) {
    const transaction = await sequelize.transaction()
    try {
      await this.checkExitToday(user_id, POINT_ACTION.LOTTERY, transaction)
      const point = this.randomPoint()
      await this.changePoint(
        { user_id, action: POINT_ACTION.LOTTERY, point },
        transaction
      )
      await transaction.commit()
      return { point }
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  }

  async changePoint(
    body: ChangePointBody,
    transaction: Sequelize.Transaction
  ): Promise<void> {
    const user = await this.exec(
      User.findByPk(body.user_id, { transaction: transaction }),
      {
        allowNull: false,
      }
    )
    const current_user_point = user.point + body.point
    await user.update(
      {
        point: current_user_point,
      },
      { transaction }
    )
    await this.exec(
      this.model.create({ ...body, current_user_point } as any, { transaction })
    )
  }

  async checkExitToday(
    user_id: string,
    action: string,
    transaction: Sequelize.Transaction
  ) {
    const { Op } = Sequelize
    var start = new Date()
    start.setUTCHours(0, 0, 0, 0)

    var end = new Date()
    const exist = await this.exec(
      this.model.findOne({
        where: {
          user_id,
          action,
          created_at: { [Op.between]: [start, end] },
        },
        transaction,
      })
    )
    if (exist) throw errorService.router.badRequest()
  }

  randomPoint() {
    const MAX_POINT = 24
    return Math.ceil(Math.random() * MAX_POINT)
  }

    async count(filter?:ICrudOption['filter']) {
        const total =  await this.exec(this.model.count({
            where: filter
        }))
      return {total}
    }
}
