import { CrudService } from '../crudService.pg'
import { FeedbackItem, ThemaFeedbackItem } from '@/models/tables'
import { sequelize } from '@/models'

export class FeedbackItemService extends CrudService<typeof FeedbackItem> {
  constructor() {
    super(FeedbackItem)
  }

  async bulkCreate(params: any) {
    return await this.exec(this.model.bulkCreate(params))
  }

  async themaFeedbackItem(params: any) {
    const transaction = await sequelize.transaction()
    try {
      await ThemaFeedbackItem.destroy({
        where: {
          thema_id: params.thema_id,
        },
        transaction,
      })
      const bulkBody: any[] = []
      for (const feedback_item_id of params.feedback_item_ids) {
        bulkBody.push({
          thema_id: params.thema_id,
          feedback_item_id,
        })
      }
      const result = await ThemaFeedbackItem.bulkCreate(bulkBody, {
        transaction,
      })
      transaction.commit()
      return result
    } catch (err) {
      transaction.rollback()
      throw err
    }
  }
}
