import { CrudService, ICrudOption } from '../crudService.pg';
import { LinkCategory } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService } from '@/services';
import {Sequelize} from "@/models/base.pg";
export class LinkCategoryService extends CrudService<typeof LinkCategory> {
  constructor() {
    super(LinkCategory);
  }
  async dragDrop(id: string, params: any) {
    let {prev_index_number, next_index_number} = params
    let currElIndexNumber;
    if (prev_index_number === undefined) {
      currElIndexNumber = next_index_number - 512;
    } else if (next_index_number === undefined) {
      currElIndexNumber = prev_index_number + 512;
    } else {
      currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
    }
    try {
      const update = await this.model.update({index: currElIndexNumber}, {
        where: {
          id
        }
      })
      if (
          Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
          Math.abs(currElIndexNumber - next_index_number) <= 1
      ) {
        const links =await this.model.findAll({
          attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
        })
        await Promise.all(
            links.map(async (element) => {
              await this.model.update({index : element.dataValues.orderedData * 1024 }, {where : {
                  id : element.id
                }})
            })
        );
      }
      return update
    } catch (e) {
      console.log(e)
    }
  }
}
