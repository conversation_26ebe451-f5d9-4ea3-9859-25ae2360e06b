import { CrudService, ICrudOption } from '../crudService.pg';
import { Setting } from '@/models/tables';
import axios from 'axios'
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService, smsService } from '@/services';

export class SettingService extends CrudService<typeof Setting> {
    constructor() {
        super(Setting);
    }

    async getLocationSetting() {
        const locationObj = await this.exec(
            Setting.findOne({
                where: {
                    field: "LOCATION"
                }
            })
        )
        return locationObj;
    }

    async blogsPerPage(valueObj: {desktop?: number, mobile?: number}) {
        const record = await this.exec(
            Setting.findOne({
                where: {
                    field: "BLOG_LIMIT_PER_PAGE"
                }
            })
        )
        if (record) {
            const currentValue = record.value_obj || {};

            await this.exec(record.update({
                value_obj: {
                    ...currentValue,
                    ...valueObj
                }
            }));
            return record;
        } else {
           const createdSetting = await this.create({
                field: "BLOG_LIMIT_PER_PAGE",
                value_obj: valueObj
           });
           return createdSetting;
        }
    }

    async geoCode (address: string) {
        const url = `https://search.map.kakao.com/mapsearch/map.daum?q=${address}&msFlag=A&sort=0`
        const {data} = await axios.get(encodeURI(url) , {
            headers: {
                'accept': '*/*',
                'accept-language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6',
                'cookie': 'kd_lang=en; __T_=1; __T_SECURE=1',
                'referer': 'https://map.kakao.com/',
                'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'script',
                'sec-fetch-mode': 'no-cors',
                'sec-fetch-site': 'same-site',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        })
        if (!data) return null
        if (data.address && data.address.length){
            return {
                latitude: data.address[0].lat,
                longitude: data.address[0].lon
            }
        }
        else if (data.place && data.place.length){
            return {
                latitude: data.place[0].lat,
                longitude: data.place[0].lon
            }
        }
        else {
            return null
        }
    }

    async sendSms(content: string) {
        await smsService.notifyToBoss(content)
        return {
            message: 'success'
        }
    }
}
