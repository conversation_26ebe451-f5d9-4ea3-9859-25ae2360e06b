import { CrudService, ICrudOption } from '../crudService.pg'
import { FavouriteShortVideo } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'
import { tokenService, firebaseService, errorService } from '@/services'
export class FavouriteShortVideoService extends CrudService<
  typeof FavouriteShortVideo
> {
  constructor() {
    super(FavouriteShortVideo)
  }
}
