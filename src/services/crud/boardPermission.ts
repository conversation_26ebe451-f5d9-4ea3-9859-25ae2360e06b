import { CrudService, ICrudOption } from '../crudService.pg'
import { BoardPermission } from '@/models/tables'
import { config } from '@/config'
import {
    sequelize
} from '@/models'

import {
    tokenService,
    firebaseService,
    errorService
} from '@/services'
export class BoardPermissionService extends CrudService<typeof BoardPermission> {
    constructor() {
        super(BoardPermission)
    }
}