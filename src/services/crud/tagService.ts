import { CrudService, ICrudOption } from '../crudService.pg';
import {Tag, Shop, ShopTag, User, Thema} from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService } from '@/services';
import {Sequelize} from "@/models/base.pg";
import * as moment from "moment";
import {Op} from "sequelize";
export class TagService extends CrudService<typeof Tag> {
  constructor() {
    super(Tag);
  }
  async create(params: any, option?: ICrudOption) {
    const lastItem = await this.exec(this.getItem({
      order: [[
        'index', 'DESC'
      ]]
    }))
    params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
    return await this.exec(
        this.model.create(params, this.applyCreateOptions(option))
    )
  }
  async dragDrop(id: string, params: any) {
    let {prev_index_number, next_index_number} = params
    let currElIndexNumber;
    if (prev_index_number === undefined) {
      currElIndexNumber = next_index_number - 512;
    } else if (next_index_number === undefined) {
      currElIndexNumber = prev_index_number + 512;
    } else {
      currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
    }
    try {
      const update = await this.model.update({index: currElIndexNumber}, {
        where: {
          id
        }
      })
      if (
          Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
          Math.abs(currElIndexNumber - next_index_number) <= 1
      ) {
        const links =await this.model.findAll({
          attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
        })
        await Promise.all(
            links.map(async (element) => {
              await this.model.update({index : element.dataValues.orderedData * 1024 }, {where : {
                  id : element.id
                }})
            })
        );
      }
      return update
    } catch (e) {
      console.log(e)
    }
  }
  // async delete(option?: ICrudOption) {
  //   const item = await this.exec(this.getItem(option), { allowNull: false });
  //   const shoptagList = await this.exec(
  //     ShopTag.findAll({
  //       where: {
  //         tag_id: item.id,
  //       },
  //     })
  //   );
  //   if (shoptagList && shoptagList.length) {
  //     throw errorService.database.queryFail(
  //       'You need to delete all shops belongs to this tag first.'
  //     );
  //   }
  //   return await this.exec(item.destroy());
  // }

  async getList(
      option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
      }
      ,employee_id ? : string ,
      user_id?:string
  ) {
    // const themeOption: Record<string, any> = {
    //   ...(option.filter && option.filter.thema_id ? {id: option.filter.thema_id} : {})
    // }
    // if (!employee_id) {
    //   if (!user_id) {
    //     themeOption['is_for_adults'] = false
    //   } else {
    //     const _19YearAgo = moment().subtract(19, 'years').toDate()
    //     const user = await User.findOne({
    //       where: {
    //         id: user_id,
    //         birthday: {
    //           [Op.lte]: _19YearAgo
    //         }
    //       },
    //     })
    //     if (!user) {
    //       themeOption['is_for_adults'] = false
    //     }
    //   }
    // }
    // const thema = await Thema.findOne({
    //   where: themeOption
    // })
    // if (!thema) {
    //   throw errorService.router.requestDataInvalid('Underage users')
    // }
    return await this.exec(
        this.modelWithScope(option.scope).findAndCountAll(
            this.applyFindOptions(option)
        )
    )
  }
}
