import { CrudService, ICrudOption } from '../crudService'
import { Conversation, Message, Notification, Shop } from '@/models'
import { socketService } from '@/services/socketService'
import { Op } from 'sequelize'

import { FCM_ACTIONS, FCM_ACTIONS_MESSAGE, getFcmActionMessage } from '@/const'
import { SOCKET_EVENTS } from '@/services/socketService/constant/event'

import { errorService, firebaseService } from '@/services'
import * as moment from 'moment'

export class MessageService extends CrudService<typeof Message> {
    constructor() {
        super(Message)
    }

    async create(params: any, option?: ICrudOption) {
        const transaction = await this.transaction()
        const {user_id, conversation_id, recruit_id} = params
        try {
            const conversation = await this.exec(
                Conversation.findOne({
                    where: {id: conversation_id},
                    include: [
                        {association: 'last_message'},
                        {
                            association: 'shop',
                            attributes: ['user_id', 'title', 'thumbnails'],
                            include: [
                                {
                                    association: 'user',
                                    attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                                },
                            ]
                        },
                        {
                            association: 'shop2',
                            attributes: ['user_id', 'title', 'thumbnails'],
                            include: [
                                {
                                    association: 'user',
                                    attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                                },
                            ]
                        },
                        {
                            association: 'user',
                            attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                        },
                        {
                            association: 'user2',
                            attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                        },
                    ],
                }),
                {allowNull: false}
            )
            params.shop_id = conversation.shop_id ? conversation.shop_id : conversation.shop_id_2
            if ((conversation.shop && conversation.shop.user_id === params.user_id) || (conversation.shop2 && conversation.shop2.user_id === params.user_id)) {
                delete params.user_id
            } else {
                delete params.shop_id
            }
            const message = await this.exec(
                this.model.create(params, {transaction})
            )

            const updateConversation: any = {
                last_message_id: message.id,
                hide_user_id: null,
                hide_shop_id: null,
                hide_user_id_2: null,
                hide_shop_id_2: null
            }
            if (!conversation.status) {
                updateConversation.status = true
            }

            await conversation.update(updateConversation, {
                transaction,
            })

            await transaction.commit()
            conversation.dataValues.updated_at = new Date().toISOString()
            conversation.dataValues.last_message = message
            conversation.dataValues.last_message_id = message.id

            let receiveUser
            if (user_id === conversation.user_id) {
                receiveUser = conversation.user2 ? conversation.user2 : conversation.shop ? conversation.shop.user : conversation.shop2.user
            } else if (user_id === conversation.user_id_2) {
                receiveUser = conversation.user ? conversation.user : conversation.shop ? conversation.shop.user : conversation.shop2.user
            } else if (conversation.shop && user_id === conversation.shop.user_id) {
                receiveUser = conversation.user2 ? conversation.user2 : conversation.shop2 ? conversation.shop2.user : conversation.user
            } else {
                receiveUser = conversation.user ? conversation.user : conversation.shop ? conversation.shop.user_id : conversation.user2
            }
            let whereCondition: any = {conversation_id, status: false}
            if (receiveUser.id === conversation.user_id || receiveUser.id === conversation.user_id_2) {
                whereCondition = {
                    ...whereCondition,
                    [Op.or]: [
                        {
                            user_id: null
                        },
                        {
                            user_id: {
                                [Op.ne]: receiveUser.id
                            }
                        }
                    ],
                }
            } else {
                let shop_id
                if (conversation.shop && receiveUser.id === conversation.shop.user_id) {
                    shop_id = conversation.shop_id
                }
                if (conversation.shop2 && receiveUser === conversation.shop2.user_id) {
                    shop_id = conversation.shop_id_2
                }
                whereCondition = {
                    ...whereCondition,
                    [Op.or]: [
                        {
                            shop_id: null
                        },
                        {
                            shop_id: {
                                [Op.ne]: shop_id
                            }
                        }
                    ],

                }
            }

            const receiveUserUnread = await this.exec(
                this.model.count({
                    where: whereCondition,
                })
            )

            // Sent to user
            conversation.dataValues.unread_message = 0
            socketService.emitToUser(user_id, SOCKET_EVENTS.NEW_MESSAGE, conversation)

            // Sent to receive user
            conversation.dataValues.unread_message = receiveUserUnread
            socketService.emitToUser(
                receiveUser.id,
                SOCKET_EVENTS.NEW_MESSAGE,
                conversation
            )

            // Check is my for user
            if (conversation.user_id) {
                message.dataValues.is_my = !!message.user_id && message.user_id === conversation.user_id
                socketService.emitToUser(conversation.user_id, conversation.id, message)
            }
            if (conversation.user_id_2) {
                message.dataValues.is_my = !!message.user_id && message.user_id === conversation.user_id_2
                socketService.emitToUser(conversation.user_id_2, conversation.id, message)
            }

            // Check is my for shop
            if (conversation.shop && conversation.shop.user_id) {
                message.dataValues.is_my = !!message.shop_id && message.shop_id === conversation.shop_id
                socketService.emitToUser(
                    conversation.shop.user_id,
                    conversation.id,
                    message
                )
            }
            if (conversation.shop2 && conversation.shop2.user_id) {
                message.dataValues.is_my = !!message.shop_id && message.shop_id === conversation.shop_id_2
                socketService.emitToUser(
                    conversation.shop2.user_id,
                    conversation.id,
                    message
                )
            }


            if ((conversation.shop && user_id === conversation.shop.user_id) || (conversation.shop2 && user_id === conversation.shop2.user_id)) {
                const shop = (conversation.shop && user_id === conversation.shop.user_id) ? conversation.shop : conversation.shop2
                const content = getFcmActionMessage(FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_USER, {
                    params_1: shop.title,
                })
                const bodyNoti: any = {
                    user_id: receiveUser.id,
                    title: content,
                    content: content,
                    data: {
                        message: content,
                        params: {
                            params_1: shop.title,
                        },
                    },
                    action: FCM_ACTIONS.NEW_MESSAGE_NOTIFICATION,
                    interacting_type: 'NEW_MESSAGE_NOTIFICATION',
                    interacting_content_id: conversation.id,
                    image: shop.thumbnails && shop.thumbnails.length ? shop.thumbnails[0] : null,
                }
                await Promise.all([
                    // firebaseService.sendNotification(
                    //     receiveUser.id,
                    //     message.content,
                    //     FCM_ACTIONS.NEW_MESSAGE,
                    //     conversation.id,
                    //     {
                    //         priority: 'high',
                    //         timeToLive: 2419200, // 28 days
                    //     },
                    //     receiveUser.notice_messenger_status
                    // ),
                    // firebaseService.sendNotification(
                    //     receiveUser.id,
                    //     content,
                    //     FCM_ACTIONS.NEW_MESSAGE_NOTIFICATION,
                    //     conversation.id,
                    //     {
                    //         priority: 'high',
                    //         timeToLive: 2419200, // 28 days
                    //     },
                    // ),
                    this.exec(Notification.create(bodyNoti)),
                ]);
            } else {
                const user = conversation.user_id === user_id ? conversation.user : conversation.user2
                const content = getFcmActionMessage(FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_SHOP, {
                    params_1: user.nickname,
                })
                const bodyNoti: any = {
                    user_id: receiveUser.id,
                    title: content,
                    content: content,
                    data: {
                        message: content,
                        params: {
                            params_1: user.nickname,
                        },
                    },
                    action: FCM_ACTIONS.NEW_MESSAGE_NOTIFICATION,
                    interacting_type: 'NEW_MESSAGE_NOTIFICATION',
                    interacting_content_id: conversation.id,
                    image: user.avatar
                }
                await Promise.all([
                    // firebaseService.sendNotification(
                    //     receiveUser.id,
                    //     message.content,
                    //     FCM_ACTIONS.NEW_MESSAGE,
                    //     conversation.id,
                    //     {
                    //         priority: 'high',
                    //         timeToLive: 2419200, // 28 days
                    //     },
                    //     receiveUser.notice_messenger_status
                    // ),
                    // firebaseService.sendNotification(
                    //     receiveUser.id,
                    //     content,
                    //     FCM_ACTIONS.NEW_MESSAGE_NOTIFICATION,
                    //     conversation.id,
                    //     {
                    //         priority: 'high',
                    //         timeToLive: 2419200, // 28 days
                    //     },
                    // ),
                    this.exec(Notification.create(bodyNoti)),
                ]);
            }


            return message
        } catch (e) {
            console.log('e', e)
            await transaction.rollback()
            throw e
        }
    }

    async getListByConversationId(params: any, option: ICrudOption) {
        const user_id = params.user_id
        const employee_id = params.employee_id
        const conversation_id = option.filter.id
        const options = this.applyFindOptions({
            ...option,
            filter: {
                ...option.filter,
                conversation_id
            },
            order: [
                ['created_at', 'DESC']
            ],
            include: [
                ...option.include,
                {
                    association: 'recruit',
                },
            ]
        })
        if (employee_id) {
            delete option.filter.id
            return this.model.findAndCountAll(options)
        }
        const conversation = await this.exec(
            Conversation.findOne({
                where: {id: option.filter.id},
                include: [{association: 'shop', attributes: ['user_id']}],
            }),
            {allowNull: false}
        )

        let optionOr
        if (conversation.shop && user_id === conversation.shop.user_id) {
            optionOr = [
                {
                    hide_shop_id: {
                        [Op.is]: null
                    },
                },
                {
                    hide_shop_id: {
                        [Op.ne]: user_id
                    },
                },
            ]
        }
        if (conversation.shop2 && user_id === conversation.shop2.user_id) {
            optionOr = [
                {
                    hide_shop_id_2: {
                        [Op.is]: null
                    },
                },
                {
                    hide_shop_id_2: {
                        [Op.ne]: user_id
                    },
                },
            ]
        }
        if (conversation.user_id === user_id) {
            optionOr = [
                {
                    hide_user_id: {
                        [Op.is]: null
                    },
                },
                {
                    hide_user_id: {
                        [Op.ne]: user_id
                    },
                },
            ]
        }
        if (conversation.user_id_2 === user_id) {
            optionOr = [
                {
                    hide_user_id_2: {
                        [Op.is]: null
                    },
                },
                {
                    hide_user_id_2: {
                        [Op.ne]: user_id
                    },
                },
            ]
        }


        const res = await this.exec(
            this.model.findAndCountAll({
                ...options,
                where: {
                    [Op.and]: [
                        {
                            conversation_id
                        },
                        {
                            [Op.or]: optionOr,
                        },
                    ],
                },
            })
        )

        const {rows} = res
        for (let i = 0; i < rows.length; i++) {
            rows[i].dataValues.is_my = rows[i].dataValues.user_id
                ? user_id === rows[i].dataValues.user_id
                : user_id === conversation.shop.user_id
        }

        return res
    }

    async readMessage(params: { conversation_id: string; user_id: string }) {
        const {conversation_id, user_id} = params
        const conversation: any = await this.exec(
            Conversation.findOne({
                where: {id: conversation_id},
                include: [{association: 'shop', attributes: ['user_id']}, {
                    association: 'shop2',
                    attributes: ['user_id']
                }],
            }),
            {allowNull: false}
        )
        const shops = await this.exec(Shop.findAll({where: {user_id}}))
        const shop_ids = shops.map((shop: any) => shop.id)

        if (
            user_id !== conversation.user_id &&
            user_id !== conversation.user_id_2 &&
            (conversation.shop && user_id !== conversation.shop.user_id) &&
            (conversation.shop2 && user_id !== conversation.shop2.user_id)
        ) {
            throw errorService.auth.permissionDeny()
        }

        let whereCondition: any = {
            conversation_id,
        }

        if ((conversation.shop && user_id === conversation.shop.user_id) || (conversation.shop2 && user_id === conversation.shop2.user_id)) {
            whereCondition = {
                ...whereCondition,
                [Op.or]: [
                    {
                        shop_id: null
                    },
                    ...(shop_ids.length ? [
                        {
                            shop_id: {
                                [Op.notIn]: shop_ids
                            }
                        }
                    ] : [])
                ],
            }
        } else {
            whereCondition = {
                ...whereCondition,
                [Op.or]: [
                    {
                        user_id: null
                    },
                    {
                        user_id: {
                            [Op.ne]: user_id
                        }
                    }
                ],
            }
        }

        await this.exec(Message.update({status: true}, {where: whereCondition}))
        conversation.user_id && socketService.emitToUser(conversation.user_id, SOCKET_EVENTS.READ_MESSAGE, {
            user_id,
            conversation_id,
        })
        conversation.shop && socketService.emitToUser(
            conversation.shop.user_id,
            SOCKET_EVENTS.READ_MESSAGE,
            {
                user_id,
                conversation_id,
            }
        )
        conversation.user_id_2 && socketService.emitToUser(conversation.user_id_2, SOCKET_EVENTS.READ_MESSAGE, {
            user_id,
            conversation_id,
        })
        conversation.shop2 && socketService.emitToUser(
            conversation.shop2.user_id,
            SOCKET_EVENTS.READ_MESSAGE,
            {
                user_id,
                conversation_id,
            }
        )
    }

    async unreadMessage(params: { user_id: string, type: any }) {
        const {user_id, type} = params
        const shops = await this.exec(Shop.findAll({where: {user_id}}))
        const shop_ids = shops.map((shop: any) => shop.id)
        const count = await this.exec(
            this.model.count({
                include: [
                    {
                        association: 'conversation',
                        where: {
                            ...(type ? {
                                [Op.or]: [
                                    ...(type === 'USER_USER' ? [
                                        {
                                            user_id,
                                            user_id_2: {[Op.ne]: null},
                                        },
                                        {
                                            user_id_2: user_id,
                                            user_id: {[Op.ne]: null},
                                        }
                                    ] : [
                                        {
                                            user_id,
                                            [Op.or]: [
                                                {shop_id: {[Op.ne]: null}},
                                                {shop_id_2: {[Op.ne]: null}},
                                            ]
                                        },
                                        {
                                            user_id_2: user_id,
                                            [Op.or]: [
                                                {shop_id: {[Op.ne]: null}},
                                                {shop_id_2: {[Op.ne]: null}},
                                            ]
                                        },
                                        ...(shop_ids.length ? [
                                            {
                                                shop_id: {
                                                    [Op.in]: shop_ids,
                                                },
                                                [Op.or]: [
                                                    {user_id: {[Op.ne]: null}},
                                                    {user_id_2: {[Op.ne]: null}},
                                                ]
                                            },
                                            {
                                                shop_id_2: {
                                                    [Op.in]: shop_ids,
                                                },
                                                [Op.or]: [
                                                    {user_id: {[Op.ne]: null}},
                                                    {user_id_2: {[Op.ne]: null}},
                                                ]
                                            },
                                        ] : [])

                                    ]),
                                    // {
                                    //     user_id,
                                    //     ...(type ? type === 'USER' ? {
                                    //         user_id_2: {[Op.ne]: null},
                                    //     } : {
                                    //         [Op.or]: [
                                    //             {shop_id: {[Op.ne]: null}},
                                    //             {shop_id_2: {[Op.ne]: null}},
                                    //         ]
                                    //     } : {})
                                    // },
                                    // {
                                    //     user_id_2: user_id,
                                    //     ...(type ? type === 'USER' ? {
                                    //         user_id: {[Op.ne]: null},
                                    //     } : {
                                    //         [Op.or]: [
                                    //             {shop_id: {[Op.ne]: null}},
                                    //             {shop_id_2: {[Op.ne]: null}},
                                    //         ]
                                    //     } : {})
                                    // },
                                    // ...(shop_ids.length ? [
                                    //     {
                                    //         shop_id: {[Op.in]: shop_ids},
                                    //         ...(type ? type === 'USER' ? {
                                    //             [Op.or]: [
                                    //                 {user_id: {[Op.ne]: null}},
                                    //                 {user_id_2: {[Op.ne]: null}},
                                    //             ]
                                    //         } : {
                                    //             shop_id_2: {[Op.ne]: null}
                                    //         } : {})
                                    //     },
                                    //     {
                                    //         shop_id_2: {[Op.in]: shop_ids},
                                    //         ...(type ? type === 'USER' ? {
                                    //             [Op.or]: [
                                    //                 {user_id: {[Op.ne]: null}},
                                    //                 {user_id_2: {[Op.ne]: null}},
                                    //             ]
                                    //         } : {
                                    //             shop_id: {[Op.ne]: null}
                                    //         } : {})
                                    //     }
                                    // ] : [])
                                ],
                            } : {})
                        },
                    },
                ],
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                {
                                    user_id: null,
                                    ...(shop_ids.length ? {shop_id: {[Op.notIn]: shop_ids}} : {})
                                },
                                {
                                    user_id: {[Op.ne]: user_id},
                                    shop_id: null
                                },
                            ],
                        },
                        {
                            status: false,
                            hide_user_id: {[Op.ne]: user_id},
                            hide_user_id_2: {[Op.ne]: user_id},
                            ...(shop_ids.length ? {hide_shop_id: {[Op.notIn]: shop_ids}} : {}),
                            ...(shop_ids.length ? {hide_shop_id_2: {[Op.notIn]: shop_ids}} : {})
                        },
                    ],
                },
            })
        )
        return {count}
    }

    async scheduleReminder() {
        const messages: any[] = await this.model.findAll({
            where: {
                status: false,
                _1_day_unread: false,
                created_at: {
                    [Op.lte]: moment().subtract(10, 'minutes').toDate()
                }
            },
            include: [
                {
                    association: 'conversation',
                    include: [
                        {association: 'shop'},
                        {association: 'user'}
                    ]
                },
                {association: 'shop'},
                {association: 'user'}],
        })

        const reminders = [
            {
                limit: 600,
                field: '_10_minutes_unread',
                action: FCM_ACTIONS.NEW_MESSAGE_10_MINUTES,
                interactingType: 'NEW_MESSAGE_10_MINUTES',
                userAction: FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_USER_10_MINUTES,
                shopAction: FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_SHOP_10_MINUTES
            },
            {
                limit: 3600,
                field: '_1_hour_unread',
                action: FCM_ACTIONS.NEW_MESSAGE_1_HOUR,
                interactingType: 'NEW_MESSAGE_1_HOUR',
                userAction: FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_USER_1_HOUR,
                shopAction: FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_SHOP_1_HOUR
            },
            {
                limit: 86400,
                field: '_1_day_unread',
                action: FCM_ACTIONS.NEW_MESSAGE_1_DAY,
                interactingType: 'NEW_MESSAGE_1_DAY',
                userAction: FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_USER_1_DAY,
                shopAction: FCM_ACTIONS_MESSAGE.NEW_MESSAGE_FOR_SHOP_1_DAY
            }
        ]

        for (const message of messages) {
            const seconds = moment().diff(message.created_at, 'seconds')
            for (const [index, reminder] of reminders.entries()) {
                let condition
                if (index === reminders.length - 1) {
                    condition = seconds > reminder.limit
                } else {
                    condition = seconds > reminder.limit && seconds < reminders[index + 1].limit
                }
                try {
                    if (condition && !message[reminder.field]) {
                        let content, receiverId;
                        if (message.user_id) {
                            content = getFcmActionMessage(reminder.shopAction, {
                                params_1: message.conversation.shop.title,
                            });
                            receiverId = message.conversation.shop.user_id;
                        } else {
                            content = getFcmActionMessage(reminder.userAction, {
                                params_1: message.conversation.user.nickname,
                            });
                            receiverId = message.conversation.user_id;
                        }
                        const bodyNoti: any = {
                            user_id: receiverId,
                            title: content,
                            content: content,
                            data: {
                                message: content,
                                params: {
                                    params_1: message.conversation.shop.title,
                                },
                            },
                            action: Number(reminder.action),
                            interacting_type: reminder.interactingType,
                            interacting_content_id: message.conversation_id,
                        }
                        await Promise.all([
                            // firebaseService.sendNotification(
                            //     receiverId,
                            //     content,
                            //     reminder.action,
                            //     message.conversation_id,
                            //     {
                            //         priority: 'high',
                            //         timeToLive: 2419200, // 28 days
                            //     },
                            // ),
                            Notification.create(bodyNoti),
                        ]);
                        await this.model.update(
                            {[reminder.field]: true},
                            {where: {id: message.id}}
                        )
                    }
                } catch (e) {
                    console.log('error', e)
                }
            }
        }
    }
}
