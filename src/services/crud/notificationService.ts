import {CrudService, ICrudOption} from '../crudService.pg';
import {Notification} from '@/models/tables';
import {config} from '@/config';
import {sequelize} from '@/models';

import {tokenService, firebaseService, errorService} from '@/services';
import {FCM_ACTIONS, getFcmActionMessage} from '@/const';

export class NotificationService extends CrudService<typeof Notification> {
    constructor() {
        super(Notification);
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        );
    }

    // async deleteAll() {
    //   const transaction = await sequelize.transaction();
    //   try {
    //     await this.exec(
    //       Notification.destroy({
    //         where: {
    //           status: true,
    //         },
    //       })
    //     );
    //     transaction.commit();
    //   } catch (err) {
    //     transaction.rollback();
    //   }
    // }
    async deleteAllUserNoti(params: any) {
        const transaction = await sequelize.transaction();
        try {
            const notis = await this.exec(
                Notification.findAll({
                    where: {
                        user_id: params.user_id,
                    },
                    attributes: ["id"],
                    transaction,
                })
            );
            if (notis && notis.length > 0) {
                const noti_ids = notis.map((e: any) => e.id);
                await this.exec(
                    Notification.destroy({
                        where: {
                            id: {$in: noti_ids},
                        },
                        transaction,
                    })
                );
            }
            transaction.commit();
            //   return await this.exec(item.destroy());
            return notis.length;
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async sendFCMandCreateNoti(
        receiver_id: string,
        fcmMessage: string,
        fcmAction: string,
        entity_id: string,
        params: any = undefined
    ) {
        let interacting_type = 'SEND_NOTIFICATION';
        switch (fcmAction) {
            case FCM_ACTIONS.SEND_NOTIFICATION:
                interacting_type = 'SEND_NOTIFICATION';
                break;
            case FCM_ACTIONS.INCREASE_POST_LIMIT:
                interacting_type = 'INCREASE_POST_LIMIT';
                break;
            case FCM_ACTIONS.DECREASE_POST_LIMIT:
                interacting_type = 'DECREASE_POST_LIMIT';
                break;
            case FCM_ACTIONS.EXPOSE_CATEGORY_TAG:
                interacting_type = 'EXPOSE_CATEGORY_TAG';
                break;
            case FCM_ACTIONS.HIDE_CATEGORY_TAG:
                interacting_type = 'HIDE_CATEGORY_TAG';
                break;
            case FCM_ACTIONS.COMMENT_POST: // Someone has added a comment to your Post
                interacting_type = 'COMMENT_POST';
                break;
            case FCM_ACTIONS.REPLY_COMMENT_POST: // Someone has replied to your comment
                interacting_type = 'REPLY_COMMENT_POST';
                break;
            case FCM_ACTIONS.COMMENT_SHOP: //
                interacting_type = 'COMMENT_SHOP';
                break;
            case FCM_ACTIONS.REPLY_COMMENT_SHOP:
                interacting_type = 'REPLY_COMMENT_SHOP';
                break;
            case FCM_ACTIONS.APPROVE_SHOP:
                interacting_type = 'APPROVE_SHOP';
                break;
            case FCM_ACTIONS.REJECT_SHOP:
                interacting_type = 'REJECT_SHOP';
                break;
            case FCM_ACTIONS.PRE_EXPIRE_SHOP:
                interacting_type = 'PRE_EXPIRE_SHOP';
                break;
            case FCM_ACTIONS.PRE_EXPIRE_EVENT:
                interacting_type = 'PRE_EXPIRE_EVENT';
                break;
            case FCM_ACTIONS.EXPIRE_SHOP:
                interacting_type = 'EXPIRE_SHOP';
                break;
            case FCM_ACTIONS.EXPIRE_EVENT:
                interacting_type = 'EXPIRE_EVENT';
                break;
            case FCM_ACTIONS.EXTEND_SHOP_EXPIRE_DATE:
                interacting_type = 'EXTEND_SHOP_EXPIRE_DATE';
                break;
            case FCM_ACTIONS.DECREASE_SHOP_EXPIRE_DATE:
                interacting_type = 'DECREASE_SHOP_EXPIRE_DATE';
                break;
            case FCM_ACTIONS.TRANSFER_SHOP_OWNERSHIP:
                interacting_type = 'TRANSFER_SHOP_OWNERSHIP';
                break;
            case FCM_ACTIONS.REMOVE_SHOP_OWNERSHIP:
                interacting_type = 'REMOVE_SHOP_OWNERSHIP';
                break;
            case FCM_ACTIONS.DISABLED_ACCOUNT:
                interacting_type = 'DISABLED_ACCOUNT';
                break;
            case FCM_ACTIONS.YOUR_EVENT_HAVE_BEEN_DELETED:
                interacting_type = 'YOUR_EVENT_HAVE_BEEN_DELETED';
                break;
            case FCM_ACTIONS.YOUR_POST_HAVE_BEEN_DELETED:
                interacting_type = 'YOUR_POST_HAVE_BEEN_DELETED';
                break;
            case FCM_ACTIONS.YOUR_POST_HAVE_BEEN_EDITED:
                interacting_type = 'YOUR_POST_HAVE_BEEN_EDITED';
                break;
            case FCM_ACTIONS.YOUR_EVENT_HAVE_BEEN_EDITED:
                interacting_type = 'YOUR_EVENT_HAVE_BEEN_EDITED';
                break;
            case FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_UPGRADED:
                interacting_type = 'YOUR_ACCOUNT_HAVE_BEEN_UPGRADED';
                break;
            case FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_EDITED:
                interacting_type = 'YOUR_ACCOUNT_HAVE_BEEN_EDITED';
                break;
            case FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED:
                interacting_type = 'YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED';
                break;
            case FCM_ACTIONS.YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED:
                interacting_type = 'YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED';
                break;
            case FCM_ACTIONS.YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED:
                interacting_type = 'YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED';
                break;
            default:
                interacting_type = 'SEND_NOTIFICATION';
                break;
        }
        // sendFCMandCreateNoti
        const message = getFcmActionMessage(fcmMessage, params);

        // await firebaseService.sendNotification(
        //     receiver_id,
        //     message,
        //     fcmAction,
        //     entity_id
        // );
        const bodyNoti: any = {
            user_id: receiver_id,
            title: message,
            content: message,
            data: {
                message: message,
                params,
            },
            action: fcmAction,
            interacting_type,
            interacting_content_id: entity_id,
        };
        await this.exec(Notification.create(bodyNoti));
    }

    async getItem(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const notification = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
        await notification.update({is_view: true})
        return notification
    }

    async updateMultiView(option: ICrudOption, is_view: boolean) {
        const {filter} = option
        return await this.exec(Notification.update({is_view}, {where: filter}))
    }
}
