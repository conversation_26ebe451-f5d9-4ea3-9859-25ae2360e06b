import {CrudService, ICrudOption} from '../crudService.pg'
import {Faq, FaqCategory} from '@/models/tables'
import {config} from '@/config'

export class FaqCategoryService extends CrudService<typeof FaqCategory> {
    constructor() {
        super(FaqCategory)
    }

    async updateFagCategory(params: any) {
        const {list_data} = params
        for (const data of list_data) {
            await FaqCategory.update({name: data.name}, {where: {id: data.id}})
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const {new_group} = params
        const item = await this.exec(this.getItem(option), {allowNull: false})
        if (new_group) {
            await Faq.update({
                faq_category_id: new_group
            }, {
                where: {
                    faq_category_id: item.id
                }
            })
        }
        return await this.exec(item.destroy())
    }


    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const data =  await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        for (const item of data.rows) {
            
            item.dataValues.total_faq = await Faq.count({where: {faq_category_id: item.id}})
        }
        return data
    }
}