import { config } from '@/config'
import {
    FCM_ACTIONS,
    FCM_ACTIONS_MESSAGE,
    HISTORY,
    LOGIN_TYPE,
    POINT_ACTION,
    ROLE,
    SHOP_STATE,
    TIMEZONE,
    USER_TYPE,
} from '@/const'
import { Conversation, PointHistory, Reservation, sequelize } from '@/models'
import {
    ActionLog,
    Banner,
    Comment,
    Content,
    Employee,
    Event,
    History,
    Link,
    Notification,
    Post,
    Recruit,
    ReferralCode,
    Review,
    Setting,
    Shop,
    User,
} from '@/models/tables'
import {
    actionLogService,
    errorService,
    firebaseService,
    historyService,
    notificationService,
    pointService,
    recordService,
    rewardService,
    shopService, smsService,
} from '@/services'
import axios from 'axios'
import { uniq } from 'lodash'
import * as moment from 'moment'
import { CrudService, ICrudOption } from '../crudService.pg'
import appleSignin from 'apple-signin-auth'
import { makeRandomCode } from '@/config/utils'
import { Op } from "sequelize";

const USER_ERR_CODE = 300
const REACT_DAILY_LIMIT_ERR_CODE = USER_ERR_CODE + 11
const REACH_TODAY_GIFT_ROLL_LIMIT = USER_ERR_CODE + 12
const NICKNAME_ALREADY_EXISTED_CODE = USER_ERR_CODE + 13

export class UserService extends CrudService<typeof User> {
    constructor() {
        super(User)
    }

    async findUser(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const item = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option))
        )
        if (item) {
            return item
        }
        return 'user not found'
    }

    async getRollGiftBoardtype(type: any) {
        let boardName = 'EVENT_PROFILE_TYPE_A'
        if (type === 'A') {
            boardName = 'EVENT_PROFILE_TYPE_A'
        } else if (type === 'B') {
            boardName = 'EVENT_PROFILE_TYPE_B'
        } else if (type === 'C') {
            boardName = 'EVENT_PROFILE_TYPE_C'
        } else if (type === 'repeat-application') {
            boardName = 'repeat-application'
        } else if (type === 'view-winners') {
            boardName = 'view-winners'
        }
        return await this.exec(
            Content.findOne({
                where: {
                    type: boardName,
                },
            })
        )
    }

    async doRollGift(params: any) {
        try {
            const timeNow = moment
                .utc()
                .utcOffset(TIMEZONE * 60)
                .valueOf()
            const currentFriday = moment(timeNow)
                .isoWeekday('Friday')
                .utc()
                .utcOffset(TIMEZONE * 60)
                .valueOf()
            const todayIsFriday =
                moment(timeNow).isoWeekday('Friday').format('YYYY-MM-DD') ===
                moment(timeNow).format('YYYY-MM-DD')

            let startTime = 0
            let endTime = 0
            if (timeNow < currentFriday) {
                startTime = moment(currentFriday)
                    .subtract(7, 'day')
                    .endOf('day')
                    .utc()
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()
                endTime = moment(currentFriday)
                    .endOf('day')
                    .utc()
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()
            } else {
                startTime = moment(currentFriday)
                    .endOf('day')
                    .utc()
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()
                endTime = moment(currentFriday)
                    .add(7, 'day')
                    .endOf('day')
                    .utc()
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()
            }

            const currentWeekRollRecords = await this.exec(
                History.findAll({
                    where: {
                        category: HISTORY.USER.NAME,
                        type_1: HISTORY.USER.TYPES.GIFT_ROLL.NAME,
                        user_id: params.user_id,
                        created_at_unix_timestamp: {$between: [startTime, endTime]},
                    },
                    attributes: ['note', 'created_at_unix_timestamp'],
                })
            )

            const todayRecord = currentWeekRollRecords.find(
                (e: any) =>
                    moment
                        .utc()
                        .startOf('day')
                        .utcOffset(TIMEZONE * 60)
                        .valueOf() <= parseInt(e.created_at_unix_timestamp) &&
                    parseInt(e.created_at_unix_timestamp) <=
                    moment
                        .utc()
                        .endOf('day')
                        .utcOffset(TIMEZONE * 60)
                        .valueOf()
            )
            // check if already existed record for each week, if yes then only re-show the content:
            if (todayRecord && todayRecord.note) {
                // if (!todayIsFriday) {
                //   todayRecord.note = 'repeat-application';
                // }
                // return await this.getRollGiftBoardtype(todayRecord.note);
                // new flow:
                return await this.getRollGiftBoardtype('repeat-application')
            } else if (currentWeekRollRecords.find((e: any) => e.note === 'A')) {
                return await this.getRollGiftBoardtype('repeat-application')
            }
            // create new record for week:
            let contentObj = null
            let boardType = 'A'
            // FRIDAY show type B or C depend on user win or lose
            // OTHER day show type A
            if (todayIsFriday) {
                // now is friday
                // const user = await this.exec(User.findOne({
                //   where: {
                //     id: params.user_id
                //   },
                //   attributes: ["event_type"],
                // }))
                // // check to see type B (loser) or C (winner)
                // if (user.event_type === 'C') {
                //   boardType = 'C';
                // } else {
                //   boardType = 'B';
                // }
                // new flow
                boardType = 'A'
            } else {
                // repeat-application or A
                boardType = 'A'
            }
            contentObj = await this.getRollGiftBoardtype(boardType)

            console.log(
                '29148  ~ file: userService.ts ~ line 135 ~ UserService ~ doRollGift ~ contentObj',
                contentObj
            )

            // create record
            const bodyHistory: any = {
                category: HISTORY.USER.NAME,
                type_1: HISTORY.USER.TYPES.GIFT_ROLL.NAME,
                user_id: params.user_id,
                note: boardType,
            }

            const option: ICrudOption = {}
            await historyService.create(bodyHistory, option)

            return contentObj
        } catch (error) {
            throw error
        }
    }

    async changeImagePath(tableName: any, transaction: any) {
        //
        // await this.exec(Post.update({
        //   videos: []
        // }, {
        //   where: {
        //     videos: null
        //   },
        //   transaction: transaction
        // }))

        // - images: event, post, recruit, shop
        // - image: comment, link, review,
        // - thumbnail: banner, comment,  keyword, review
        // - thumbnails: post, recruit, shop
        // - badge_image: shop
        // - description: shop, event, recruit
        // - short_description: shop
        // - avatar: user, employee
        // - cover_avatar: user
        // - shop: old_shop (json), params_update (json), denied_shop (json)

        //  , banner, keyword,employee

        // event
        // post
        // recruit
        // shop
        // comment
        // link
        // review
        // banner
        // employee
        // user
        try {
            const converterFunc = (modelData: any) => {
                const body: any = {}
                // https://server.kormassage.kr:9877/api/v1/image/get_image_from_s3/resized-image-1616600033536.png
                // https://server.busandal31.net:9877/

                // // for original:
                // const OLD_PATH = 'https://server.kormassage.kr:9877/';
                // const NEW_PATH = 'https://server.busandal31.net:9877/';
                // for clone:

                // const OLD_PATH = 'https://server.busandal31.net:9877/';
                // const OLD_PATH_1 = 'https://server.kormassage.com:9887/';

                const OLD_PATH = 'https://server.kormassage.com:9887/'
                const NEW_PATH = 'https://server.kormsg.com:9887/'

                if (modelData.images && modelData.images.length > 0) {
                    const newListImgs = []
                    for (let i = 0; i < modelData.images.length; i++) {
                        const newUrl = modelData.images[i].split(OLD_PATH).join(NEW_PATH)
                        newListImgs.push(newUrl)
                    }
                    body.images = newListImgs
                }
                if (modelData.thumbnails && modelData.thumbnails.length > 0) {
                    const newListImgs = []
                    for (let i = 0; i < modelData.thumbnails.length; i++) {
                        const newUrl = modelData.thumbnails[i]
                            .split(OLD_PATH)
                            .join(NEW_PATH)
                        newListImgs.push(newUrl)
                    }
                    body.thumbnails = newListImgs
                }

                if (modelData.avatar) {
                    const newUrl = modelData.avatar.split(OLD_PATH).join(NEW_PATH)
                    body.avatar = newUrl
                }
                if (modelData.cover_avatar) {
                    const newUrl = modelData.cover_avatar.split(OLD_PATH).join(NEW_PATH)
                    body.cover_avatar = newUrl
                }
                if (modelData.image) {
                    const newUrl = modelData.image.split(OLD_PATH).join(NEW_PATH)
                    body.image = newUrl
                }
                if (modelData.thumbnail) {
                    const newUrl = modelData.thumbnail.split(OLD_PATH).join(NEW_PATH)
                    body.thumbnail = newUrl
                }
                if (modelData.badge_image) {
                    const newUrl = modelData.badge_image.split(OLD_PATH).join(NEW_PATH)
                    body.badge_image = newUrl
                }
                if (modelData.description) {
                    const newUrl = modelData.description.split(OLD_PATH).join(NEW_PATH)
                    body.description = newUrl
                }
                if (modelData.short_description) {
                    const newUrl = modelData.short_description
                        .split(OLD_PATH)
                        .join(NEW_PATH)
                    body.short_description = newUrl
                }
                return body
            }
            let modelName: any
            if (tableName === 'shop') {
                modelName = Shop
            } else if (tableName === 'user') {
                modelName = User
            } else if (tableName === 'event') {
                modelName = Event
            } else if (tableName === 'post') {
                modelName = Post
            } else if (tableName === 'recruit') {
                modelName = Recruit
            } else if (tableName === 'comment') {
                modelName = Comment
            } else if (tableName === 'link') {
                modelName = Link
            } else if (tableName === 'review') {
                modelName = Review
            } else if (tableName === 'banner') {
                modelName = Banner
            } else if (tableName === 'employee') {
                modelName = Employee
            }
            const listItems = await this.exec(modelName.findAll({paranoid: true}))
            for (let i = 0; i < listItems.length; i++) {
                //
                const itemId = listItems[i].id
                const itemModel = listItems[i]
                const body: any = {
                    ...converterFunc(itemModel),
                }
                //////////////
                if (itemModel.old_shop) {
                    const bodyForChild = {
                        ...itemModel.old_shop,
                        ...converterFunc(itemModel.old_shop),
                    }
                    body.old_shop = bodyForChild
                }
                if (itemModel.params_update) {
                    const bodyForChild = {
                        ...itemModel.params_update,
                        ...converterFunc(itemModel.params_update),
                    }
                    body.params_update = bodyForChild
                }
                if (itemModel.denied_shop) {
                    const bodyForChild = {
                        ...itemModel.denied_shop,
                        ...converterFunc(itemModel.denied_shop),
                    }
                    body.denied_shop = bodyForChild
                }

                await this.exec(
                    modelName.update(body, {
                        where: {
                            id: itemId,
                        },
                        transaction,
                    })
                )
            }
            return true
        } catch (error) {
            throw error
        }
    }

    async doSecretJobFunc(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        /// BELOW IS GET ALL SHOPS CONTAINS BASE64 IMG IN DESCRIPTION:
        console.log('29148 doSecretJobFunc start')

        const transaction = await sequelize.transaction()

        try {
            let result
            // 1: Update geolocation_api_type of all shop to NAVER
            // 2: find and removes all shops of deleted users.
            // 3: update all description_content of all shops.
            if (params.option1 === '1') {
                result = await this.exec(
                    Shop.update(
                        {
                            geolocation_api_type: 'NAVER',
                        },
                        {
                            where: {
                                geolocation_api_type: null,
                            },
                            transaction,
                        }
                    )
                )
            } else if (params.option1 === '2') {
                const includeUser: any = [
                    {
                        association: 'user',
                        include: [],
                        distinct: true,
                        attributes: ['id', 'account_type'],
                    },
                ]
                const shops = await this.exec(
                    Shop.findAll({
                        include: includeUser,
                        attributes: ['id', 'user_id'],
                    })
                )

                for (let i = 0; i < shops.length; i++) {
                    const shop = shops[i]
                    if (!shop.user) {
                        await this.exec(
                            Shop.destroy({
                                where: {
                                    user_id: shop.user_id,
                                },
                                transaction,
                            })
                        )
                    }
                }
                result = shops.length
            } else if (params.option1 === '3') {
                //
                const includeUser: any = [
                    {
                        association: 'user',
                        include: [],
                        distinct: true,
                        attributes: ['id', 'account_type'],
                    },
                ]
                const shops = await this.exec(
                    Shop.findAll({
                        include: includeUser,
                        attributes: ['id', 'user_id', 'title'],
                    })
                )
                for (let i = 0; i < shops.length; i++) {
                    await this.exec(
                        Shop.update(
                            {
                                title: shops[i].title,
                            },
                            {
                                where: {
                                    id: shops[i].id,
                                },
                                transaction,
                            }
                        )
                    )
                }
            } else {
                result = false
            }

            transaction.commit()
            return result
        } catch (error) {
            console.log('29148  ~ doSecretJobFunc ~ error', error)
            transaction.rollback()
            throw error
        }
    }

    async checkExistPhoneNumber(params: any) {
        const {phone} = params
        const check = await this.exec(User.findOne({where: {phone}}))
        if (check)
            throw errorService.database.queryError({
                message: 'Phone already in use!',
                code: 409,
            })
        return check
    }

    async checkLoginWithNaver(params: any, option?: ICrudOption) {
        let dataFromNaver: any = {}
        console.log('@@@@ ', params.naverToken)
        const access_token = 'Bearer ' + params.naverToken
        try {
            const response = await axios.get(`https://openapi.naver.com/v1/nid/me`, {
                headers: {Authorization: access_token},
            })
            const data = response.data.response
            dataFromNaver = data
            console.log(dataFromNaver)
        } catch (error) {
            console.log(error)
        }

        let result = await this.exec(
            this.model.findOne({
                where: {
                    login_type: 'NAVER',
                    username: dataFromNaver.id,
                },
            })
        )

        if (!result) {
            const body: any = {
                username: dataFromNaver.id,
                password: dataFromNaver.id,
                fullname: dataFromNaver.id,
                nickname: dataFromNaver.id,
                login_type: LOGIN_TYPE.NAVER,
                platform_create: params.platform_create,
                account_type: params.account_type,
            }
            if (dataFromNaver.email) {
                body.email = dataFromNaver.email
            }
            result = await this.exec(
                this.model.create(body, this.applyCreateOptions(option))
            )

            result.isNewUser = true
            await recordService.increaseCurrentRecordData(
                {
                    new_member_count: true,
                },
                undefined
            )
            // reward JOIN_MEMBERSHIP 
            await this.exec(rewardService.addReward(result.id, "JOIN_MEMBERSHIP"));
        }
        await this.exec(
            User.update(
                {
                    sign_in_time_unix_timestamp: moment().valueOf(),
                },
                {
                    where: {
                        id: result.id,
                    },
                }
            )
        )
        result.password = undefined
        return result
    }

    getKakaoUserInfo = async (kakaoToken: any) => {
        try {
            const response = await axios.get(`https://kapi.kakao.com/v2/user/me`, {
                headers: {
                    Authorization: `Bearer ${kakaoToken}`,
                },
            })
            return response
        } catch (error) {
            return null
        }
    }

    async checkLoginWithKakaotalk(params: any, option?: ICrudOption) {
        let dataFromKakaotalk: any = {}
        try {
            const response = await axios.get(
                `https://kapi.kakao.com/v1/user/access_token_info`,
                {
                    headers: {
                        Authorization: `Bearer ${params.kakaotalkToken}`,
                    },
                }
            )
            const data = response.data
            dataFromKakaotalk = data
            console.log(data)
        } catch (error) {
            console.log(error)
        }
        let result = await this.exec(
            this.model.findOne({
                where: {
                    login_type: 'KAKAO',
                    username: `${dataFromKakaotalk.id}`,
                    // social_network_app_id: `${dataFromKakaotalk.appId}`
                },
            })
        )

        if (!result) {
            const body: any = {
                username: dataFromKakaotalk.id,
                password: dataFromKakaotalk.id,
                fullname: dataFromKakaotalk.id,
                nickname: dataFromKakaotalk.id,
                login_type: LOGIN_TYPE.KAKAO,
                platform_create: params.platform_create,
                account_type: params.account_type,
            }
            try {
                const userInfo = await this.getKakaoUserInfo(params.kakaotalkToken)

                if (
                    userInfo &&
                    userInfo.data &&
                    userInfo.data.kakao_account &&
                    userInfo.data.kakao_account.email
                ) {
                    body.email = userInfo.data.kakao_account.email
                }
            } catch (error) {
            }

            result = await this.exec(
                this.model.create(body, this.applyCreateOptions(option))
            )

            result.isNewUser = true
            await recordService.increaseCurrentRecordData(
                {
                    new_member_count: true,
                },
                undefined
            )
        }
        await this.exec(
            User.update(
                {
                    sign_in_time_unix_timestamp: moment().valueOf(),
                },
                {
                    where: {
                        id: result.id,
                    },
                }
            )
        )
        result.password = undefined
        return result
    }

    async getLocationInfoFunc(params: any) {
        try {
            const response = await axios.get(
                `https://naveropenapi.apigw.ntruss.com/map-reversegeocode/v2/gc?coords=${params.lng},${params.lat}&output=json&orders=addr,roadaddr`,
                {
                    headers: {
                        'X-NCP-APIGW-API-KEY-ID': 'q1akdla7qx',
                        'X-NCP-APIGW-API-KEY': 'VSGq5tFHRriofHTLIHAYHVGu6PV12GavO7ob40Xk',
                    },
                }
            )
            return response.data
        } catch (error) {
            return error
        }
    }

    async getPlacesInfoFunc(params: any) {
        try {
            let url = `https://naveropenapi.apigw.ntruss.com/map-geocode/v2/geocode?query=${encodeURI(
                params.address
            )}`
            if (params.lat && params.lng) {
                url = url + `&coordinate=${params.lng},${params.lat}`
            }

            const response = await axios.get(url, {
                headers: {
                    'X-NCP-APIGW-API-KEY-ID': 'q1akdla7qx',
                    'X-NCP-APIGW-API-KEY': 'VSGq5tFHRriofHTLIHAYHVGu6PV12GavO7ob40Xk',
                },
            })

            return response.data
        } catch (error) {
            return error
        }
    }

    async checkLoginWithApple(params: any, option?: ICrudOption) {
        let dataFromApple: any = {}
        try {
            dataFromApple = await appleSignin.verifyIdToken(params.apple_token)
            // await appleSignin.verifyIdToken(params.apple_token, "com.hitek.kidztok").then(async (result: any) => {
            //   dataFromApple = result;
            //   console.log("haizzz  ", dataFromApple);
            // })
            // const applePublicKey = await this.getApplePublicKey();

            // const result = jwt.decode(params.apple_token, applePublicKey);
            // console.log("@@@@ ",result)
        } catch (error) {
            console.log('error ', error)
            throw errorService.database.queryFail(error.message)
        }

        let result = await this.exec(
            this.model.findOne({
                where: {
                    login_type: 'APPLE',
                    username: dataFromApple.sub,
                },
            })
        )
        if (!result) {
            const body: any = {
                username: dataFromApple.sub,
                password: dataFromApple.sub,
                email: dataFromApple.email,
                fullname: dataFromApple.email || dataFromApple.sub,
                login_type: 'APPLE',
                platform_create: params.platform_create,
                account_type: params.account_type,
            }
            result = await this.exec(
                this.model.create(body, this.applyCreateOptions(option))
            )
            result.isNewUser = true
            // reward JOIN_MEMBERSHIP
            await this.exec(rewardService.addReward(result.id, "JOIN_MEMBERSHIP"));
        }
        return result
    }

    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {statistic} = params
        const result = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        // const users = result.rows && result.rows.length > 0 ? result.rows : [];
        // for (let i = 0; i < users.length; i++) {
        //   if (users[i].account_type === USER_TYPE.BIZ_USER) {
        //     const userShops = await this.exec(
        //       Shop.findAll({
        //         where: {
        //           user_id: users[i].id,
        //         },
        //       })
        //     );
        //     users[i].dataValues.current_active_post = userShops.filter(
        //       (e: any) => e.state === SHOP_STATE.APPROVED
        //     ).length;
        //     users[i].dataValues.current_pending_post = userShops.filter(
        //       (e: any) => e.state === SHOP_STATE.PENDING
        //     ).length;
        //     users[i].dataValues.current_rejected_post = userShops.filter(
        //       (e: any) => e.state === SHOP_STATE.REJECTED
        //     ).length;
        //     users[i].dataValues.current_expired_post = userShops.filter(
        //       (e: any) => e.state === SHOP_STATE.EXPIRED
        //     ).length;
        //   }
        // }
        // console.log('29148 userService get list :', result);
        if (statistic) {
            for (const row of result.rows) {
                const [totalPost, totalReview, current_on_event_shop, totalReservation] = await Promise.all([
                    Post.count({where: {user_id: row.id}}),
                    Review.count({where: {user_id: row.id}}),
                    Shop.count({
                        where: {
                            user_id: row.id,
                            state: {
                                [Op.ne]: SHOP_STATE.EXPIRED
                            }
                        },
                        include: [{
                            association: "events",
                            required: true,
                            where: {
                                state: {
                                    [Op.ne]: SHOP_STATE.EXPIRED
                                }
                            }
                        }]
                    }),
                    Reservation.count({where: {user_id: row.id}})
                ])
                row.dataValues.totalPost = totalPost
                row.dataValues.totalReview = totalReview
                row.dataValues.totalReservation = totalReservation
                row.dataValues.current_on_event_shop = current_on_event_shop
            }
        }
        return result
    }

    convertImageUrl(oldUrl: String) {
        // Hàm đổi url ảnh:
        // "https://kormassage.kr:9877/api/v1/image/get/resized-image-1598883072740.png"
        const OLD_STRING = 'https://kormassage.kr:9877'
        const NEW_STRING = 'https://server.kormassage.kr:9877'
        if (oldUrl && oldUrl.includes(NEW_STRING)) {
            return oldUrl
        } else if (oldUrl) {
            return oldUrl.replace(OLD_STRING, NEW_STRING)
        }
        return ''
    }

    attachInfoToData(item: any) {
        // Hàm gắn url vào body
        if (item) {
            const body: any = {}
            if (item && item.avatar) {
                body.avatar = this.convertImageUrl(item.avatar)
            }
            if (item && item.cover_avatar) {
                body.cover_avatar = this.convertImageUrl(item.cover_avatar)
            }
            if (item && item.badge_image) {
                body.badge_image = this.convertImageUrl(item.badge_image)
            }
            if (item && item.image) {
                body.image = this.convertImageUrl(item.image)
            }
            if (item && item.images && item.images.length) {
                const newArr = item.images.map((url: String) =>
                    this.convertImageUrl(url)
                )
                body.images = newArr
            }
            if (item && item.thumbnails && item.thumbnails.length) {
                const newArr = item.thumbnails.map((url: String) =>
                    this.convertImageUrl(url)
                )
                body.thumbnails = newArr
            }
            return body
        }
        return null
    }

    async onQueryModel(model: any, transaction: any) {
        // Hàm query data:
        const items = await this.exec(model.findAll({transaction}))
        for (let i = 0; i < items.length; i++) {
            if (items[i]) {
                const body: any = this.attachInfoToData(items[i])
                if (body) {
                    await this.exec(
                        model.update(body, {
                            where: {
                                id: items[i].id,
                            },
                            transaction,
                        })
                    )
                }
            }
        }
        return items.length
    }

    async syncImagesUrl(params: any) {
        // hàm gọi update toàn db
        const transaction = await sequelize.transaction()
        try {
            const user_count = await this.onQueryModel(User, transaction)
            const employee_count = await this.onQueryModel(Employee, transaction)
            const post_count = await this.onQueryModel(Post, transaction)
            const shop_count = await this.onQueryModel(Shop, transaction)
            const event_count = await this.onQueryModel(Event, transaction)
            const link_count = await this.onQueryModel(Link, transaction)
            const comment_count = await this.onQueryModel(Comment, transaction)
            const review_count = await this.onQueryModel(Review, transaction)
            const banner_count = await this.onQueryModel(Banner, transaction)

            transaction.commit()

            return {
                success: true,
                user_count,
                employee_count,
                post_count,
                event_count,
                shop_count,
                link_count,
                comment_count,
                review_count,
                banner_count,
            }
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async requestFacebookDelete(params: any) {
        // const { registration_token, topic } = params;
        // if (!registration_token || !topic) {
        //   throw errorService.database.queryFail(
        //     'Please add user firebase registration_token and topic to continue.'
        //   );
        // }
        // const isSuccess = await firebaseService.messaging.subscribeToTopic(
        //   registration_token,
        //   topic
        // );
        // return {
        //   success: isSuccess,
        // };
        try {
            const {user_id} = params
            let confirmation_code = 401

            console.log('29148  ~ FACEBOOK DELETE ~ user_id', user_id)
            if (user_id) {
                console.log('29148  ~ FACEBOOK DELETE ~ vao delete', user_id)

                await this.exec(
                    this.model.destroy({
                        where: {
                            login_type: LOGIN_TYPE.FACEBOOK,
                            username: user_id,
                        },
                    })
                )
                confirmation_code = 200
            }
            console.log('29148  ~ FACEBOOK DELETE ~ delete 2', confirmation_code)

            const url = `https://${process.env.HOST_NAME}:${process.env.PORT_SSL}/api/v1/user/find_user/${user_id}`
            console.log('29148  ~ FACEBOOK DELETE ~ delete 3', url)

            //     $status_url = 'https://www.<your_website>.com/deletion?id=abc123'; // URL to track the deletion
            // $confirmation_code = 'abc123'; // unique code for the deletion request
            return {
                url: `https://${process.env.HOST_NAME}:${process.env.PORT_SSL}/api/v1/user/find_user/${user_id}`,
                confirmation_code,
            }
        } catch (error) {
            throw error
        }
    }

    async subscribeToTopic(params: any) {
        const {registration_token, topic} = params
        if (!registration_token || !topic) {
            throw errorService.database.queryFail(
                'Please add user firebase registration_token and topic to continue.'
            )
        }
        const isSuccess = await firebaseService.messaging.subscribeToTopic(
            registration_token,
            topic
        )
        return {
            success: isSuccess,
        }
    }

    async unsubscribeFromTopic(params: any) {
        const {registration_token, topic} = params
        if (!registration_token || !topic) {
            throw errorService.database.queryFail(
                'Please add registration_token and topic to continue.'
            )
        }
        const isSuccess = await firebaseService.messaging.unsubscribeFromTopic(
            registration_token,
            topic
        )
        return {
            success: isSuccess,
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const item = await this.exec(this.getItem(option), {allowNull: false})
        if (params.employee_id) {
            const message = FCM_ACTIONS_MESSAGE.DISABLED_ACCOUNT
            const body_noti: any = {
                user_id: item.id,
                title: message,
                content: message,
                data: {message: message},
                action: FCM_ACTIONS.DISABLED_ACCOUNT,
                interacting_type: 'DISABLED_ACCOUNT',
            }
            // firebaseService.sendNotification(
            //     item.id,
            //     message,
            //     FCM_ACTIONS.DISABLED_ACCOUNT,
            //     item.id
            // )
            await this.exec(
                Notification.create(body_noti, this.applyCreateOptions(option))
            )
        }
        return await this.exec(item.destroy())
    }

    async sendNotification(params: any, option?: ICrudOption) {
        const message = params.message
        const user_id = params.user_id
        // await this.exec(Notification.create({ user_id, title, content, data }, this.applyCreateOptions(option)))
        try {
            // firebaseService.sendNotification(
            //     user_id,
            //     message,
            //     FCM_ACTIONS.SEND_NOTIFICATION
            // )
            return {user_id, message}
        } catch (error) {
            return {user_id, error}
        }
    }

    async updateCurrentRecommendationPost(
        user_id: string,
        shouldIncreaseCounterToIncludeCurrentItem: boolean = false,
        transaction: any = undefined
    ) {
        try {
            if (user_id) {
                const count_recommendation_post_of_user = await this.exec(
                    Shop.findAll({
                        where: {
                            user_id: user_id,
                            is_random_20_shop: true,
                        },
                        attributes: ['id'],
                        transaction,
                    })
                )

                const body: any = {
                    current_recommendation_post: count_recommendation_post_of_user.length,
                }

                // shouldIncreaseCounterToIncludeCurrentItem: will increase value by 1 to include current changing data. in create or edit, when we call these api we dont usually include this value
                if (shouldIncreaseCounterToIncludeCurrentItem) {
                    body.current_recommendation_post =
                        body.current_recommendation_post + 1
                }

                const newUser = await this.exec(
                    User.update(body, {
                        where: {
                            id: user_id,
                        },
                        transaction,
                    })
                )
            }
        } catch (error) {
            console.log(
                '29148 : UserService -> updateCurrentRecommendationPost -> error',
                error
            )
        }
    }

    async updateJumpLimitAPI(params: any, option?: ICrudOption) {
        const {user_role} = params
        const transaction = await sequelize.transaction()
        const isNormalUser = user_role === ROLE.USER

        try {
            // only support update jump_limit in this API:
            const body = {
                jump_limit: params.jump_limit,
            }
            await this.exec(
                User.update(body, {
                    where: {
                        id: option.filter.id,
                    },
                    transaction,
                })
            )

            // create record history:
            const user = await this.exec(
                User.findOne({
                    where: {
                        id: option.filter.id,
                    },
                    attributes: ['id', 'jump_limit'],
                })
            )
            if (user) {
                const bodyHistory: any = {
                    user_id: user.id,
                    category: HISTORY.USER.NAME,
                    type_1: HISTORY.USER.TYPES.JUMP_UP.NAME,
                    note: params.jump_limit - user.jump_limit,
                }
                await this.exec(
                    History.create(bodyHistory, {
                        transaction,
                    })
                )
            }
            transaction.commit()
            return this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async update(params: any, option?: ICrudOption) {
        const {user_role, nickname} = params
        const isNormalUser = user_role === ROLE.USER

        try {
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })

            if (nickname !== item.nickname) {
                const checkNicknameExist = await this.exec(
                    this.model.findOne({
                        where: {
                            nickname,
                        },
                        attributes: ['id'],
                    })
                )
                if (checkNicknameExist) {
                    throw errorService.database.queryFail(
                        'Your Nickname have already existed, please use another Nickname!',
                        NICKNAME_ALREADY_EXISTED_CODE
                    )
                }
            }
            if (params.phone && params.phone !== item.phone) {
                const checkPhoneExist = await this.exec(
                    this.model.findOne({
                        where: {
                            phone: params.phone,
                        },
                        attributes: ['id'],
                    })
                )
                if (checkPhoneExist) {
                    throw errorService.database.queryFail(
                        'Your phone number have already existed, please use another phone number!'
                    )
                }
            }

            if (params && params.email && params.email !== item.email) {
                const user = await this.exec(
                    this.model.findOne({
                        where: {
                            email: params.email,
                        },
                        attributes: ['id'],
                    })
                )
                if (user) {
                    throw errorService.database.queryFail(
                        'Your email have already existed, please use another email!'
                    )
                }
            } else if (
                params &&
                params.post_limit >= 0 &&
                item.post_limit !== params.post_limit
            ) {
                const USED_POST_NUMBER =
                    item.current_active_post + item.current_pending_post

                if (params.post_limit < USED_POST_NUMBER) {
                    throw errorService.database.queryFail(
                        'This user current advertising and pending post exceeded your new limit!'
                    )
                }

                const message =
                    params.post_limit > item.post_limit
                        ? FCM_ACTIONS_MESSAGE.INCREASE_POST_LIMIT
                        : FCM_ACTIONS_MESSAGE.DECREASE_POST_LIMIT
                const action =
                    params.post_limit > item.post_limit
                        ? FCM_ACTIONS.INCREASE_POST_LIMIT
                        : FCM_ACTIONS.DECREASE_POST_LIMIT
                // firebaseService.sendNotification(item.id, message, action)

                const bodyNoti: any = {
                    user_id: item.id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action: action,
                }

                await this.exec(Notification.create(bodyNoti))
            } else if (
                params &&
                params.show_shop_tag !== null &&
                params.show_shop_tag !== undefined &&
                item.show_shop_tag !== params.show_shop_tag
            ) {
                const message = params.show_shop_tag
                    ? FCM_ACTIONS_MESSAGE.EXPOSE_CATEGORY_TAG
                    : FCM_ACTIONS_MESSAGE.HIDE_CATEGORY_TAG
                const action = params.show_shop_tag
                    ? FCM_ACTIONS.EXPOSE_CATEGORY_TAG
                    : FCM_ACTIONS.HIDE_CATEGORY_TAG
                // firebaseService.sendNotification(item.id, message, action)
                const bodyNoti: any = {
                    user_id: item.id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action: action,
                }
                await this.exec(Notification.create(bodyNoti))
            } else if (
                !isNormalUser &&
                params.account_type &&
                item.account_type !== params.account_type
            ) {
                if (item.id) {
                    if (
                        params.account_type === USER_TYPE.PAID_USER &&
                        params.paid_user_expiration_date !== item.paid_user_expiration_date
                    ) {
                        await notificationService.sendFCMandCreateNoti(
                            item.id,
                            FCM_ACTIONS_MESSAGE.YOUR_ACCOUNT_HAVE_BEEN_UPGRADED,
                            FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_UPGRADED,
                            item.id
                        )
                    } else if (params.account_type === USER_TYPE.FREE_USER) {
                        params.paid_user_expiration_date = null
                        await notificationService.sendFCMandCreateNoti(
                            item.id,
                            FCM_ACTIONS_MESSAGE.YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED,
                            FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED,
                            item.id
                        )
                    }
                }
            } else if (
                !isNormalUser &&
                params.paid_user_expiration_date !== item.paid_user_expiration_date
            ) {
            } else if (!isNormalUser) {
                await notificationService.sendFCMandCreateNoti(
                    item.id,
                    FCM_ACTIONS_MESSAGE.YOUR_ACCOUNT_HAVE_BEEN_EDITED,
                    FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_EDITED,
                    item.id
                )
            }

            await this.exec(item.update(params))
            return await this.getItem(option)
        } catch (e) {
            throw e
        }
    }

    async changeLanguage(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            await this.exec(
                User.update(
                    {
                        language: params.language,
                    },
                    {
                        where: {
                            id: option.filter.id,
                        },
                        transaction,
                    }
                )
            )

            transaction.commit()
            return this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async changePassword(params: any, option?: ICrudOption) {
        const {password, new_password, user_id} = params
        const user = await this.exec(
            this.model.findOne({
                where: {
                    id: user_id,
                    password,
                },
            })
        )
        if (user) {
            await this.exec(
                User.update(
                    {
                        password: new_password,
                    },
                    {where: {id: user.id}}
                )
            )
            return user
        } else {
            throw errorService.auth.unauthorized()
        }
    }

    async getPassword(params: any, option?: ICrudOption) {
        const {old_password, new_password, user_id} = params
        const findUser = await this.exec(
            this.model.findOne({
                where: {
                    id: user_id,
                    password: old_password,
                },
            })
        )
        if (findUser) {
            await this.exec(
                User.update(
                    {
                        password: new_password,
                    },
                    {where: {id: findUser.id}}
                )
            )
            return findUser
        } else {
            throw errorService.database.queryFail(
                'Token hết hiệu lực lấy lại mật khẩu'
            )
        }
    }

    async create(params: any, option?: ICrudOption) {
        params.username = params.username.toLowerCase()

        const username = params.username
        const phone = params.phone
        const resultUserName: any = await this.model.count({
            where: {
                username,
            },
        })
        const lenghtPhone = phone ? phone.length : 0
        const lenghtUserName = username ? username.length : 0
        if (lenghtUserName < 2) {
            throw errorService.database.queryFail(
                'username must have at least 2 characters'
            )
        } else if (
            params.account_type === USER_TYPE.BIZ_USER &&
            phone &&
            (lenghtPhone > 15 || lenghtPhone < 9)
        ) {
            throw errorService.database.queryFail('invalid phone number')
        } else if (resultUserName >= 1) {
            throw errorService.database.queryFail(username + 'already exist')
        } else {
            const data = {
                ...params,
                nickname: params.nickname ? params.nickname : username,
            }
            const user = await this.exec(
                this.model.findOne({
                    where: {
                        username: params.username,
                    },
                    attributes: ['id'],
                })
            )
            if (user) {
                throw errorService.database.queryFail(
                    'Your ID have already existed, please use another ID!'
                )
            }
            const checkNicknameExist = await this.exec(
                this.model.findOne({
                    where: {
                        nickname: params.nickname,
                    },
                    attributes: ['id'],
                })
            )
            if (checkNicknameExist) {
                throw errorService.database.queryFail(
                    'Your Nickname have already existed, please use another Nickname!',
                    NICKNAME_ALREADY_EXISTED_CODE
                )
            }
            const createdUser = await this.exec(
                this.model.create(data, this.applyCreateOptions(option))
            )
            // Add reward after user creation
            await this.exec(rewardService.addReward(createdUser.id, "JOIN_MEMBERSHIP", option?.transaction));
            await recordService.increaseCurrentRecordData(
                {
                    new_member_count: true,
                },
                undefined
            )
            return createdUser
        }
    }

    async checkLogin(params: any, option?: ICrudOption) {
        params.username = params.username.toLowerCase()

        const result = await this.exec(
            this.model.findOne({
                where: {
                    username: params.username,
                },
            })
        )

        if (!result) throw errorService.database.queryFail('가입하지 않은 아이디입니다. 회원가입해 주세요')

        if (result && result.status && result.password === params.password) {
            await this.exec(
                this.model.update(
                    {
                        sign_in_time_unix_timestamp: moment().valueOf(),
                    },
                    {
                        where: {
                            id: result.id,
                        },
                    }
                )
            )
            result.password = undefined
            return result
        } else {
            throw errorService.database.queryFail('password is not correct')
        }
    }

    async checkLoginBiz(params: any, option?: ICrudOption) {
        params.username = params.username.toLowerCase()

        const result = await this.exec(
            this.model.findOne({
                where: {
                    username: params.username,
                    password: params.password,
                },
            })
        )

        if (result && result.status && result.account_type === USER_TYPE.BIZ_USER) {
            await this.exec(
                this.model.update(
                    {
                        sign_in_time_unix_timestamp: moment().valueOf(),
                    },
                    {
                        where: {
                            id: result.id,
                        },
                    }
                )
            )
            result.password = undefined
            return result
        } else {
            throw errorService.database.queryFail(
                'Hãy kiểm tra lại tài khoản và mật khẩu'
            )
        }
    }

    async checkLoginWithGoogle(params: any, option?: ICrudOption) {
        let dataFromGoogle: any = {}
        try {
            const response = await axios.get(
                `https://oauth2.googleapis.com/tokeninfo?access_token=${params.googleToken}`
            )
            const data = response.data
            dataFromGoogle = data
        } catch (error) {
            console.log(error)
        }
        let result = await this.exec(
            this.model.findOne({
                where: {
                    login_type: LOGIN_TYPE.GOOGLE,
                    username: dataFromGoogle.email,
                },
            })
        )
        if (!result) {
            const body: any = {
                username: dataFromGoogle.email,
                password: dataFromGoogle.email,
                email: dataFromGoogle.email,
                fullname: dataFromGoogle.name || dataFromGoogle.email,
                nickname: dataFromGoogle.name || dataFromGoogle.email,
                login_type: LOGIN_TYPE.GOOGLE,
                platform_create: params.platform_create,
                account_type: params.account_type,
            }
            result = await this.exec(
                this.model.create(body, this.applyCreateOptions(option))
            )
            result.isNewUser = true

            await recordService.increaseCurrentRecordData(
                {
                    new_member_count: true,
                },
                undefined
            )
            // reward JOIN_MEMBERSHIP
            await this.exec(rewardService.addReward(result.id, "JOIN_MEMBERSHIP"));
        }
        await this.exec(
            User.update(
                {
                    sign_in_time_unix_timestamp: moment().valueOf(),
                },
                {
                    where: {
                        id: result.id,
                    },
                }
            )
        )
        return result
    }

    async checkLoginWithFacebook(params: any, option?: ICrudOption) {
        let dataFromFacebook: any = {}
        try {
            const response = await axios.get(
                `https://graph.facebook.com/me?access_token=${params.facebookToken}`
            )
            const data = response.data
            dataFromFacebook = data
            console.log('bambi login facebook ne', response)
        } catch (error) {
            console.log('Authenticate facebook failed', error)
        }
        console.log('bambi login facebook ne', dataFromFacebook)
        let result = await this.exec(
            this.model.findOne({
                where: {
                    login_type: LOGIN_TYPE.FACEBOOK,
                    username: dataFromFacebook.id,
                },
            })
        )
        // if (result && result.avatar != params.avatar) {
        //     result = await result.update({
        //         avatar: `https://graph.facebook.com/${dataFromFacebook.id}/picture`,
        //     })
        // }
        console.log('bambi login facebook ne 2', result)
        if (!result) {
            const body: any = {
                username: dataFromFacebook.id,
                password: dataFromFacebook.id,
                fullname: dataFromFacebook.name,
                nickname: dataFromFacebook.name,
                avatar: `https://graph.facebook.com/${dataFromFacebook.id}/picture`,
                login_type: LOGIN_TYPE.FACEBOOK,
                platform_create: params.platform_create,
                account_type: params.account_type,
            }
            result = await this.exec(
                this.model.create(body, this.applyCreateOptions(option))
            )

            result.isNewUser = true
            await recordService.increaseCurrentRecordData(
                {
                    new_member_count: true,
                },
                undefined
            )
            // reward JOIN_MEMBERSHIP
            await this.exec(rewardService.addReward(result.id, "JOIN_MEMBERSHIP"));
        }
        await this.exec(
            User.update(
                {
                    sign_in_time_unix_timestamp: moment().valueOf(),
                },
                {
                    where: {
                        id: result.id,
                    },
                }
            )
        )
        result.password = undefined
        return result
    }

    async scheduleUpdateUserNearestExpireShopDate() {
        try {
            const shops = await this.exec(
                Shop.findAll({
                    where: {
                        state: SHOP_STATE.APPROVED,
                    },
                    attributes: ['user_id', 'expired_date'],
                })
            )
            if (shops) {
                const user_ids = uniq(
                    shops.filter((e: any) => e.user_id).map((e: any) => e.user_id)
                )
                for (let i = 0; i < user_ids.length; i++) {
                    const userShops = shops.filter(
                        (e: any) => e && e.user_id && e.user_id === user_ids[i]
                    )
                    if (userShops && userShops.length > 0) {
                        shopService.updateUserRemainingDate(userShops, user_ids[i])
                    }
                }
            }
            return {status: shops.length}
        } catch (error) {
            console.log(
                '29148 scheduleUpdateUserNearestExpireShopDate error: ',
                error
            )
        }
    }

    async updateAllUserRanking() {
        const limit = 100
        let page = 0
        let outOfData = false
        let newRanking = 1
        do {
            const users = await this.exec(
                User.findAll({
                    where: {
                        status: true,
                    },
                    limit: limit,
                    offset: page * limit,
                    order: [['exp', 'DESC']],
                    attributes: ['id', 'ranking'],
                })
            )

            if (users.length) {
                for (let i = 0; i < users.length; i++) {
                    const user = users[i]
                    // hạng mới < hạng cũ ==> + => tăng hagn5
                    // hạng mới > hạng cũ ==> - ==> giảm hạng
                    await this.exec(
                        User.update(
                            {
                                ranking: newRanking,
                                daily_ranking_delta: user.ranking - newRanking,
                            },
                            {
                                where: {
                                    id: user.id,
                                },
                            }
                        )
                    )
                    newRanking++
                }
                page++
            } else {
                outOfData = true
            }
        } while (!outOfData)
    }

    async updateUserPostType(user_id: String, transaction: any = undefined) {
        const userShops = await this.exec(
            Shop.findAll({
                where: {
                    user_id: user_id,
                },
                attributes: ['state', 'old_shop', 'is_random_20_shop'],
                transaction,
            })
        )
        const current_active_post = userShops.filter(
            (e: any) =>
                e.state === SHOP_STATE.APPROVED ||
                (e.state === SHOP_STATE.PENDING && e.old_shop)
        ).length
        const current_pending_post = userShops.filter(
            (e: any) => e.state === SHOP_STATE.PENDING && !e.old_shop
        ).length
        const current_rejected_post = userShops.filter(
            (e: any) => e.state === SHOP_STATE.REJECTED
        ).length
        const current_expired_post = userShops.filter(
            (e: any) => e.state === SHOP_STATE.EXPIRED
        ).length
        const current_recommendation_post = userShops.filter(
            (e: any) => e.is_random_20_shop
        ).length
        const body = {
            current_active_post,
            current_pending_post,
            current_rejected_post,
            current_expired_post,
            current_recommendation_post,
        }

        await this.exec(
            User.update(body, {
                where: {
                    id: user_id,
                },
                transaction,
            })
        )
    }

    async syncPostLimitAndPostStateCounter() {
        // hàm này để gọi để update lại toàn bộ post_limit và current_active_post của toàn bộ biz user  cho 2 cái bằng nhau
        const transaction = await sequelize.transaction()
        console.log('29148 syncPostLimitAndActivepost start: ')

        try {
            const users = await this.exec(
                User.findAll({
                    where: {
                        account_type: USER_TYPE.BIZ_USER,
                    },
                    attributes: ['id'],
                    transaction,
                })
            )
            if (users) {
                for (let i = 0; i < users.length; i++) {
                    const userShops = await this.exec(
                        Shop.findAll({
                            where: {
                                user_id: users[i].id,
                            },
                            attributes: ['state', 'old_shop', 'is_random_20_shop'],
                        })
                    )
                    const current_active_post = userShops.filter(
                        (e: any) =>
                            e.state === SHOP_STATE.APPROVED ||
                            (e.state === SHOP_STATE.PENDING && e.old_shop)
                    ).length
                    const current_pending_post = userShops.filter(
                        (e: any) => e.state === SHOP_STATE.PENDING && !e.old_shop
                    ).length
                    const current_rejected_post = userShops.filter(
                        (e: any) => e.state === SHOP_STATE.REJECTED
                    ).length
                    const current_expired_post = userShops.filter(
                        (e: any) => e.state === SHOP_STATE.EXPIRED
                    ).length
                    const current_recommendation_post = userShops.filter(
                        (e: any) => e.is_random_20_shop
                    ).length

                    this.exec(
                        User.update(
                            {
                                post_limit: current_active_post + current_pending_post,
                                current_active_post,
                                current_pending_post,
                                current_rejected_post,
                                current_expired_post,
                                current_recommendation_post,
                            },
                            {
                                where: {
                                    id: users[i].id,
                                },
                                transaction,
                            }
                        )
                    )
                }
            }
            transaction.commit()
            console.log(
                '29148 syncPostLimitAndActivepost users.length done: ',
                users.length
            )

            return {status: users.length}
        } catch (error) {
            console.log('29148 syncPostLimitAndActivepost error: ', error)
            transaction.rollback()
        }
    }

    async scheduleBeforeUpdatePaidUserExpiration() {
        try {
            const users = await this.exec(
                User.findAll({
                    where: {
                        account_type: USER_TYPE.PAID_USER,
                        paid_user_expiration_date: {
                            $between: [
                                moment()
                                    .utcOffset(TIMEZONE * 60)
                                    .startOf('day')
                                    .valueOf(),
                                moment()
                                    .utcOffset(TIMEZONE * 60)
                                    .endOf('day')
                                    .valueOf(),
                            ],
                        },
                    },
                })
            )

            if (users && users.length) {
                for (let i = 0; i < users.length; i++) {
                    if (users[i].id) {
                        // await this.doExpiredPost(users[i]);
                        await notificationService.sendFCMandCreateNoti(
                            users[i].id,
                            FCM_ACTIONS_MESSAGE.YOUR_ACCOUNT_PRE_DOWNGRADED_TO_FREE_USER,
                            FCM_ACTIONS.YOUR_ACCOUNT_PRE_DOWNGRADED_TO_FREE_USER,
                            users[i].id
                        )
                    }
                }
            }
            return {status: users.length}
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
        }
    }

    async testChangeExpiredDate(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update(params))
        return await this.getItem(option)
    }

    async scheduleUpdatePaidUserExpiration() {
        try {
            const timeNow = moment()
                .utcOffset(TIMEZONE * 60)
                .startOf('day')

            const users = await this.exec(
                User.findAll({
                    where: {
                        account_type: USER_TYPE.PAID_USER,
                        paid_user_expiration_date: {$lt: timeNow.valueOf()},
                    },
                })
            )

            if (users && users.length) {
                const user_ids = users.map((e: any) => e.id)

                await this.exec(
                    User.update(
                        {
                            account_type: USER_TYPE.FREE_USER,
                            paid_user_expiration_date: null,
                        },
                        {
                            where: {
                                id: {$in: user_ids},
                            },
                        }
                    )
                )
                for (let i = 0; i < users.length; i++) {
                    if (users[i].id) {
                        // await this.doExpiredPost(users[i]);
                        await notificationService.sendFCMandCreateNoti(
                            users[i].id,
                            FCM_ACTIONS_MESSAGE.YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED,
                            FCM_ACTIONS.YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED,
                            users[i].id
                        )
                    }
                }
            }
            return {status: users.length}
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
        }
    }

    async getUser(userId: String) {
        return this.exec(
            this.model.findOne({
                where: {
                    id: userId,
                },
            })
        )
    }

    async earningExp(
        type: String,
        user_id: String,
        transaction: any = undefined
    ) {
        const user = await this.exec(
            User.findOne({
                where: {
                    id: user_id,
                },
                attributes: ['id', 'level'],
            })
        )
        if (user) {
            const body: any = {
                user_id: user.id,
                category: HISTORY.USER.NAME,
                type_1: HISTORY.USER.TYPES.LEVEL.NAME,
                type_2: type,
            }
            const histories = await this.exec(
                History.findAll({
                    where: {
                        ...body,
                        created_at_unix_timestamp: {
                            $between: [
                                moment().startOf('day').valueOf(),
                                moment().endOf('day').valueOf(),
                            ],
                        },
                    },
                })
            )

            const pointList = await this.exec(
                Setting.findOne({
                    where: {
                        field: 'EARNING_POINT',
                    },
                    attributes: ['value_array_obj'],
                })
            )
            const levelList = await this.exec(
                Setting.findOne({
                    where: {
                        field: 'LEVEL_LIST',
                    },
                    attributes: ['value_array_obj'],
                })
            )

            const selectedType =
                pointList && pointList.value_array_obj.find((e: any) => e.id === type)

            if (selectedType && histories.length < selectedType.limit_per_day) {
                // increase exp
                let expPoint = await this.exec(
                    History.sum('value_1', {
                        where: {
                            user_id: user.id,
                            category: HISTORY.USER.NAME,
                            type_1: HISTORY.USER.TYPES.LEVEL.NAME,
                        },
                    })
                )
                if (!expPoint) {
                    expPoint = 0
                }
                const latestExp = parseInt(expPoint) + parseInt(selectedType.point)

                const latestLevel =
                    levelList &&
                    levelList.value_array_obj.find(
                        (e: any) =>
                            parseInt(e.start_exp) <= latestExp &&
                            latestExp <= parseInt(e.end_exp)
                    )

                await this.exec(
                    User.update(
                        {
                            exp: latestExp,
                            level: latestLevel ? latestLevel.id : user.level,
                        },
                        {
                            where: {
                                id: user.id,
                            },
                            transaction,
                        }
                    )
                )

                body.note = selectedType.point
                body.value_1 = parseInt(selectedType.point)

                await this.exec(History.create(body, transaction))
            } else {
                // throw errorService.database.queryFail("You have reached today limit",REACT_DAILY_LIMIT_ERR_CODE);
            }
        }
    }

    async getItem(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const data = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
        // const totalPost = await Post.count({where: {user_id: data.id}})
        // const totalReview = await Review.count({where: {user_id: data.id}})
        // data.dataValues.totalPost = totalPost
        // data.dataValues.totalReview = totalReview
        data.dataValues.current_on_event_shop = await Shop.count({
            where: {
                user_id: data.id,
                state: {
                    [Op.ne]: SHOP_STATE.EXPIRED
                }
            },
            include: [{
                association: "events",
                required: true,
                where: {
                    state: {
                        [Op.ne]: SHOP_STATE.EXPIRED
                    }
                }
            }]
        })
        return data
    }

    async updateNoticeMessengerStatus(
        params: any,
        option: ICrudOption = {scope: ['defaultScope']}
    ) {
        const {id} = option.filter
        const user = await this.exec(
            this.modelWithScope(option.scope).findOne({where: {id}}),
            {allowNull: false}
        )
        await user.update({
            notice_messenger_status: params.status,
        })
        return {notice_messenger_status: params.status}
    }

    async referralCode(user_id: string) {
        try {
            let referralCode = await this.exec(ReferralCode.findByPk(user_id))
            if (referralCode) return referralCode
            let code = makeRandomCode(6)
            while (true) {
                const exist = await this.exec(ReferralCode.findOne({where: {code}}))
                if (!exist) break
                code = makeRandomCode(6)
            }
            const body: any = {
                user_id,
                code,
            }
            referralCode = await ReferralCode.create(body)
            return referralCode
        } catch (err) {
            throw err
        }
    }

    async createWithReferral(params: any, option?: ICrudOption) {
        const POINT_REFERRAL = 500
        const transaction = await sequelize.transaction()
        option = {transaction}
        try {
            const {referral_code} = params
            const result = await this.create(params, {transaction})
            // JOIN_MEMBERSHIP
            await this.exec(rewardService.addReward(result.id, "JOIN_MEMBERSHIP", transaction));
            if (referral_code) {
                const referralCode = await this.exec(
                    ReferralCode.findOne({
                        where: {code: referral_code},
                    })
                )
                if (!referralCode)
                    throw errorService.database.queryFail('referral code is invalid', 404)
                // await pointService.changePoint(
                //     {
                //         user_id: referralCode.user_id,
                //         action: POINT_ACTION.INVITE,
                //         point: POINT_REFERRAL,
                //     },
                //     transaction
                // )
                // await pointService.changePoint(
                //     {
                //         user_id: result.id,
                //         action: POINT_ACTION.INVITE,
                //         point: POINT_REFERRAL,
                //     },
                //     transaction
                // )
                // addReward
                await this.exec(rewardService.addReward(referralCode.user_id, "INVITE_USER", transaction));
            }
            transaction.commit()
            return result
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async findUserByPhone(phone: string) {
        return await this.exec(
            this.model.findOne({
                attributes: ['id', 'phone', 'username', 'login_type'],
                where: {
                    phone,
                },
            }),
            {allowNull: false}
        )
    }

    async resetPassword(token: string, new_password: string) {
        try {
            const decodeToken = await firebaseService.verifyIdToken(token)
            const user = await this.findUserByPhone(decodeToken.phone_number)
            await this.model.update(
                {password: new_password},
                {
                    where: {id: user.id},
                }
            )
            return {
                success: true,
            }
        } catch (error) {
            throw error
        }
    }

    async countUser(group_id ?: string) {
        const countBizUser = await this.model.count({
            where: {
                account_type: USER_TYPE.BIZ_USER,
                ...(group_id ? {group_id} : {})
            },
        })

        const countFreeUser = await this.model.count({
            where: {
                account_type: USER_TYPE.FREE_USER,
                ...(group_id ? {group_id} : {})
            },
        })

        const countPaidUser = await this.model.count({
            where: {
                account_type: USER_TYPE.PAID_USER,
                ...(group_id ? {group_id} : {})
            },
        })
        const countAdmin = await Employee.count({
            where: {
                deleted_at: null,
            },
        })
        const totalUser = await this.exec(
            User.count({
                where: {
                    deleted_at: null
                },
            })
        )
        return {countBizUser, countFreeUser, countPaidUser, countAdmin, totalUser}
    }

    async sendOtp(phone: string) {
        return smsService.sendOtp(phone)
    }

    async verifyOtp(phone: string, otp: string) {
        const check = await smsService.verifyOtp(phone, otp)
        if (!check) {
            throw errorService.auth.codeError()
        }
        return check
    }

    async countActivity(user_id: string) {
        const countPost = await Post.count({where: {user_id}})
        const countReview = await Review.count(
            {
                where: {user_id}
            }
        )
        const countReservation = await Reservation.count(
            {
                where: {user_id}
            }
        )
        const countPointHistory = await PointHistory.count(
            {
                where: {user_id}
            }
        )
        const shops = await this.exec(Shop.findAll({where: {user_id}}))
        const countChat = await Conversation.count(
            {
                where: {
                    [Op.or]: [
                        {user_id},
                        {
                            shop_id: {
                                [Op.in]: shops.map((shop: any) => shop.id)
                            }
                        }
                    ]
                }
            }
        )
        return {
            countPost,
            countReview,
            countReservation,
            countPointHistory,
            countChat
        }
    }

    async updateAutoApproveStatus(
        params: any,
        option: ICrudOption = {scope: ['defaultScope']}
    ) {
        const {id} = option.filter
        const user = await this.exec(
            this.modelWithScope(option.scope).findOne({where: {id}}),
            {allowNull: false}
        )
        await user.update({
            auto_approve_reservation: params.status,
        })
        return {auto_approve_reservation: params.status}
    }

    async checkReferralCode(code: string) {
        const referralCode = await ReferralCode.findOne({where: {code}})
        return {
            available: !!referralCode,
        }
    }

    async updateReservationStatus(
        params: any,
        option: ICrudOption = {scope: ['defaultScope']}
    ) {
        const {id} = option.filter
        const user = await this.exec(
            this.modelWithScope(option.scope).findOne({where: {id}}),
            {allowNull: false}
        )
        await user.update({
            reservation_status: params.status,
        })
        return {reservation_status: params.status}
    }

    async checkUserByPhoneAndUsername(phone: string, username: string , secret_answer: string) {
        if (!phone && !username) {
            throw errorService.auth.unauthorized()
        }
        const user = await this.exec(this.model.findOne({
            where: {
                ...(phone ? {phone} : {}),
                ...(username ? {username} : {}),
                ...(secret_answer ? {secret_answer} : {})
            }
        }))
        return {
            available: !!user,
        }
    }

    async changePasswordWithPhoneAndUsername(params: any) {
        const {phone, username, new_password} = params
        const user = await this.exec(this.model.findOne({where: {phone, username}}))
        if (user) {
            await this.exec(
                User.update(
                    {
                        password: new_password,
                    },
                    {where: {id: user.id}}
                )
            )
            return user
        } else {
            throw errorService.auth.unauthorized()
        }
    }
    async reward(params: { user_id: string; exp?: number; point?: number },  option: ICrudOption = {scope: ['defaultScope']}) {
        const { user_id, exp = 0, point = 0 } = params
        
        return await sequelize.transaction(async (t) => {
          const user = await this.exec(
            User.findByPk(user_id, { transaction: t })
          )
          if (!user) throw errorService.database.recordNotFound()
    
          if (exp) user.exp = user.exp + exp
          if (point) user.point = user.point + point
          await user.save({ transaction: t })
          await actionLogService.create({user_id: user.id,
            action_type: 'REWARD_USER'}, option)
          return {
            user_id: user.id,
            exp,
            point,
          }
        })
      }
}
