import {CrudService, ICrudOption} from '../crudService.pg'
import {Site, SiteCategory} from '@/models/tables'
import {config} from '@/config'
import {Sequelize} from "@/models/base.pg";

export class SiteCategoryService extends CrudService<typeof SiteCategory> {
    constructor() {
        super(SiteCategory)
    }

    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async updateSiteCategory(params: any) {
        const {list_data} = params
        for (const data of list_data) {
            await SiteCategory.update({name: data.name}, {where: {id: data.id}})
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const {new_group} = params
        const item = await this.exec(this.getItem(option), {allowNull: false})
        if (new_group) {
            await Site.update({
                site_category_id: new_group
            }, {
                where: {
                    site_category_id: item.id
                }
            })
        }
        return await this.exec(item.destroy())
    }


    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const data =  await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        for (const item of data.rows) {
            item.dataValues.total_site = await Site.count({where: {site_category_id: item.id}})
        }
        return data
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }
}