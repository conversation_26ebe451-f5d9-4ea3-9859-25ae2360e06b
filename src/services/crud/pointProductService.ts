import {CrudService, ICrudOption} from '../crudService'
import {Notification, PointHistory, PointProduct, sequelize} from '@/models'
import {
  pointService,
  userService,
  pointProductHistoryService,
  errorService, firebaseService,
} from '@/services'
import {FCM_ACTIONS, FCM_ACTIONS_MESSAGE, getFcmActionMessage, POINT_ACTION} from '@/const'

export class PointProductService extends CrudService<typeof PointProduct> {
  constructor() {
    super(PointProduct)
  }

  async buyPointProduct(user_id: string, id: string) {
    const transaction = await sequelize.transaction()
    try {
      const [user, product] = await Promise.all([
        userService.getItem({
          filter: { id: user_id },
          transaction,
        }),
        this.getItem({ filter: { id }, transaction }),
      ])
      if (user.point < product.point)
        throw errorService.database.queryFail('Not Enough Point')

      const pointProductHistoryBody: any = {
        user_name: user.username,
        user_point: user.point,
        user_phone: user.phone,
        product: {
          id: product.id,
          name: product.title,
          point: product.point,
          image: product.images[0],
        },
      }
      const message = getFcmActionMessage(FCM_ACTIONS_MESSAGE.BUY_POINT_PRODUCT, {
        params_1: product.title,
      })
      const bodyNoti: any = {
        user_id: user_id,
        title: message,
        content: message,
        data: {
          message: message,
          params: {
            params_1: product.title,
          },
        },
        action: Number(FCM_ACTIONS.BUY_POINT_PRODUCT),
        interacting_type: 'BUY_POINT_PRODUCT',
        interacting_content_id: product.id,
      }
      await Promise.all([
        pointService.changePoint(
          {
            user_id,
            action: POINT_ACTION.BUY,
            point: -product.point,
            di_object: {
              id: product.id,
              name: product.title,
              image: product.images[0],
            },
          },
          transaction
        ),
        // firebaseService.sendNotification(
        //     user_id,
        //     message,
        //     FCM_ACTIONS.BUY_POINT_PRODUCT,
        //     product.id
        // ),
        this.exec(Notification.create(bodyNoti, {transaction})),
      ])

      await pointProductHistoryService.create(pointProductHistoryBody, {
        transaction,
      })
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  }

  async count(filter?:ICrudOption['filter']) {
    const total =  await this.exec(this.model.count({
      where: filter
    }))
    return {total}
  }
}
