import {CrudService, ICrudOption} from '../crudService.pg';
import {
    Category,
    Comment,
    DislikePost,
    FavouritePost,
    Notification,
    Post,
    Report,
    Review,
    User,
    ViewPost
} from '@/models/tables';
import {config} from '@/config';
import {sequelize} from '@/models';
import * as moment from 'moment';
import {errorService, firebaseService, reportService, rewardService, userService,} from '@/services';
import {FCM_ACTIONS, FCM_ACTIONS_MESSAGE, HISTORY} from '@/const';
import {isBase64EncodeStr} from '@/config/utils';
import {Op} from "sequelize";

const EXECUTE = {
    LIKE_POST: 'LIKE_POST',
    UNLIKE_POST: 'UNLIKE_POST',
    DISLIKE_POST: 'DISLIKE_POST',
    UNDISLIKE_POST: 'UNDISLIKE_POST',
};

export class PostService extends CrudService<typeof Post> {
    constructor() {
        super(Post);
    }

    async create(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction();
        try {
            if (params.images && params.images.length) {
                const filteredListImgs = params.images.filter((e: any) => !isBase64EncodeStr(e));
                params.images = filteredListImgs;
            }
            if (params.thumbnails && params.thumbnails.length) {
                const filteredListImgs = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
                params.thumbnails = filteredListImgs;
            }
            console.log('29148 tao post ne nha', HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_6.NAME, params.user_id);
            await userService.earningExp(HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_6.NAME, params.user_id, transaction);
            const item = await this.exec(
                this.model.create(params, {
                    transaction
                })
            );
            await rewardService.addReward(params.user_id, "WRITE_POST", transaction);
            await transaction.commit();
            const message = FCM_ACTIONS_MESSAGE.POST_DONE
            const action = FCM_ACTIONS.POST_DONE
            const bodyNoty: any = {
                user_id: params.user_id,
                title: message,
                content: message,
                data: {message: message},
                action: Number(action),
                interacting_type: 'POST_DONE',
                interacting_content_id: item.id,
            };
    
            try {
                await Notification.create(bodyNoty);
            } catch (err) {
                console.error(':', err);
            }
    
            // try {
            //     await firebaseService.sendNotification(
            //         params.user_id,
            //         message,
            //         action,
            //         item.id
            //     );
            // } catch (err) {
            //     console.error('Lỗi gửi FCM (không làm crash server):', err);
            // }
    
            return item;
        } catch (error) {
            await transaction.rollback();
            throw error; 
        }
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        });
        if (params.images && params.images.length) {
            const filteredListImgs = params.images.filter((e: any) => !isBase64EncodeStr(e));
            params.images = filteredListImgs;
        }
        if (params.thumbnails && params.thumbnails.length) {
            const filteredListImgs = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
            params.thumbnails = filteredListImgs;
        }
        await this.exec(
            Post.update(
                {
                    ...params,
                    created_at_unix_timestamp: moment().valueOf(),
                },
                {
                    where: {
                        id: item.id,
                    },
                }
            )
        );
        return await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
    }

    async getListPost(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const options = this.applyFindOptions({...option, filter: {...option.filter, status: true}});
        options.distinct = true;
        const postData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        );
        postData.total_rows = postData.count;

        for (let i = 0; i < postData.rows.length; i++) {
            if (params.user_id) {
                const like = await this.exec(
                    FavouritePost.findOne({
                        where: {
                            post_id: postData.rows[i].dataValues.id,
                            user_id: params.user_id,
                        },
                        attributes: ["id"],
                    })
                );
                const dislike = await this.exec(
                    DislikePost.findOne({
                        where: {
                            post_id: postData.rows[i].dataValues.id,
                            user_id: params.user_id,
                        },
                        attributes: ["id"],
                    })
                );
                const report = await this.exec(
                    Report.findOne({
                        where: {
                            post_id: postData.rows[i].dataValues.id,
                            user_id: params.user_id,
                        },
                        attributes: ["id"],
                    })
                );
                const foundViewPost = await this.exec(
                    ViewPost.findOne({
                        where: {
                            post_id: postData.rows[i].dataValues.id,
                            user_id: params.user_id,
                        },
                        attributes: ["id"]
                    })
                );
                postData.rows[i].dataValues.is_view = !!foundViewPost
                postData.rows[i].dataValues.is_like = !!like;
                postData.rows[i].dataValues.is_dislike = !!dislike;
                postData.rows[i].dataValues.is_report = !!report;
            } else {
                postData.rows[i].dataValues.is_like = false;
                postData.rows[i].dataValues.is_dislike = false;
                postData.rows[i].dataValues.is_report = false;
                postData.rows[i].dataValues.is_view = false;
            }
        }
        return postData;
    }

    onChangePostLikeStatus = async (
        status: string,
        user_id: string,
        post_id: string
    ) => {
        const transaction = await sequelize.transaction();
        try {
            const bodyLP: any = {user_id: user_id, post_id: post_id};

            const tempFind = await this.exec(
                FavouritePost.findOne({
                    where: bodyLP,
                    attributes: ["id"]
                })
            );
            if (status === EXECUTE.LIKE_POST && tempFind) {
                // call api like when the post is already liked
                throw errorService.database.queryFail(
                    'You have already liked this post!'
                );
            } else if (status === EXECUTE.UNLIKE_POST && !tempFind) {
                // call api unlike when the post not like
                throw errorService.database.queryFail(
                    'You have already unliked this post!'
                );
            }

            const post = await this.exec(this.model.findById(post_id), {
                allowNull: false,
            });

            if (status === EXECUTE.LIKE_POST) {
                // level up activity_3
                await userService.earningExp(HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_3.NAME, post.user_id, transaction);

                await this.exec(FavouritePost.create(bodyLP, {transaction}));
            } else {
                await this.exec(FavouritePost.destroy({
                    where: {
                        id: tempFind.id
                    },
                    transaction
                }));
            }

            const like_count = await this.exec(
                this.model.count({
                    where: {
                        id: post_id,
                    },
                })
            );

            await this.exec(
                this.model.update(
                    {
                        like: like_count,
                    },
                    {
                        where: {
                            id: post_id,
                        },
                        transaction: transaction,
                    }
                )
            );

            post.like = like_count;

            transaction.commit();
            return {
                post,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    };

    onChangePostDislikeStatus = async (
        status: string,
        user_id: string,
        post_id: string
    ) => {
        const transaction = await sequelize.transaction();
        try {
            const bodyDP: any = {user_id: user_id, post_id: post_id};

            const tempFind = await this.exec(
                DislikePost.findOne({
                    where: bodyDP,
                    attributes: ["id"]
                })
            );
            if (status === EXECUTE.DISLIKE_POST && tempFind) {
                // call api like when the post is already liked
                throw errorService.database.queryFail(
                    'You have already disliked this post!'
                );
            } else if (status === EXECUTE.UNDISLIKE_POST && !tempFind) {
                // call api unlike when the post not like
                throw errorService.database.queryFail(
                    'You have already unisliked this post!'
                );
            }

            if (status === EXECUTE.DISLIKE_POST) {
                await this.exec(DislikePost.create(bodyDP, {transaction}));
            } else {
                await this.exec(tempFind.destroy());
            }

            const post = await this.exec(this.model.findById(post_id), {
                allowNull: false,
            });

            const dislike_count =
                status === EXECUTE.DISLIKE_POST ? post.dislike + 1 : post.dislike - 1;
            post.dislike = dislike_count;

            await this.exec(
                this.model.update(
                    {
                        dislike: dislike_count,
                    },
                    {
                        where: {
                            id: post.id,
                        },
                        transaction: transaction,
                    }
                )
            );

            transaction.commit();
            return {
                post,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    };

    // task view post detail
    async onViewPost(params: any, option?: ICrudOption) {
        console.log("29148  ~ file: postService.ts ~ line 248 ~ PostService ~ onViewPost ~ params", params)
        console.log("29148  ~ file: postService.ts ~ line 248 ~ PostService ~ onViewPost ~ option", option)
        try {
            const post = await this.exec(Post.findOne(this.applyFindOptions(option)));
            console.log("29148  ~ file: postService.ts ~ line 250 ~ PostService ~ onViewPost ~ post", post)
            const foundLike = await this.exec(
                FavouritePost.findOne({
                    where: {
                        post_id: post.id,
                        user_id: params.user_id,
                    },
                    attributes: ["id"]
                })
            );
            const foundDislike = await this.exec(
                DislikePost.findOne({
                    where: {
                        post_id: post.id,
                        user_id: params.user_id,
                    },
                    attributes: ["id"]
                })
            );
            const foundReport = await this.exec(
                Report.findOne({
                    where: {
                        post_id: post.id,
                        user_id: params.user_id,
                    },
                    attributes: ["id"]
                })
            );
            // console.log("4444 ", post)
            post.dataValues.is_like = !!foundLike;
            post.dataValues.is_dislike = !!foundDislike;
            post.dataValues.is_report = !!foundReport;
            return post;
        } catch (e) {
            throw e;
        }
    }

    async viewPost(params: any, option?: ICrudOption) {
        const {user_id} = params;
        return await this.onViewPost(
            {
                user_id,
            },
            option
        );
    }

    async reportFunc(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id

        const transaction = await sequelize.transaction();
        try {
            // create report
            const num = await reportService.createReport(user_id, id, undefined, undefined, transaction);
            // level up activity_2
            await userService.earningExp(HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_2.NAME, user_id, transaction);

            transaction.commit();
            let item = await this.exec(this.model.findById(id));

            if (item) item.report = num
            else item = {id ,report: num};

            return item;
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async unreportFunc(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id

        const transaction = await sequelize.transaction();
        try {
            // remove report
            const num = await reportService.removeReport(user_id, id, undefined, undefined, transaction);

            transaction.commit();
            const item = await this.exec(this.model.findById(id), {
                allowNull: false,
            });

            item.report = num;

            return item;
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async dislikePost(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id
        return await this.onChangePostDislikeStatus(
            EXECUTE.DISLIKE_POST,
            user_id,
            id
        );
    }

    async undislikePost(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post_id
        return await this.onChangePostDislikeStatus(
            EXECUTE.UNDISLIKE_POST,
            user_id,
            id
        );
    }

    async likePost(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id
        return await this.onChangePostLikeStatus(EXECUTE.LIKE_POST, user_id, id);
    }

    async unlikePost(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post_id
        return await this.onChangePostLikeStatus(EXECUTE.UNLIKE_POST, user_id, id);
    }

    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params
        const options = this.applyFindOptions({...option, filter: {...option.filter, status: true}});
        // options.include = [
        //   ...options.include,
        //   {
        //     model: Comment,
        //     as: 'comments',
        //     where: { parent_id: null },
        //   },
        // ];

        const postData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        );
        if (user_id) {
            for (let i = 0; i < postData.rows.length; i++) {
                const foundViewPost = await this.exec(
                    ViewPost.findOne({
                        where: {
                            post_id: postData.rows[i].dataValues.id,
                            user_id
                        },
                        attributes: ["id"]
                    })
                );
                postData.rows[i].dataValues.is_view = !!foundViewPost
                // get 5 parent comments:
                // postData.rows[i].dataValues.comments = [
                //   ...postData.rows[i].dataValues.comments.slice(0, 5),
                // ];
                // // get 5 replies of these parent comments:
                // for (let j = 0; j < postData.rows[i].dataValues.comments.length; j++) {
                //   const comment = postData.rows[i].dataValues.comments[j].dataValues;
                //   const replies = await commentService.getDefaultRepliesComment(
                //     comment.id
                //   );
                //   comment.replies = replies;
                //   postData.rows[i].dataValues.comments[j].dataValues = comment;
                // }
            }
        }
        // for (let i = 0; i < postData.rows.length; i++) {
        //   // get 5 parent comments:
        //   postData.rows[i].dataValues.comments = [
        //     ...postData.rows[i].dataValues.comments.slice(0, 5),
        //   ];

        //   // get 5 replies of these parent comments:
        //   for (let j = 0; j < postData.rows[i].dataValues.comments.length; j++) {
        //     const comment = postData.rows[i].dataValues.comments[j].dataValues;
        //     const replies = await commentService.getDefaultRepliesComment(
        //       comment.id
        //     );
        //     comment.replies = replies;
        //     postData.rows[i].dataValues.comments[j].dataValues = comment;
        //   }
        // }
        return postData;
    }

    async delete(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction();
        try {
            const item = await this.exec(
                this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
                {allowNull: false}
            )

            if (item) {
                await this.exec(
                    Comment.destroy({
                        where: {
                            post_id: item.id,
                        },
                        transaction,
                    })
                );
                await this.exec(
                    Report.destroy({
                        where: {
                            post_id: item.id,
                        },
                        transaction,
                    })
                );
            }

            await this.exec(item.destroy());

            transaction.commit();

            return {
                item,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async createByAdmin(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction();
        try {
            if (params.images && params.images.length) {
                params.images = params.images.filter((e: any) => !isBase64EncodeStr(e));
            }
            if (params.thumbnails && params.thumbnails.length) {
                params.thumbnails = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
            }
            params.status = !params.execute_at;
            params.execute_at = params.execute_at ? new Date(params.execute_at) : null
            const item = await this.exec(
                this.model.create(params, {
                    transaction
                })
            );
            await transaction.commit();
            return item;
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async runCreate() {
        await this.model.update({status: true}, {
            where: {
                status: false,
                is_hide : false,
                execute_at: {
                    [Op.lte]: new Date()
                }
            }
        })
    }

    async getItem(
        params: any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params;
        const {id} = option.filter;
        if (option.attributes && option.attributes.includes('id')) {
            option.attributes.push('created_at_unix_timestamp')
        }
        const post = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
        if (user_id) {
            await this.exec(ViewPost.create({user_id: user_id, post_id: id} as any));
        }
        post.view++
        post.save()
        if (option.filter && option.filter.id) {
            delete option.filter.id
        }
        option.attributes && delete option.attributes
        const [nextPost, prevPost, foundLike, foundDislike, foundReport, topComment] = await Promise.all([
            Post.findOne(
                {
                    where: {
                        created_at: {
                            [Op.gte]: post.dataValues.created_at,
                        },
                        id: {
                            [Op.not]: post.id
                        },
                        status: true
                    },
                    order: [['created_at', 'ASC']]
                }
            ),
            Post.findOne(
                {
                    where: {
                        created_at: {
                            [Op.lte]: post.dataValues.created_at,
                        },
                        id: {
                            [Op.not]: post.id
                        },
                        status: true
                    },
                    order: [['created_at', 'DESC']]
                }
            ),
            FavouritePost.findOne({
                where: {
                    post_id: post.id,
                    user_id
                },
                attributes: ["id"]
            }),
            DislikePost.findOne({
                where: {
                    post_id: post.id,
                    user_id
                },
                attributes: ["id"]
            }),
            Report.findOne({
                where: {
                    post_id: post.id,
                    user_id
                },
                attributes: ["id"]
            }),
            Review.findOne({
                where: {
                    post_id: post.id,
                },
                include: [
                    {
                        association: 'user', required: false
                    }
                ],
                order: [["like", "DESC"]]
            })
        ])

        // const foundViewPost = await this.exec(
        //     ViewPost.findOne({
        //         where: {
        //             post_id: post.id,
        //         },
        //         attributes: ["id"]
        //     })
        // );
        // post.dataValues.is_view = !!foundViewPost
        post.dataValues.is_like = user_id ? !!foundLike : false;
        post.dataValues.is_dislike = user_id ? !!foundDislike : false;
        post.dataValues.is_report = user_id ? !!foundReport : false;
        post.dataValues.nextPost = nextPost || null;
        post.dataValues.prevPost = prevPost || null;
        post.dataValues.topComment = topComment || null;
        if (post.view === 501) {
            const message = FCM_ACTIONS_MESSAGE.FIVE_HUNDRED_VIEWS
            const bodyNoty: any = {
                user_id: post.user_id,
                title: message,
                content: message,
                data: {message: message},
                action: Number(FCM_ACTIONS.FIVE_HUNDRED_VIEWS),
                interacting_type: 'FIVE_HUNDRED_VIEWS',
                interacting_content_id: post.id,
            }
            await Promise.all([
                Notification.create(bodyNoty),
                // firebaseService.sendNotification(post.user_id, message, FCM_ACTIONS.FIVE_HUNDRED_VIEWS, post.id)
            ])
        }
        return post;
    }

    async getListByAdmin(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const options = this.applyFindOptions(option);
        return this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        );
    }

    //get List post by rank of view in ViewPost
    async rankPost(params: any) {
        const {startDate, endDate} = params;
        const listPost = await this.exec(
            ViewPost.findAll({
                where: {
                    created_at: {
                        [Op.between]: [startDate, endDate]
                    }
                },
                attributes: ['post_id', [sequelize.fn('COUNT', 'post_id'), 'view_count']],
                group: ['post_id', 'post.id'],
                include: [{
                    association: "post",
                    where: {
                        id: {
                            [Op.not]: null,
                        },
                        status: true
                    }
                }],
                order: [[sequelize.literal('view_count'), 'DESC']] as any,
                limit: 10
            })
        );
        for (const post of listPost) {
            post.dataValues.view_count = Number(post.dataValues.view_count)
            const user = await User.findOne({
                where: {
                    id: post.dataValues.post.user_id
                }
            })
            post.dataValues.post.dataValues.user = user
        }
        return listPost
    }

    async rankPostV2(params: any) {
        const {thema_id} = params;
        const startOfDay = moment().utc().add(9, 'hour').startOf('month').subtract(9, 'hour').toDate();
        const endOfDay = moment().utc().add(9, 'hour').endOf('days').subtract(9, 'hour').toDate();
        const category = await Category.findAll({
            where: {
                thema_id
            }
        })
        let listPost
        for (let i = 0; i < 5; i++) {
            listPost = await ViewPost.findAll({
                    where: {
                        created_at: {
                            [Op.between]: [
                                moment(startOfDay).subtract(i, 'days').toDate(),
                                moment(endOfDay).subtract(i, 'days').toDate()
                            ]
                        },
                    },
                    attributes: ['post_id', [sequelize.fn('COUNT', 'post_id'), 'like_count']],
                    group: ['post_id', 'post.id'],
                    include: [{
                        association: "post",
                        where: {
                            id: {
                                [Op.not]: null
                            },
                            status: true,
                            ...(thema_id ? {
                                category_id: {[Op.in]: category.map(i => i.id)}
                            } : {})
                        },
                    }],
                    order: [[sequelize.literal('like_count'), 'DESC']] as any,
                    limit: 5
                }
            )
            if (listPost && listPost.length > 0) {
                break;
            }
        }
        if (!listPost || listPost.length < 5) {
           let listPost2 = await this.model.findAll({
                where: {
                    ...(thema_id ? {
                        category_id: {[Op.in]: category.map(i => i.id)}
                    } : {}),
                    ...(listPost.length ? {
                        id: {
                            [Op.notIn]: listPost.map(i => i.post_id)
                        },
                    } : {
                        id: {
                            [Op.not]: null
                        },
                    }),
                    status: true
                },
                    order: [['view', 'DESC']] as any,
                    limit: (5 -listPost.length)
                }
            )
            listPost2 = listPost2.map(i => ({dataValues: {post_id : i.id , like_count : i.view, post: i}}))
            listPost = listPost.concat(listPost2)
        }
        for (const post of listPost) {
            post.dataValues.like_count = Number(post.dataValues.like_count)
            const user = await User.findOne({
                where: {
                    id: post.dataValues.post.user_id
                }
            })
            post.dataValues.post.dataValues.user = user
        }
        return listPost.map(i =>i.dataValues)
    }

    async scheduleCongratulation() {
        const startOfWeek = moment().utc().add(9, 'hour').startOf('week').subtract(9, 'hour').toDate();
        const endOfWeek = moment().utc().add(9, 'hour').endOf('week').subtract(9, 'hour').toDate();
        const listPost = await FavouritePost.findAll({
            where: {
                created_at: {
                    [Op.between]: [startOfWeek, endOfWeek]
                }
            },
            attributes: ['post_id', [sequelize.fn('COUNT', 'post_id'), 'like_count']],
            group: ['post_id', 'post.id'],
            include: [{
                association: "post",
                where: {
                    id: {
                        [Op.not]: null
                    }
                },
            }],
            order: [[sequelize.literal('like_count'), 'DESC']] as any,
            limit: 1
        })
        const [post] = listPost
        if (post) {
            const message = FCM_ACTIONS_MESSAGE.CONGRATULATION_POST
            const bodyNoty: any = {
                user_id: post.dataValues.post.user_id,
                title: message,
                content: message,
                data: {message: message},
                action: Number(FCM_ACTIONS.CONGRATULATION_POST),
                interacting_type: 'CONGRATULATION_POST',
                interacting_content_id: post.dataValues.post.id,
            }
            await Promise.all([
                Notification.create(bodyNoty),
                // firebaseService.sendNotification(post.dataValues.post.user_id, message, FCM_ACTIONS.CONGRATULATION_POST, post.dataValues.post.id)
            ])

        }

    }

    async deleteAll(option?: ICrudOption) {
        const t = await this.transaction()
        option.transaction = t
        try {
            await this.exec(
                Comment.destroy({
                    where: {
                        post_id: option.filter.id,
                    },
                    transaction:t,
                })
            );
            await this.exec(
                Report.destroy({
                    where: {
                        post_id: option.filter.id,
                    },
                    transaction:t,
                })
            );
            const result = await this.exec(
                this.model.destroy(this.applyDestroyOptions(option))
            )

            t.commit()
            return result
        } catch (err) {
            t.rollback()
            throw err
        }
    }

    async countByUser(user_id: string) {
        const totalPost =  await this.model.count({
            where: {
                user_id
            }
        })
        return {
            totalPost
        }

    }
}
