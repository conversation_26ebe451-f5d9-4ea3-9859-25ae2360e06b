import {CrudService, ICrudOption} from '../crudService.pg';
import {Category, Favourite, RecentReading, Recruit, Review, ShopTag, Thema, User} from '@/models/tables';
import {sequelize} from '@/models';
import * as moment from 'moment';

import {tokenService, firebaseService, errorService, settingService} from '@/services';
import {REVIEW_TYPE, SHOP_STATE} from '@/const';
import {DESCRIPTION_CANNOT_BE_EMPTY, LIKE_STATUS} from './shopService';
import {config} from '@/config';
import {isBase64EncodeStr, removeBase64EncodeStr} from '@/config/utils';

const RECRUIT_ERR_CODE = 500;
const RECRUIT_WAS_DELETED_ERR_CODE = 500 + 1;

export class RecruitService extends CrudService<typeof Recruit> {
    constructor() {
        super(Recruit);
    }

    updateBonusRecruitInfo = (recruit: any, params: any) => {
        const body = {...params};
        if (recruit && recruit.old_post) {
            body.old_post = {...recruit.old_post, ...params};
        }
        if (recruit && recruit.denied_post) {
            body.denied_post = {...recruit.denied_post, ...params};
        }
        return body;
    }

    async getRelate(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {
            order_option,
            initial_id
        } = params;

        option.limit = undefined;

        const initialItem = await this.exec(this.model.findOne({
            where: {
                id: initial_id
            },
            attributes: ["category_id", "state"],
        }));

        const category = await this.exec(Category.findOne({
            where: {
                id: initialItem.category_id
            },
            attributes: ["id"],
        }))

        if (category) {
            option.filter = {
                category_id: category.id,
            };

            option.order = [['created_at', 'DESC']];

            option.attributes = ["id"];
            const options: any = this.applyFindOptions(option);

            if (initialItem.state === SHOP_STATE.APPROVED) {
                options.where = {
                    ...options.where,
                    state: SHOP_STATE.APPROVED,
                }

                const reponseData = await this.exec(
                    this.modelWithScope(option.scope).findAndCountAll(options)
                );

                const itemIdIndex = reponseData.rows.findIndex((e: any, i: any) => e.dataValues.id === initial_id)

                if (reponseData.rows.length > 0) {
                    const boundaryPos = 50;
                    const startIndex = itemIdIndex > boundaryPos ? itemIdIndex - boundaryPos : 0;
                    const endIndex = itemIdIndex > boundaryPos ? itemIdIndex + boundaryPos : boundaryPos;
                    const finalList = [...reponseData.rows.slice(startIndex, endIndex + 1)];
                    reponseData.rows = finalList.map(e => e && e.dataValues && e.dataValues.id);
                } else {
                    reponseData.rows = [];
                }
                // reponseData.location = await settingService.getLocationSetting();
                return reponseData;
            } else {
                const reponseData: any = {
                    rows: [initial_id]
                }
                // reponseData.location = await settingService.getLocationSetting();
                return reponseData;
            }
        } else {
            throw errorService.database.queryFail("recruit was deleted.", RECRUIT_WAS_DELETED_ERR_CODE);
        }
    }

    async getNear(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {
            order_option,
            initial_id,
            categories,
            tags
        } = params;

        let reponseData: any = {};

        option.order = [['created_at', 'DESC']];

        option.limit = undefined;

        if (categories && categories.length || tags && tags.length) {
//
            option.filter = {
                $and: [
                    categories && categories.length ? {
                        $or: categories.map((category_id: any) => ({category_id: category_id}))
                    } : {},
                    tags && tags.length ? {
                        tag_ids: {
                            $contains: tags.map((tag_id: any) => (tag_id))
                        }
                    } : {},
                    {
                        $or: [
                            {
                                state: SHOP_STATE.APPROVED
                            },
                        ]
                    }
                ]
            }

            option.include = option.include.map((e: any) => ({...e, attributes: ["id"]}))

            const options: any = this.applyFindOptions(option);

            reponseData = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll({
                    ...options,
                    attributes: ["id"]
                })
            );

            const recruitIdIndex = reponseData.rows.findIndex((e: any, i: any) => e.dataValues.id === initial_id)

            if (reponseData.rows.length > 0) {
                const boundaryPos = 50;
                const startIndex = recruitIdIndex > boundaryPos ? recruitIdIndex - boundaryPos : 0;
                const endIndex = recruitIdIndex > boundaryPos ? recruitIdIndex + boundaryPos : boundaryPos;
                const finalList = [...reponseData.rows.slice(startIndex, endIndex + 1)];
                reponseData.rows = finalList.map(e => e && e.dataValues && e.dataValues.id);
            } else {
                reponseData.rows = [];
            }

        } else {
            //
            reponseData.count = 1;
            reponseData.rows = [initial_id]
        }
        return reponseData;
    }

    async view(
        params: any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const user_id = params.user_id;
        const recruit_id = option.filter && option.filter.id;

        const item = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        );
        if (user_id && recruit_id) {
            // add to recent reading list:
            await this.exec(
                RecentReading.destroy({
                    where: {
                        recruit_id,
                        user_id,
                    },
                })
            );
            const bodyRR: any = {
                user_id,
                recruit_id,
                type: REVIEW_TYPE.RECRUIT,
            };
            await this.exec(RecentReading.create(bodyRR));

            // check like shop:
            const like = await this.exec(
                Favourite.findOne({
                    where: {
                        recruit_id,
                        user_id,
                    },
                    attributes: ["id"]
                })
            );
            item.dataValues.is_like = !!like;
        } else {
            item.dataValues.is_like = false;
        }
        return item;
    }

    async create(params: any, option?: ICrudOption) {

        const {tag_ids, account_type, user_role, user_id, category_id} = params;
        console.log("29148  ~ file: recruitService.ts ~ line 15 ~ RecruitService ~ create ~ user_id", user_id, tag_ids)
        const transaction = await sequelize.transaction();
        try {
            if (params.description === '') {
                throw errorService.database.queryFail(
                    'description cannot be empty',
                    DESCRIPTION_CANNOT_BE_EMPTY
                );
            }
            if (params.images && params.images.length) {
                const filteredListImgs = params.images.filter((e: any) => !isBase64EncodeStr(e));
                params.images = filteredListImgs;
            }
            if (params.thumbnails && params.thumbnails.length) {
                const filteredListImgs = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
                params.thumbnails = filteredListImgs;
            }
            // check and remove base64 image from description:
            params.description = removeBase64EncodeStr(params.description);


            const recruitInfo: any = {
                ...params,
                verified: false,
                comment: 0,
                view: 0,
                like: 0,
                short_address: '',
                state: SHOP_STATE.APPROVED,
                created_by_admin: !!!params.user_id,
                user_id: params.user_id,
            };

            if (params.category_id) {
                const categoryObj = await this.exec(Category.findOne({
                    where: {
                        id: params.category_id
                    },
                    attributes: ["thema_id"],
                }))
                if (categoryObj) {
                    const themaObj = await this.exec(Thema.findOne({
                        where: {
                            id: categoryObj.thema_id
                        },
                        attributes: ["id"]
                    }))
                    recruitInfo.thema_id = themaObj.id;
                }
            }

            recruitInfo.start_date = moment().valueOf();
            recruitInfo.expired_date = moment().valueOf() + 86400000 * 30;
            ; // set new

            if (params.latitude && params.longitude) {
                recruitInfo.latitude = params.latitude;
                recruitInfo.longitude = params.longitude;
            } else {
                throw errorService.database.queryFail(
                    '검색한 상점주소가 확인되지 않습니다. 정확한 주소정보를 입력해주세요'
                );
            }

            const recruit = await this.exec(
                Recruit.create(recruitInfo, {
                    transaction,
                })
            );

            if (tag_ids) {
                for (let i = 0; i < tag_ids.length; i++) {
                    const bodyRT: any = {
                        recruit_id: recruit.id,
                        tag_id: tag_ids[i],
                    };
                    await this.exec(
                        ShopTag.create(bodyRT, {
                            transaction,
                        })
                    );
                }
            }
            transaction.commit();
            if (recruit.created_at_unix_timestamp) {
                console.log('#@$% ', recruit.created_at_unix_timestamp);
                await this.exec(
                    recruit.update(
                        {
                            created_at_unix_timestamp: recruit.created_at_unix_timestamp,
                        },
                        {
                            where: {
                                id: recruit.id,
                            },
                        }
                    )
                );
            }
            return {
                recruit,
            };
        } catch (e) {
            transaction.rollback();
            throw e;
        }
    }

    async update(params: any, option?: ICrudOption) {
        const {tag_ids, user_id, category_id} = params;
        console.log("29148  ~ file: recruitService.ts ~ line 15 ~ RecruitService ~ create ~ user_id", user_id, tag_ids)
        const transaction = await sequelize.transaction();

        try {
            if (params.description === '') {
                throw errorService.database.queryFail(
                    'description cannot be empty',
                    DESCRIPTION_CANNOT_BE_EMPTY
                );
            }
            if (params.images && params.images.length) {
                const filteredListImgs = params.images.filter((e: any) => !isBase64EncodeStr(e));
                params.images = filteredListImgs;
            }
            if (params.thumbnails && params.thumbnails.length) {
                const filteredListImgs = params.thumbnails.filter((e: any) => !isBase64EncodeStr(e));
                params.thumbnails = filteredListImgs;
            }
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            });

            // check and remove base64 image from description:
            params.description = removeBase64EncodeStr(params.description);
            // if (item.old_post) {
            //   params.old_post.description = removeBase64EncodeStr(item.old_post.description);
            // }
            // if (item.denied_post) {
            //   params.denied_post.description = removeBase64EncodeStr(item.denied_post.description);
            // }

            if (tag_ids) {
                await this.exec(
                    ShopTag.destroy({
                        where: {
                            recruit_id: item.id,
                        },
                        transaction: transaction,
                    })
                );

                for (let i = 0; i < tag_ids.length; i++) {
                    const bodyRT: any = {
                        recruit_id: item.id,
                        tag_id: tag_ids[i],
                    };
                    await this.exec(
                        ShopTag.create(bodyRT, {
                            transaction,
                        })
                    );
                }
            }

            if (params.category_id !== item.category_id) {
                const categoryObj = await this.exec(Category.findOne({
                    where: {
                        id: params.category_id
                    },
                    attributes: ["thema_id"],
                }))
                if (categoryObj) {
                    const themaObj = await this.exec(Thema.findOne({
                        where: {
                            id: categoryObj.thema_id
                        },
                        attributes: ["id"],
                    }))
                    item.dataValues.thema_id = themaObj.id;
                }
            }

            transaction.commit();
            await this.exec(item.update(params));
            return await this.getItem(option);
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async deleteAll(option?: ICrudOption) {
        const t = await this.transaction();
        option.transaction = t;
        try {
            const result = await this.exec(
                this.model.destroy(this.applyDestroyOptions(option))
            );
            t.commit();
            return result;
        } catch (err) {
            t.rollback();
            throw err;
        }
    }

    async getListRecruit(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {
            user_id,
        } = params;

        const options = this.applyFindOptions(option);

        options.distinct = true;

        const listData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        );
        listData.total_rows = listData.count;

        for (let i = 0; i < listData.rows.length; i++) {
            const [like, view] = await Promise.all([
                this.exec(
                    Favourite.findOne({
                        where: {
                            recruit_id: listData.rows[i].dataValues.id,
                            user_id: user_id,
                        },
                        attributes: ["id"]
                    })
                ),
                this.exec(
                    RecentReading.findOne({
                        where: {
                            recruit_id: listData.rows[i].dataValues.id,
                            user_id: user_id,
                        },
                        attributes: ["id"]
                    })
                )
            ])
            listData.rows[i].dataValues.is_like = !!like;
            listData.rows[i].dataValues.is_view = !!view;
        }

        listData.location = await settingService.getLocationSetting();
        return listData;
    }

    async likeRecruit(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id
        console.log("29148  ~ file: recruitService.ts ~ line 185 ~ RecruitService ~ likeRecruit ~ id", id)
        return await this.onChangePostLikeStatus(LIKE_STATUS.LIKE_POST, user_id, id);
    }

    async unlikeRecruit(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post_id
        return await this.onChangePostLikeStatus(LIKE_STATUS.UNLIKE_POST, user_id, id);
    }

    onChangePostLikeStatus = async (
        status: string,
        user_id: string,
        recruit_id: string
    ) => {
        const transaction = await sequelize.transaction();
        try {
            const bodyFS: any = {user_id: user_id, recruit_id: recruit_id};
            console.log("29148  ~ file: recruitService.ts ~ line 203 ~ RecruitService ~ bodyFS", bodyFS)

            const tempFind = await this.exec(
                Favourite.findOne({
                    where: bodyFS,
                    attributes: ["id"],
                    transaction,
                })
            );

            if (status === LIKE_STATUS.LIKE_POST && tempFind) {
                // call api like when the post is already liked
                throw errorService.database.queryFail(
                    'You have already liked this recruit profile!'
                );
            } else if (status === LIKE_STATUS.UNLIKE_POST && !tempFind) {
                // call api unlike when the post not like
                throw errorService.database.queryFail(
                    'You have already unliked this recruit profile!'
                );
            }

            if (status === LIKE_STATUS.LIKE_POST) {
                bodyFS.type = REVIEW_TYPE.RECRUIT;
                await this.exec(Favourite.create(bodyFS, {transaction}));
            } else {
                await this.exec(Favourite.destroy({
                    where: {
                        id: tempFind.id
                    },
                    transaction
                }));
            }

            const like_count = await this.exec(Favourite.count({
                where: {
                    recruit_id: recruit_id
                },
                transaction
            }));

            await this.exec(
                this.model.update(
                    {
                        like: like_count,
                    },
                    {
                        where: {
                            id: recruit_id,
                        },
                        transaction: transaction,
                    }
                )
            );

            transaction.commit();
            const recruit_profile = await this.exec(Recruit.findOne({
                where: {
                    id: recruit_id
                },
            }))

            return {
                recruit: recruit_profile,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    };
}
