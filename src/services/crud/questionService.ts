import { CrudService, ICrudOption } from '../crudService.pg'
import { Question, AnswerQuestion, sequelize, Sequelize } from '@/models'
import { QUESTION_STATUS } from '@/const'
import { config } from '@/config'
import { utilService } from '@/services'

const { Op } = sequelize
export class QuestionService extends CrudService<typeof Question> {
  constructor() {
    super(Question)
  }

  async answerQuestion(params: any, option?: ICrudOption) {
    const { id } = option.filter
    params.question_id = id
    delete params.status
    const transaction = await sequelize.transaction()
    try {
      const [question, answerExist] = await Promise.all([
        this.exec(Question.findByPk(id), {
          allowNull: false,
        }),
        this.exec(
          AnswerQuestion.findOne({
            where: { question_id: id, parent_id: { $eq: null } },
            raw: true,
          })
        ),
      ])

      if (answerExist) {
        params.parent_id = answerExist.id
      }

      if (
        params.role !== 'SUPERADMIN' &&
        params.role !== 'ADMIN' &&
        params.role !== 'OPERATOR'
      ) {
        await question.update({ status: QUESTION_STATUS.MORE }, { transaction })
      } else {
        await Question.update(
          { status: QUESTION_STATUS.COMPLETED },
          { where: { id: id }, transaction }
        )
      }
      await AnswerQuestion.create(params, { transaction })
      transaction.commit()
      return {
        is_success: true,
      }
    } catch (err) {
      transaction.rollback()
      throw err
    }
  }

  async getList(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    if (
      // params.role !== 'SUPERADMIN' &&
      params.role !== 'ADMIN' &&
      params.role !== 'OPERATOR'
    ) {
      option.order = [
        utilService.customOrder(
          'tbl_question.status',
          [
            QUESTION_STATUS.PENDING,
            QUESTION_STATUS.MORE,
            QUESTION_STATUS.COMPLETED,
          ],
          'ASC'
        ),
        ...option.order,
      ]
    }

    const res = await this.exec(
      this.modelWithScope(option.scope).findAndCountAll({
        ...this.applyFindOptions(option),
        logging: true,
      })
    )
    return res
  }

  async adminGetList(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    const { offset, limit } = this.applyFindOptions(option)

    const rowsProgress = this.exec(
      this.model.findAll({
        attributes: {
          include: [
            [Sequelize.literal(`COUNT(answer_question.id)`), 'unread_count'],
          ],
        },
        include: [
          {
            association: 'user',
            required: false,
          },
        ],
        group: ['tbl_question.id', 'user.id'],
        subQuery: false,
        offset,
        limit,
      })
    )
    const countProgress = this.exec(this.model.count())
    const [rows, count] = await Promise.all([rowsProgress, countProgress])
    return { rows, count }
  }

  async countUnread() {
    {
      const count = await this.exec(
        this.model.count({
          where: {
            status: {
              [Op.or]: [QUESTION_STATUS.PENDING, QUESTION_STATUS.MORE],
            },
          },
        })
      )
      return { count }
    }
  }
}
