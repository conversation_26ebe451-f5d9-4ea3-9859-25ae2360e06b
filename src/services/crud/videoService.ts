import { config } from '@/config';
import { CrudService, ICrudOption } from '../crudService.pg'
import { Video } from '@/models/tables'

export class VideoService extends CrudService<typeof Video> {
    constructor() {
        super(Video)
    }
 async getList(
  params: any, 
  option: ICrudOption = {
    limit: config.database.defaultPageSize,
    offset: 0,
    scope: ['defaultScope'],
    filter: {}
  }
) {
  // Merge shop_id vào option.filter để applyFindOptions nhận được
  option.filter = {
    ...option.filter,
    ...(params.shop_id ? { shop_id: params.shop_id } : {}),
  };

  const findOptions = this.applyFindOptions(option);
console.log('findOptions', findOptions);


  const data = await this.exec(
    this.modelWithScope(option.scope).findAndCountAll(findOptions)
  );
console.log('data',data);

  return data;
}

}