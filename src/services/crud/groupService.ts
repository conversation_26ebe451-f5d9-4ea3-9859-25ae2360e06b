import {CrudService, ICrudOption} from '../crudService.pg'
import {Group, User} from '@/models/tables'
import {config} from "@/config";
import {Sequelize} from "@/models/base.pg";

export class GroupService extends CrudService<typeof Group> {
    constructor() {
        super(Group)
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        option.order = [['index', 'ASC']]
        const data = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        const totalUser = await this.exec(
            User.count({
                where: {
                    deleted_at: null
                },
            })
        )
        for (const group of data.rows) {
            const numberUser = await this.exec(
                User.count({
                    where: {
                        group_id: group.id,
                    },
                })
            );
            group.dataValues.numberUser = numberUser
        }
        data.totalUser = totalUser
        return data
    }

    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }


    async delete(params: any, option?: ICrudOption) {
        const {new_group} = params
        const item = await this.exec(this.getItem(option), {allowNull: false})
        if (new_group) {
            await User.update({group_id: new_group}, {
                where: {
                    group_id: item.id
                }
            })
        }
        return await this.exec(item.destroy())
    }
}
