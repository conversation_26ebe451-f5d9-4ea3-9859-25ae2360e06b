import { CrudService, ICrudOption } from '../crudService.pg';
import { Keyword } from '@/models/tables';
import * as moment from "moment";

const fs = require('fs')
const xlsx = require('node-xlsx')
const AWS = require('aws-sdk')
import axios from 'axios'

const FILE_IMAGE_PATH = 'image/'
const KEY_CLOUDIMAGE = 'ce8391bac'
const ID = process.env.AWS_ID
const SECRET = process.env.AWS_SECRET_KEY
const BUCKET_NAME = process.env.AWS_BUCKET_NAME

import { tokenService, firebaseService, errorService, linkService, smsService } from '@/services';
import { Sequelize } from "@/models/base.pg";
import { config } from "@/config";
import { BOARD_ROUTE } from "@/const";
import { Op } from "sequelize";

export class KeywordService extends CrudService<typeof Keyword> {
    constructor() {
        super(Keyword);
    }

    async importExcel(params: any, query: any) {
        const {keyword_category_id} = query
        const data = await xlsx.parse(params.url)
        const dataExcel = data[0].data
        const [listKey, ...rest] = dataExcel
        const dataImport: Record<string, any> = []
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        const K_Index = lastItem ? Number(lastItem.dataValues.index) : 0
        for (let i = 0; i < rest.length; i++) {
            const obj: Record<string, any> = {}
            for (const [index, key] of listKey.entries()) {
                obj[key] = rest[i][index]
            }
            obj['keyword_category_id'] = keyword_category_id
            obj['index'] = K_Index + 1024 * (i + 1)
            dataImport.push(obj)
        }
        fs.unlinkSync(params.url)
        const t = await this.transaction()
        const dataImportFilter = dataImport.filter((item: any) => item.name)
        try {
            const created = await Keyword.bulkCreate(dataImportFilter as any, {transaction: t})
            await t.commit()
            return created
        } catch (e) {
            await t.rollback()
        }
    }

    async downloadExcel(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        try {
            const result = await this.exec(
                this.modelWithScope(option.scope).findAll(
                    {
                        include: [
                            {
                                association: 'keyword_category',
                                where : {
                                    id : {
                                        [Op.not] : null
                                    }
                                },
                                include: [
                                    {
                                        association: 'province'
                                    },
                                    {
                                        association: 'district'
                                    },
                                    {
                                        association: 'keyword_type',
                                        where : {
                                            id : {
                                                [Op.not] : null
                                            }
                                        },
                                        include: [
                                            {
                                                association: 'category',
                                                include: [
                                                    {
                                                        association: 'thema'
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ],
                        limit : 800
                    }
                )
            )
            const s3 = new AWS.S3({
                accessKeyId: ID,
                secretAccessKey: SECRET,
            })
            const header = [
                'id',
                'name',
                'type',
                'category',
                'keyword_category_id',
                'thema_id',
                'category_id',
                'province',
                'district',
                'url'
            ]
            const data = [header]
            for (let i = 0; i < result.length; i++) {
                const keyword = result[i]
                const thema_id = keyword.keyword_category.keyword_type.category.thema_id
                const province = keyword.keyword_category.province
                const district = keyword.keyword_category.district
                const category_id = keyword.keyword_category.keyword_type.category_id
                const type = keyword.keyword_category.keyword_type.name
                const category = keyword.keyword_category.name
                const link = await linkService.model.findOne({
                    where: {
                        thema_id
                    }
                })
                let href = ''
                if (link) {
                    href = BOARD_ROUTE[link.route]
                        ? `${BOARD_ROUTE[link.route]}/${link.thema_id}`
                        : ''
                    if (category_id) {
                        href += `?categories=${category_id}`
                    }
                    if (province && province.name) {
                        href += `&province=${province.name}`
                    }
                    if (district && district.name) {
                        href += `&district=${district.name}`
                    }
                    href += `&keyword=${keyword.name}`
                }
                const rowData = [
                    keyword.id ? keyword.id : '',
                    keyword.name ? keyword.name : '',
                    type,
                    category,
                    keyword.keyword_category_id ? keyword.keyword_category_id : '',
                    thema_id,
                    category_id,
                    province && province.name ? province.name : '',
                    district && district.name ? district.name : '',
                    `${process.env.WEB_URL}${href}`
                ]
                data.push(rowData)
            }
            const fileName = 'keyword' + moment().valueOf() + '.xlsx'
            const buffer = xlsx.build([{name: 'data', data: data}]) // Returns a buffer
            fs.writeFileSync(FILE_IMAGE_PATH + fileName, buffer)
            const fileContent = fs.readFileSync(FILE_IMAGE_PATH + fileName)

            const awsParams = {
                Bucket: BUCKET_NAME,
                Key: fileName, // File name you want to save as in S3
                Body: fileContent,
                ACL: 'public-read',
            }
            // Uploading files to the bucket
            await s3.upload(awsParams, (err: any, data: any) => {
                if (err) {
                    throw err
                }
            })
            const urls3High = `https://${process.env.IMAGE_URL}/${fileName}`

            fs.unlinkSync(FILE_IMAGE_PATH + fileName)
            return {url: urls3High, file_name: fileName}
        } catch (error) {
            console.log(
                'error',
                error
            )
            throw error
        }
    }

    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }

     delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

    async Indexing() {
        const url = `https://ga.kormsg.com/index?domain=${process.env.SEO}`
        const keywords = await this.model.findAll({
            where: {
                indexing_status: false
            },
            include: [
                {
                    association: 'keyword_category',
                    include: [
                        {
                            association: 'province'
                        },
                        {
                            association: 'district'
                        },
                        {
                            association: 'keyword_type',
                            include: [
                                {
                                    association: 'category',
                                    include: [
                                        {
                                            association: 'thema'
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            limit: 200
        })
        const results : string[] = []
        for (const keyword of keywords) {
            console.log('start',keyword.name)
            await this.delay(2000)
            try {
                const thema_id = keyword.keyword_category.keyword_type.category.thema_id
                const province = keyword.keyword_category.province
                const district = keyword.keyword_category.district
                const category_id = keyword.keyword_category.keyword_type.category_id
                const link = await linkService.model.findOne({
                    where: {
                        thema_id
                    }
                })
                if (link) {
                    let href = BOARD_ROUTE[link.route]
                        ? `${BOARD_ROUTE[link.route]}/${link.thema_id}`
                        : ''
                    if (category_id) {
                        href += `?categories=${category_id}`
                    }
                    if (province) {
                        href += `&province=${province.name}`
                    }
                    if (district) {
                        href += `&district=${district.name}`
                    }
                    href += `&keyword=${keyword.name}`
                    try {
                        const {data} = await axios.post(url, {
                            url: `${process.env.WEB_URL}${href}`
                        })
                        if (data) {
                            await this.model.update({indexing_status: true}, {
                                where: {
                                    id: keyword.id
                                }
                            })
                            results.push(`${process.env.WEB_URL}${href}`)
                        }
                        console.log('done',keyword.name)
                    } catch (e) {
                    }
                }
            } catch (e) {

            }
        }

        const header = [
            'url'
        ]
        const data = [header]
        for (const result of results) {
            data.push([result])
        }
        const fileName = 'result' + moment().valueOf()  + '.xlsx'
        const buffer = xlsx.build([{name: 'data', data: data}]) // Returns a buffer
        fs.writeFileSync(FILE_IMAGE_PATH + fileName, buffer)
        const fileContent = fs.readFileSync(FILE_IMAGE_PATH + fileName)

        const awsParams = {
            Bucket: BUCKET_NAME,
            Key: fileName, // File name you want to save as in S3
            Body: fileContent,
            ACL: 'public-read',
        }
        const s3 = new AWS.S3({
            accessKeyId: ID,
            secretAccessKey: SECRET,
        })
        // Uploading files to the bucket
        await s3.upload(awsParams, (err: any, data: any) => {
            if (err) {
                throw err
            }
        })
        const urls3High = `https://${process.env.IMAGE_URL}/${fileName}`
        fs.unlinkSync(FILE_IMAGE_PATH + fileName)
        await smsService.sendMessageToBoss(`Indexing done ${urls3High} in day ${moment().format('YYYY-MM-DD')}`)
    }
}
