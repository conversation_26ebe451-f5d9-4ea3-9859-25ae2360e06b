import {Op} from 'sequelize'

import {CrudService, ICrudOption} from '../crudService'
import {errorService, reportService, userService} from '@/services'
import {Conversation, Message, Shop, sequelize} from '@/models'
import {config} from '@/config'
import {HISTORY} from "@/const";

export class ConversationService extends CrudService<typeof Conversation> {
    constructor() {
        super(Conversation)
    }

    async findOrCreate(params: any, option?: ICrudOption) {
        const {user_id = null, shop_id = null, user_id_2 = null, shop_id_2 = null} = params
        const [conversation, created] = await this.exec(
            this.model.findOrCreate({
                where: {
                    [Op.or]: [
                        {user_id, user_id_2, shop_id, shop_id_2},
                        {user_id, user_id_2, shop_id: shop_id_2, shop_id_2: shop_id},
                        {user_id: user_id_2, user_id_2: user_id, shop_id, shop_id_2},
                        {user_id: user_id_2, user_id_2: user_id, shop_id: shop_id_2, shop_id_2: shop_id},
                    ]
                },
                include: [
                    {association: 'last_message'},
                    {
                        association: 'shop',
                        attributes: ['user_id', 'title', 'thumbnails'],
                        include: [
                            {
                                association: 'user',
                                attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                            },
                        ]
                    },
                    {
                        association: 'shop2',
                        attributes: ['user_id', 'title', 'thumbnails'],
                        include: [
                            {
                                association: 'user',
                                attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                            },
                        ]
                    },
                    {
                        association: 'user',
                        attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                    },
                    {
                        association: 'user2',
                        attributes: ['nickname', 'avatar', 'notice_messenger_status', 'id'],
                    },
                ],
                defaults: {user_id, shop_id, user_id_2, shop_id_2, status: false},
            })
        )
        return conversation
    }

    async getItem(
        params: any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        await this.checkInConversation(option.filter.id, params.user_id)

        return await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
    }

    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id, employee_id, type} = params
        const options = this.applyFindOptions({
            ...option,
            include: [
                {
                    association: 'shop',
                    attributes: ['thumbnails', 'title'],
                },
                {
                    association: 'user',
                    attributes: ['avatar', 'nickname'],
                },
                {
                    association: 'shop2',
                    attributes: ['thumbnails', 'title'],
                },
                {
                    association: 'user2',
                    attributes: ['avatar', 'nickname'],
                },
                {
                    association: 'last_message',
                },
            ],
            order: [['updated_at', 'DESC']],
        })
        if (employee_id) {
            if (options.where) {
                const {user_id} = options.where as any
                if (user_id) {
                    const shops = await this.exec(Shop.findAll({where: {user_id}}))
                    options.where = {
                        [Op.or]: [
                            {user_id},
                            {
                                shop_id: {
                                    [Op.in]: shops.map((shop: any) => shop.id)
                                }
                            }
                        ]
                    }
                }
            }
            return this.model.findAndCountAll(options)
        }

        const shops = await this.exec(Shop.findAll({where: {user_id}}))
        const shop_ids = shops.map((shop: any) => shop.id)
        let sqlQuery;

      if (shop_ids.length) {
          const new_shop_ids = shop_ids.map((i:any)=>`'${i}'`)
    const shop_ids_string = '(' + new_shop_ids.join(', ') + ')';
    sqlQuery = `(SELECT COUNT(*)::int FROM tbl_message WHERE tbl_message.conversation_id\
= tbl_conversation.id AND tbl_message.status = FALSE AND\
 (CASE WHEN tbl_message.user_id IS NULL AND tbl_message.shop_id NOT IN ${shop_ids_string} THEN 1
 WHEN tbl_message.user_id != '${user_id}' AND tbl_message.shop_id IS NULL THEN 1
 ELSE 0 END = 1))`;
} else {
    sqlQuery = `(SELECT COUNT(*)::int FROM tbl_message WHERE tbl_message.conversation_id\
= tbl_conversation.id AND tbl_message.status = FALSE AND\
 (CASE WHEN tbl_message.user_id IS NULL OR tbl_message.user_id != '${user_id}' THEN 1
 ELSE 0 END = 1))`;
}
        const result = await this.exec(
            this.model.findAndCountAll({
                attributes: {
                    include: [
                        [
                            sequelize.literal(sqlQuery),
                            'unread_message',
                        ],
                    ],
                },
                include: [
                    {
                        association: 'shop',
                        attributes: ['thumbnails', 'title'],
                    },
                    {
                        association: 'user',
                        attributes: ['avatar', 'nickname'],
                    },
                    {
                        association: 'shop2',
                        attributes: ['thumbnails', 'title'],
                    },
                    {
                        association: 'user2',
                        attributes: ['avatar', 'nickname'],
                    },
                    {
                        association: 'last_message',
                    },
                ],
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...(type ? type === 'USER_USER' ? [
                                    {
                                        user_id,
                                        user_id_2: {[Op.ne]: null},
                                    },
                                    {
                                        user_id_2: user_id,
                                        user_id: {[Op.ne]: null},
                                    }
                                ] : [
                                    {
                                        user_id,
                                        [Op.or]: [
                                            {shop_id: {[Op.ne]: null}},
                                            {shop_id_2: {[Op.ne]: null}},
                                        ]
                                    },
                                    {
                                        user_id_2: user_id,
                                        [Op.or]: [
                                            {shop_id: {[Op.ne]: null}},
                                            {shop_id_2: {[Op.ne]: null}},
                                        ]
                                    },
                                    ...(shop_ids.length ? [
                                        {
                                            shop_id: {
                                                [Op.in]: shop_ids,
                                            },
                                            [Op.or]: [
                                                {user_id: {[Op.ne]: null}},
                                                {user_id_2: {[Op.ne]: null}},
                                            ]
                                        },
                                        {
                                            shop_id_2: {
                                                [Op.in]: shop_ids,
                                            },
                                            [Op.or]: [
                                                {user_id: {[Op.ne]: null}},
                                                {user_id_2: {[Op.ne]: null}},
                                            ]
                                        },
                                    ] : [])

                                ] : []),
                                // {
                                //     user_id,
                                //     ...(type ? type === 'USER_USER' ? {
                                //         user_id_2: {[Op.ne]: null},
                                //     } : {
                                //         [Op.or]: [
                                //             {shop_id: {[Op.ne]: null}},
                                //             {shop_id_2: {[Op.ne]: null}},
                                //         ]
                                //     } : {})
                                // },
                                // {
                                //     user_id_2: user_id,
                                //     ...(type ? type === 'USER_USER' ? {
                                //         user_id: {[Op.ne]: null},
                                //     } : {
                                //         [Op.or]: [
                                //             {shop_id: {[Op.ne]: null}},
                                //             {shop_id_2: {[Op.ne]: null}},
                                //         ]
                                //     } : {})
                                // },
                                // ...(shop_ids.length ? [
                                //     {
                                //         shop_id: {
                                //             [Op.in]: shop_ids,
                                //         },
                                //         ...(type ? type === 'USER_USER' ? {
                                //             [Op.or]: [
                                //                 {user_id: {[Op.ne]: null}},
                                //                 {user_id_2: {[Op.ne]: null}},
                                //             ]
                                //         } : {
                                //             shop_id_2: {[Op.ne]: null}
                                //         } : {})
                                //     },
                                //     {
                                //         shop_id_2: {[Op.in]: shop_ids},
                                //         ...(type ? type === 'USER_USER' ? {
                                //             [Op.or]: [
                                //                 {user_id: {[Op.ne]: null}},
                                //                 {user_id_2: {[Op.ne]: null}},
                                //             ]
                                //         } : {
                                //             shop_id: {[Op.ne]: null}
                                //         } : {})
                                //     },
                                // ] : []),
                            ],
                        },
                        {status: true},
                        {
                            [Op.or]: [
                                {
                                    [Op.and]: [
                                        {
                                            hide_shop_id: {
                                                [Op.is]: null
                                            },
                                        },
                                        {
                                            hide_user_id: {
                                                [Op.is]: null
                                            },
                                        },
                                        {
                                            hide_user_id_2: {
                                                [Op.is]: null
                                            },
                                        },
                                        {
                                            hide_shop_id_2: {
                                                [Op.is]: null
                                            },
                                        },
                                    ],
                                },
                                {
                                    [Op.and]: [
                                        {
                                            hide_shop_id: {
                                                [Op.ne]: user_id
                                            },
                                        },
                                        {
                                            hide_user_id: {
                                                [Op.ne]: user_id
                                            },
                                        },
                                        {
                                            hide_user_id_2: {
                                                [Op.ne]: user_id
                                            },
                                        },
                                        {
                                            hide_shop_id_2: {
                                                [Op.ne]: user_id
                                            },
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                },
                order: [['updated_at', 'DESC']],
                limit: options.limit,
                offset: options.offset,
            })
        )

        return result
    }

    async delete(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter
        await this.checkInConversation(id, user_id)
        const item = await this.exec(this.getItem(params, option), {
            allowNull: false,
        })
        const shop = await this.exec(Shop.findOne({where: {id: item.shop_id}}))
        const shop2 = await this.exec(Shop.findOne({where: {id: item.shop_id_2}}))

        const dataUpdateMessage: Record<string, string> = {}
        if (shop && shop.user_id === user_id) {
            item.hide_shop_id = user_id
            dataUpdateMessage.hide_shop_id = user_id
        } else if (shop2 && shop2.user_id === user_id) {
            item.hide_shop_id_2 = user_id
            dataUpdateMessage.hide_shop_id_2 = user_id
        } else if (user_id === item.user_id_2) {
            item.hide_user_id_2 = user_id
            dataUpdateMessage.hide_user_id_2 = user_id
        } else {
            item.hide_user_id = user_id
            dataUpdateMessage.hide_user_id = user_id
        }
        await this.exec(Message.update(dataUpdateMessage, {where: {conversation_id: id}}))
        return await this.exec(item.save())
    }

    async inConversation(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter
        await this.checkInConversation(id, user_id)

        const conversation = await this.exec(
            this.model.findOne({
                where: {id},
                include: [{association: 'shop', attributes: ['user_id']}],
            })
        )

        let whereCondition: any = {
            conversation_id: id,
        }

        if (user_id === conversation.shop.user_id) {
            whereCondition = {...whereCondition, shop_id: null}
        } else {
            whereCondition = {...whereCondition, user_id: null}
        }

        await this.exec(Message.update({status: true}, {where: whereCondition}))
    }

    async checkInConversation(id: string, user_id: string) {
        const conversation = await this.exec(
            this.model.findOne({where: {id}}),
            {allowNull: false}
        )
        const shops = await this.exec(Shop.findAll({where: {user_id}}))
        const shop_ids = shops.map((shop: any) => shop.id)
        if (
            conversation.user_id !== user_id &&
            conversation.user_id_2 !== user_id &&
            !shop_ids.includes(conversation.shop_id) &&
            !shop_ids.includes(conversation.shop_id_2)
        )
            throw errorService.auth.permissionDeny()
    }

    async reportFunc(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id

        const transaction = await sequelize.transaction();
        try {
            // create report
            const num = await reportService.createReport(user_id, undefined, undefined, id, transaction);

            transaction.commit();
            let item = await this.exec(this.model.findById(id));

            if (item) item.report = num
            else item = {id ,report: num};

            return item;
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async unreportFunc(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id

        const transaction = await sequelize.transaction();
        try {
            // remove report
            const num = await reportService.removeReport(user_id, undefined, undefined, id, transaction);

            transaction.commit();
            const item = await this.exec(this.model.findById(id), {
                allowNull: false,
            });

            item.report = num;

            return item;
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }
}
