import {CrudService, ICrudOption} from '../crudService.pg'
import {Keyword, KeywordCategory} from '@/models/tables'
import {config} from '@/config'
import {Sequelize} from "@/models/base.pg";
import * as moment from "moment";
const fs = require('fs')
const xlsx = require('node-xlsx')
const AWS = require('aws-sdk')

const FILE_IMAGE_PATH = 'image/'
const KEY_CLOUDIMAGE = 'ce8391bac'
const ID = process.env.AWS_ID
const SECRET = process.env.AWS_SECRET_KEY
const BUCKET_NAME = process.env.AWS_BUCKET_NAME

export class KeywordCategoryService extends CrudService<typeof KeywordCategory> {
    constructor() {
        super(KeywordCategory)
    }

    async importExcel(params: any) {
        const data = await xlsx.parse(params.url)
        const dataExcel = data[0].data
        const [listKey, ...rest] = dataExcel
        const dataImport: Record<string, any> = []
        for (let i = 0; i < rest.length; i++) {
            const obj: Record<string, any> = {}
            for (const [index, key] of listKey.entries()) {
                obj[key] = rest[i][index]
            }
            dataImport.push(obj)
        }
        fs.unlinkSync(params.url)
        const t = await this.transaction()
        try {
            const created = await KeywordCategory.bulkCreate(dataImport as any, {transaction: t})
            await t.commit()
            return created
        } catch (e) {
            await t.rollback()
        }
    }

    async downloadExcel(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        try {
            const result = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll(
                    this.applyFindOptions(option)
                )
            )
            const s3 = new AWS.S3({
                accessKeyId: ID,
                secretAccessKey: SECRET,
            })
            const header = [
                'id',
                'name',
                'keyword_type_id'
            ]
            const data = [header]
            for (let i = 0; i < result.rows.length; i++) {
                const keyword_category = result.rows[i]
                const rowData = [
                    keyword_category.id ? keyword_category.id : '',
                    keyword_category.name ? keyword_category.name : '',
                    keyword_category.keyword_type_id ? keyword_category.keyword_type_id : '',
                ]
                data.push(rowData)
            }
            const fileName = 'keyword_category' + moment().valueOf() + '.xlsx'
            const buffer = xlsx.build([{name: 'data', data: data}]) // Returns a buffer
            fs.writeFileSync(FILE_IMAGE_PATH + fileName, buffer)
            const fileContent = fs.readFileSync(FILE_IMAGE_PATH + fileName)

            const awsParams = {
                Bucket: BUCKET_NAME,
                Key: fileName, // File name you want to save as in S3
                Body: fileContent,
                ACL: 'public-read',
            }
            // Uploading files to the bucket
            await s3.upload(awsParams, (err: any, data: any) => {
                if (err) {
                    throw err
                }
            })
            const urls3High = `https://${process.env.IMAGE_URL}/${fileName}`

            fs.unlinkSync(FILE_IMAGE_PATH + fileName)
            return {url: urls3High, file_name: fileName}
        } catch (error) {
            console.log(
                'error',
                error
            )
            throw error
        }
    }

    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async updateKeywordCategory(params: any) {
        const {list_data} = params
        for (const data of list_data) {
            await KeywordCategory.update({name: data.name}, {where: {id: data.id}})
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const {new_group} = params
        const item = await this.exec(this.getItem(option), {allowNull: false})
        if (new_group) {
            await Keyword.update({
                keyword_category_id: new_group
            }, {
                where: {
                    keyword_category_id: item.id
                }
            })
        }
        return await this.exec(item.destroy())
    }


    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const data =  await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        return data
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }
}