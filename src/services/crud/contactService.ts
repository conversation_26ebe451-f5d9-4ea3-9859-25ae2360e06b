import { CrudService, ICrudOption } from '../crudService.pg';
import { Contact } from '@/models/tables';
import { config } from '@/config';
import { Sequelize, sequelize } from '@/models';

import {
  tokenService,
  firebaseService,
  errorService,
  recordService,
  userService,
} from '@/services';
import { CONTACT_TYPE, HISTORY } from '@/const';
export class ContactService extends CrudService<typeof Contact> {
  constructor() {
    super(Contact);
  }

  async create(params: any, option?: ICrudOption) {
    // level up activity_7
    const transaction = await sequelize.transaction();
    try {
      await userService.earningExp(HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_7.NAME, params.user_id, transaction);

      if (params && params.type === CONTACT_TYPE.PARTNERSHIP_ISSUES) {
        recordService.increaseCurrentRecordData(
          {
            partnership_inquiry_count: true,
          },
          transaction
        );
      }
      
      const item = await this.exec(
        this.model.create(params, {
          transaction
        })
      );
      transaction.commit();
      return item;
    } catch (error) {
      transaction.rollback();
      throw error;
    }
  }
}
