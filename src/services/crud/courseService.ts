import { CrudService, ICrudOption } from '../crudService.pg'
import { Course, Price } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'

import { tokenService, firebaseService, errorService } from '@/services'
import * as _ from 'lodash'

const ITEM_NOT_FOUND_ERR_CODE = 404

export class CourseService extends CrudService<typeof Course> {
  constructor() {
    super(Course)
  }

  async setRecommended(params: any, option?: ICrudOption) {
    const { id } = option.filter
    const transaction = await sequelize.transaction()
    try {
      const item = await this.exec(
        Course.findOne({
          where: {
            id,
          },
        })
      )
      if (item) {
        await this.exec(
          Course.update(
            {
              recommended: false,
            },
            {
              where: {
                shop_id: item.shop_id,
              },
            }
          )
        )
        const item2 = await this.exec(
          Course.update(
            {
              recommended: true,
            },
            {
              where: {
                id: item.id,
              },
            }
          )
        )
        transaction.commit()
        return item2
      }
      transaction.rollback()
      throw errorService.database.queryFail(
        '항목을 찾을 수 없습니다',
        ITEM_NOT_FOUND_ERR_CODE
      )
    } catch (e) {
      transaction.rollback()
      throw e
    }
  }

  async setCourses(params: any, option?: ICrudOption) {
    const { courses } = params
    const { id } = option.filter
    const transaction = await sequelize.transaction()
    const shopId = id
    try {
      // remove:
      const existedCourseIds = await this.exec(
        Course.findAll({
          where: {
            shop_id: shopId,
          },
          attributes: ['id'],
        })
      )
      const existedPriceIds = await this.exec(
        Price.findAll({
          where: {
            course_id: {
              $in: existedCourseIds.map((e: any) => e.id),
            },
          },
          attributes: ['id', 'course_id'],
        })
      )

      const listCourseIdsShouldRemove = existedCourseIds.filter(
        (e: any) => !courses.find((e2: any) => e.id === e2.id)
      )
      for (let i = 0; i < courses.length; i++) {
        const course = courses[i]
        const oldPriceIds = existedPriceIds
          .filter((e: any) => e.course_id === course.id)
          .map((e: any) => e.id)
        const newPriceIds = course.prices.map((e: any) => e.id)
        const difference = oldPriceIds.filter(
          (x: any) => newPriceIds.indexOf(x) === -1
        )
        if (difference.length > 0) {
          await this.exec(
            Price.destroy({
              where: {
                id: {
                  $in: difference,
                },
              },
              transaction,
            })
          )
        }
      }
      if (listCourseIdsShouldRemove.length > 0) {
        await this.exec(
          Course.destroy({
            where: {
              id: {
                $in: listCourseIdsShouldRemove.map((e: any) => e.id),
              },
            },
            transaction,
          })
        )
      }

      // create and edit:
      for (let i = 0; i < courses.length; i++) {
        const tempCourse = courses[i]
        const courseBody: any = {
          images: tempCourse.images,
          thumbnails: tempCourse.thumbnails,
          title: tempCourse.title,
          running_time: tempCourse.running_time,
          description: tempCourse.description,
          recommended: tempCourse.recommended,
          unit: tempCourse.unit,
          shop_id: shopId,
          limit_in_one_day: tempCourse.limit_in_one_day,
          order: i,
        }

        let course = null
        if (tempCourse.id) {
          if (!tempCourse.id.includes('-')) {
            // create
            course = await this.exec(Course.create(courseBody, { transaction }))
          } else {
            // edit
            const result = await this.exec(
              Course.update(courseBody, {
                where: {
                  id: tempCourse.id,
                },
                transaction,
                returning: true,
              })
            )
            course = result[1][0]
          }
        }
        course.prices = tempCourse.prices
        // prices:
        const pricesList = course.prices
        for (let j = 0; j < pricesList.length; j++) {
          const tempPrice = pricesList[j]
          const priceBody: any = {
            name: tempPrice.name,
            price: tempPrice.price,
            discount: tempPrice.discount,
            course_id: course.id,
          }
          let price = null
          if (tempPrice.id) {
            if (!tempPrice.id.includes('-')) {
              // create
              price = await this.exec(Price.create(priceBody, { transaction }))
            } else {
              // edit
              const result = await this.exec(
                Price.update(priceBody, {
                  where: {
                    id: tempPrice.id,
                  },
                  transaction,
                  returning: true,
                })
              )
              price = result
            }
          }
        }
      }
      transaction.commit()
      return {
        is_success: true,
      }
    } catch (e) {
      console.log(
        '29148  ~ file: courseService.ts ~ line 163 ~ CourseService ~ setCourses ~ e',
        e
      )
      transaction.rollback()
      throw e
    }
  }
}
