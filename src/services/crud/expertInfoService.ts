import { sequelize } from "@/models";
import { CrudService, ICrudOption } from "../crudService.pg";
import { ExpertInfo, User } from "@/models/tables";

export class ExpertInfoService extends CrudService<typeof ExpertInfo> {
  constructor() {
    super(ExpertInfo);
  }

  async getItem(
    option: ICrudOption = {
      scope: ["defaultScope"],
    }
  ) {
    const findOptions = this.applyFindOptions({
      ...option,
      include: [
        {
          model: User,
          as: "user",
          required: false,
        },
        ...(option.include || []),
      ],
    });

    return await this.exec(
      this.modelWithScope(option.scope).findOne(findOptions),
      { allowNull: false }
    );
  }
  async create(params: any, option?: ICrudOption) {
    const transaction = await sequelize.transaction();
    try {
      const expertInfo: any = await this.model.create(params, {
        ...this.applyCreateOptions(option),
        transaction,
      });

      if (params.user_id) {
        await User.update(
          { expert_info_id: expertInfo.id },
          { where: { id: params.user_id }, transaction }
        );
      }

      await transaction.commit();
      return expertInfo;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
