import { CrudService, ICrudOption } from '../crudService'
import { AppVersion } from '@/models'

const DEFAULT_ID = 1
export class AppVersionService extends CrudService<typeof AppVersion> {
  constructor() {
    super(AppVersion)
  }

  async getItem() {
    return this.exec(AppVersion.findOne({ where: { id: DEFAULT_ID } }))
  }

  async update(params: any, option?: ICrudOption) {
    const item = await this.exec(this.model.findById(DEFAULT_ID), {
      allowNull: false,
    })
    await this.exec(item.update(params))
    return await this.getItem()
  }
}
