import { CrudService, ICrudOption } from '../crudService.pg'
import { Block, Review, Shop, Recruit, Post } from '@/models/tables'
import { errorService } from '@/services'

export class BlockService extends CrudService<typeof Block> {
  constructor() {
    super(Block)
  }
  async create(params: any, option?: ICrudOption) {
    const {user_id, user_id_blocked} = params
    const transaction = await this.transaction()

    if (!user_id || !user_id_blocked)
      throw errorService.database.queryFail(
        'user_id and user_id_blocked is not provided',
        400
      )
    try {
      const shop = await this.exec(
        Shop.findAll({
          where: {user_id: user_id},
          attributes: ['id'],
          transaction,
          raw: true,
        })
      )
      const _shop = shop.map((item: any) => item.id)
      const recruit = await this.exec(
        Recruit.findAll({
          where: {user_id: user_id},
          attributes: ['id'],
          transaction,
          raw: true,
        })
      )
      const _recruit = recruit.map((item: any) => item.id)
      const post = await this.exec(
        Post.findAll({
          where: {user_id: user_id},
          attributes: ['id'],
          transaction,
          raw: true,
        })
      )
      const _post = post.map((item: any) => item.id)
      await this.exec(
        Review.destroy({
          where: {
            $and: [
              {
                user_id: user_id_blocked,
              },
              {
                $or: [
                  {shop_id: {$in: _shop}},
                  {post_id: {$in: _post}},
                  {recruit_id: {$in: _recruit}},
                ],
              },
            ],
          },
          transaction,
        })
      )
      await this.exec(Block.create(params))
      transaction.commit()
    } catch (err) {
      transaction.rollback()
      throw err
    }
  }
}
