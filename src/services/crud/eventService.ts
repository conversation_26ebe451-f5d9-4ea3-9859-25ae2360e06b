import {CrudService, ICrudOption} from '../crudService.pg'
import {Event, Shop, User, Notification} from '@/models/tables'
import {config} from '@/config'
import {sequelize} from '@/models'
import * as moment from 'moment'
import {
    tokenService,
    firebaseService,
    errorService,
    notificationService,
} from '@/services'
import {
    SHOP_STATE,
    USER_TYPE,
    FCM_ACTIONS,
    ERROR,
    TIMEZONE,
    FCM_ACTIONS_MESSAGE,
    getFcmActionMessage,
} from '@/const'
import {ROLE} from '../../const'
import {calcDistance} from '../socketService/util'
import {checkExpired} from '../../config/utils'
import {uniq} from 'lodash'
import {Sequelize} from 'sequelize'
const KILOMETER_TO_MILE = 1 / 1.609344
const MILE_TO_DEGREE = 1 / 68.703

export class EventService extends CrudService<typeof Event> {
    constructor() {
        super(Event)
    }

    async getItem(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const item = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
        return item
    }

    async getListEvent(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        try {
            const {latitude, longitude, distance_order, radius} = params
            const foundShopJoinTable =
                option.include &&
                option.include.length &&
                option.include.find((e) => e.association === 'shop')
                
            if (radius && foundShopJoinTable) {
                option.filter = {
                    $and: [
                        option.filter,
                        Sequelize.where(
                            Sequelize.fn(
                                'ST_DWithin',
                                Sequelize.col('shop.position'),
                                Sequelize.fn('ST_SetSRID', Sequelize.fn('ST_MakePoint', longitude, latitude), 4326),
                                radius * KILOMETER_TO_MILE * MILE_TO_DEGREE
                            ),
                            true
                        ),
                    ],
                };
            }
            if (distance_order && foundShopJoinTable) {
                option.order = [
                    [
                        Sequelize.fn(
                            'ST_DistanceSphere',
                            Sequelize.col('shop.position'),
                            Sequelize.fn('ST_MakePoint', longitude, latitude)
                        ),
                        distance_order === 'ASC' ? 'asc' : 'desc',
                    ],
                ]
            }
            const options = this.applyFindOptions(option)

            options.distinct = true
            const eventData = await this.exec(Event.findAndCountAll(options))

            console.log(eventData)
            eventData.total_rows = eventData.count

            for (let i = 0; i < eventData.rows.length; i++) {
                const shop = eventData.rows[i].dataValues.shop
                if (shop && latitude && longitude) {
                    eventData.rows[i].dataValues.distance = calcDistance(
                        {
                            latitude: latitude,
                            longitude: longitude,
                        },
                        {
                            latitude: shop.latitude,
                            longitude: shop.longitude,
                        }
                    )
                }
            }
            return eventData
        } catch (error) {
            if (error.message) {
                throw errorService.database.queryFail(error.message)
            } else {
                throw error
            }
        }
    }

    async createEvent(params: any) {
        const {account_type, user_role, shop_id, user_id} = params
        const transaction = await sequelize.transaction()
        const isNormalUser = user_role === ROLE.USER
        // so sl event cua user tao ko duoc vuot qua so luong active post
        // ko the gan nhiu event vao 1 post
        try {
            if (account_type !== USER_TYPE.BIZ_USER && isNormalUser) {
                throw errorService.database.queryFail(
                    'only biz account can create event.'
                )
            }

            const eventsOfSameShop = await this.exec(
                Event.findAll({
                    where: {
                        shop_id,
                    },
                    transaction,
                    attributes: ['id'],
                })
            )

            if (eventsOfSameShop && eventsOfSameShop.length) {
                throw errorService.database.queryError(ERROR.ONE_EVENT_ONE_SHOP_ONLY)
            }

            if (user_id) {
                const user = await this.exec(
                    User.findOne({
                        where: {
                            id: user_id,
                        },
                        attributes: ['current_active_post'],
                        transaction: transaction,
                    })
                )

                const activeEvents = await this.exec(
                    Event.findAll({
                        where: {
                            user_id: user_id,
                        },
                        attributes: ['id'],
                        transaction: transaction,
                    })
                )

                if (activeEvents && activeEvents.length >= user.current_active_post) {
                    throw errorService.database.queryError(ERROR.EXCEED_EVENT_LIMIT)
                }
            }

            const eventInfo: any = {
                images: params.images,
                start_time: params.start_time,
                end_time: params.end_time,
                created_by_admin: !isNormalUser,
                description: params.description,
                state: SHOP_STATE.APPROVED,
                user_id: isNormalUser ? params.user_id : null,
                shop_id: shop_id,
            }

            if (shop_id) {
                const shop = await this.exec(
                    Shop.findOne({
                        where: {
                            id: shop_id,
                        },
                        attributes: ['title','expired_date'],
                        transaction: transaction,
                    })
                )
                if (shop) {
                    eventInfo.title = shop.title
                } else {
                    throw errorService.database.queryFail(
                        'cannot found the shop you want to pick.'
                    )
                }
                const end_time = parseInt(params.end_time)
                if (moment(end_time).diff(moment(parseInt(shop.expired_date)), 'seconds') > 0) {
                    throw errorService.database.queryFail(
                        'your event end time cannot bigger than shop expiration date.'
                    )
                }
            }

            const event = await this.exec(
                Event.create(eventInfo, {
                    transaction,
                })
            )

            // if (user && eventInfo.state === SHOP_STATE.APPROVED) {
            //   await this.exec(
            //     User.update(
            //       {
            //         current_active_event: user.current_active_event + 1,
            //       },
            //       {
            //         where: { id: user.id },
            //         transaction: transaction,
            //       }
            //     )
            //   );
            // }
            transaction.commit()
            if (event.created_at_unix_timestamp) {
                await this.exec(
                    Event.update(
                        {
                            created_at_unix_timestamp: event.created_at_unix_timestamp,
                        },
                        {
                            where: {
                                id: event.id,
                            },
                        }
                    )
                )
            }
            return {
                event,
            }
        } catch (error) {
            transaction.rollback()
            if (error.message) {
                throw errorService.database.queryFail(error.message)
            } else {
                throw error
            }
        }
    }

    async update(params: any, option?: ICrudOption) {
        const {shop_id, user_role} = params
        const transaction = await sequelize.transaction()
        try {
            const isNormalUser = user_role === ROLE.USER

            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })

            const eventsOfSameShop = await this.exec(
                Event.findAll({
                    where: {
                        shop_id: item.shop_id,
                    },
                    attributes: ['id'],
                    transaction,
                })
            )

            if (
                eventsOfSameShop &&
                eventsOfSameShop.length &&
                eventsOfSameShop.find((e: any) => e.id !== item.id)
            ) {
                throw errorService.database.queryError(ERROR.ONE_EVENT_ONE_SHOP_ONLY)
            }

            if (params.user_id && params.user_id !== item.user_id) {
                // đổi owner event khác:
                const checking_user_id = params.user_id

                const user = await this.exec(
                    User.findOne({
                        where: {
                            id: checking_user_id,
                        },
                        attributes: ['current_active_post'],
                        transaction: transaction,
                    })
                )

                const activeEvents = await this.exec(
                    Event.findAll({
                        where: {
                            user_id: checking_user_id,
                        },
                        attributes: ['id'],
                        transaction: transaction,
                    })
                )

                if (activeEvents && activeEvents.length >= user.current_active_post) {
                    throw errorService.database.queryError(ERROR.EXCEED_EVENT_LIMIT)
                }
            }

            if (shop_id) {
                const shop = await this.exec(
                    Shop.findOne({
                        where: {
                            id: shop_id,
                        },
                        attributes: ['title', 'expired_date'],
                        transaction,
                    })
                )
                if (shop) {
                    params.title = shop.title
                    const end_time = parseInt(params.end_time || item.end_time)
                    if (moment(end_time).diff(moment(parseInt(shop.expired_date)), 'seconds') > 0) {
                        throw errorService.database.queryFail(
                            'your event end time cannot bigger than shop expiration date.'
                        )
                    }
                } else {
                    throw errorService.database.queryFail(
                        'cannot found the shop you want to pick.'
                    )
                }
            }

            // cap nhat lai ngay event ve expired neu end date < hien tai:
            const timeNow = moment.utc().utcOffset(TIMEZONE * 60)
            if (
                item.state === SHOP_STATE.APPROVED &&
                item.end_time &&
                item.end_time > 0 &&
                moment
                    .utc(parseInt(item.end_time))
                    .utcOffset(TIMEZONE * 60)
                    .valueOf() < timeNow.valueOf()
            ) {
                // params.end_time = null;
                params.state = SHOP_STATE.EXPIRED
                if (item.user_id) {
                    const user = await this.exec(
                        User.findOne({
                            where: {
                                id: item.user_id,
                            },
                            attributes: ['id'],
                        })
                    )
                    if (user) {
                        await this.doExpiredEvent(item.id, item.title, item.user_id)
                    }
                }
            }
            if (!isNormalUser) {
                let event_user_id = item.user_id ? item.user_id : undefined
                if (!event_user_id) {
                    const shop = await this.exec(
                        Shop.findOne({
                            where: {
                                id: item.shop_id,
                            },
                            attributes: ['user_id'],
                        })
                    )
                    if (shop && shop.user_id) {
                        event_user_id = shop.user_id
                    }
                }
                if (event_user_id) {
                    await notificationService.sendFCMandCreateNoti(
                        event_user_id,
                        FCM_ACTIONS_MESSAGE.YOUR_EVENT_HAVE_BEEN_EDITED,
                        FCM_ACTIONS.YOUR_EVENT_HAVE_BEEN_EDITED,
                        item.id,
                        {
                            params_1: item.title,
                        }
                    )
                }
            }

            if (
                params.end_time &&
                parseInt(params.end_time) > 0 &&
                moment
                    .utc(parseInt(params.end_time))
                    .utcOffset(TIMEZONE * 60)
                    .valueOf() > timeNow.valueOf()
            ) {
                params.state = SHOP_STATE.APPROVED
            }
            console.log(
                '29148 : EventService -> update -> isNormalUser 2',
                isNormalUser
            )

            await this.exec(item.update(params, {transaction}))

            // if (params.state && item.user_id) {
            //   if (
            //     params.state === SHOP_STATE.APPROVED &&
            //     item.state === SHOP_STATE.PENDING
            //   ) {
            //     firebaseService.sendNotification(
            //       item.user_id,
            //       FCM_ACTIONS_MESSAGE.APPROVE_SHOP,
            //       FCM_ACTIONS.APPROVE_SHOP
            //     );
            //   } else if (
            //     params.state === SHOP_STATE.REJECTED &&
            //     item.state === SHOP_STATE.PENDING
            //   ) {
            //     firebaseService.sendNotification(
            //       item.user_id,
            //       FCM_ACTIONS_MESSAGE.REJECT_SHOP,
            //       FCM_ACTIONS.REJECT_SHOP
            //     );
            //   }
            // }
            transaction.commit()

            return await this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async doExpiredEvent(id: string, title: string, user_id: string) {
        const params = {
            params_1: title,
        }
        const message = getFcmActionMessage(
            FCM_ACTIONS_MESSAGE.EXPIRE_EVENT,
            params
        )

        // firebaseService.sendNotification(
        //     user_id,
        //     message,
        //     FCM_ACTIONS.EXPIRE_EVENT,
        //     id
        // )

        const entity_id = id
        const bodyNoti: any = {
            user_id: user_id,
            title: message,
            content: message,
            data: {
                message: message,
                params,
            },
            action: FCM_ACTIONS.EXPIRE_EVENT,
            interacting_type: 'EXPIRE_EVENT',
            interacting_content_id: entity_id,
        }
        console.log(
            '29148 : EventService -> scheduleExpiredEvent -> bodyNoti',
            bodyNoti
        )

        await this.exec(Notification.create(bodyNoti))
    }

    async scheduleExpiredEvent() {
        try {
            const states = [SHOP_STATE.APPROVED]

            const timeNow = moment()
                .utcOffset(TIMEZONE * 60)
                .startOf('day')

            const events = await this.exec(
                Event.findAll({
                    where: {
                        end_time: {$lt: timeNow.valueOf()},
                        state: {$in: states},
                    },
                    attributes: ['id', 'title', 'user_id'],
                })
            )

            if (events && events.length > 0) {
                const event_ids = events.map((e: any) => e.id)

                await this.exec(
                    Event.update(
                        {
                            state: SHOP_STATE.EXPIRED,
                            // end_time: null,
                        },
                        {
                            where: {
                                id: {$in: event_ids},
                            },
                        }
                    )
                )

                for (let i = 0; i < events.length; i++) {
                    if (events[i].user_id) {
                        await this.doExpiredEvent(
                            events[i].id,
                            events[i].title,
                            events[i].user_id
                        )
                    }
                }
            }
            return {status: events.length}
        } catch (error) {
            console.log('29148 scheduleExpiredEvent error: ', error)
        }
    }

    async scheduleNotiBeforeExpiredEvent() {
        try {
            const states = [SHOP_STATE.APPROVED]

            const events = await this.exec(
                Event.findAll({
                    where: {
                        end_time: {
                            $between: [
                                moment()
                                    .utcOffset(TIMEZONE * 60)
                                    .startOf('day')
                                    .valueOf(),
                                moment()
                                    .utcOffset(TIMEZONE * 60)
                                    .endOf('day')
                                    .valueOf(),
                            ],
                        },
                        state: {$in: states},
                    },
                    attributes: ['id', 'title', 'user_id'],
                })
            )

            if (events && events.length > 0) {
                for (let i = 0; i < events.length; i++) {
                    if (events[i].user_id) {
                        const params = {
                            params_1: events[i].title,
                        }
                        const message = getFcmActionMessage(
                            FCM_ACTIONS_MESSAGE.PRE_EXPIRE_EVENT,
                            params
                        )
                        // firebaseService.sendNotification(
                        //     events[i].user_id,
                        //     message,
                        //     FCM_ACTIONS.PRE_EXPIRE_EVENT,
                        //     events[i].id
                        // )

                        const entity_id = events[i].id
                        const bodyNoti: any = {
                            user_id: events[i].user_id,
                            title: message,
                            content: message,
                            data: {
                                message: message,
                                params,
                            },
                            action: FCM_ACTIONS.PRE_EXPIRE_EVENT,
                            interacting_type: 'PRE_EXPIRE_EVENT',
                            interacting_content_id: entity_id,
                        }

                        await this.exec(Notification.create(bodyNoti))
                    }
                }
            }
            return {status: events.length}
        } catch (error) {
            console.log('29148 scheduleNotiBeforeExpiredEvent error: ', error)
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const {user_role} = params
        const item = await this.exec(this.getItem(option), {allowNull: false})
        const isNormalUser = user_role === ROLE.USER

        let event_user_id = item.user_id ? item.user_id : undefined
        console.log(
            '29148 : EventService -> delete -> event_user_id',
            event_user_id
        )
        if (!event_user_id) {
            const shop = await this.exec(
                Shop.findOne({
                    where: {
                        id: item.shop_id,
                    },
                    attributes: ['user_id'],
                })
            )
            if (shop && shop.user_id) {
                event_user_id = shop.user_id
            }
        }
        console.log('29148 : EventService -> delete -> isNormalUser', isNormalUser)
        console.log(
            '29148 : EventService -> delete -> event_user_id',
            event_user_id
        )

        if (!isNormalUser && event_user_id) {
            console.log(
                '29148 : EventService -> delete -> !isNormalUser && event_user_id',
                !isNormalUser && event_user_id
            )
            await notificationService.sendFCMandCreateNoti(
                event_user_id,
                FCM_ACTIONS_MESSAGE.YOUR_EVENT_HAVE_BEEN_DELETED,
                FCM_ACTIONS.YOUR_EVENT_HAVE_BEEN_DELETED,
                item.id,
                {
                    params_1: item.title,
                }
            )
        }

        return await this.exec(item.destroy())
    }
}
