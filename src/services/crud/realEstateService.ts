import {CrudService, ICrudOption} from '../crudService.pg'
import {CallShop, RealEstate, RealEstateTag} from '@/models/tables'
import {sequelize} from "@/models";

export class RealEstateService extends CrudService<typeof RealEstate> {
    constructor() {
        super(RealEstate)
    }

    async create(params: any, option?: ICrudOption) {
        const {tag_ids} = params
        const transaction = await sequelize.transaction()
        try {
            const created = await this.exec(
                this.model.create(params, this.applyCreateOptions({...option, transaction}))
            )
            if (tag_ids) {
                for (let i = 0; i < tag_ids.length; i++) {
                    const bodyST: any = {
                        real_estate_id: created.id,
                        tag_id: tag_ids[i],
                    }
                    await this.exec(
                        RealEstateTag.create(bodyST, {
                            transaction,
                        })
                    )
                }
            }
            await transaction.commit()
            return created
        } catch (e) {
            await transaction.rollback()
            throw e
        }
    }

    async update(params: any, option?: ICrudOption) {
        const {tag_ids} = params
        const transaction = await sequelize.transaction()
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update(params , {transaction}))
        if (tag_ids) {
            await this.exec(
                RealEstateTag.destroy({
                    where: {
                        real_estate_id: item.id,
                    },
                    transaction: transaction,
                })
            )

            for (let i = 0; i < tag_ids.length; i++) {
                const bodyST: any = {
                    real_estate_id: item.id,
                    tag_id: tag_ids[i],
                }
                await this.exec(
                    RealEstateTag.create(bodyST, {
                        transaction,
                    })
                )
            }
        }
        return await this.getItem(option)
    }

    async call(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update({call: item.call + 1}))
        return await this.getItem(option)
    }
}
