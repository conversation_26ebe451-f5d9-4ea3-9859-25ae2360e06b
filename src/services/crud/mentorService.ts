import { CrudService, ICrudOption } from '../crudService.pg'
import { Favourite, FavouriteMentor, Mentor, Shop, User, ViewMentor } from '@/models/tables'
import { sequelize } from '@/models'
import { config } from "@/config";
import { Op, Sequelize } from "sequelize";
import { SHOP_LIST_BOARD_ORDER, SHOP_STATE } from "@/const";
import * as moment from "moment";

const LIKE_STATUS = {
    LIKE: true,
    UNLIKE: false,
}

export class MentorService extends CrudService<typeof Mentor> {
    constructor() {
        super(Mentor)
    }

    async setMentor(params: any, option?: ICrudOption) {
        const {user_id, mentors} = params
        const {id} = option.filter
        const transaction = await sequelize.transaction()
        const shopId = id
        try {
            const existedMentorIds = await this.exec(
                Mentor.findAll({
                    where: {
                        shop_id: shopId,
                    },
                    attributes: ['id'],
                })
            )

            const listMentorIdsShouldRemove = existedMentorIds.filter(
                (e: any) => !mentors.find((e2: any) => e.id === e2.id)
            )

            if (listMentorIdsShouldRemove.length > 0) {
                await this.exec(
                    Mentor.destroy({
                        where: {
                            id: {
                                $in: listMentorIdsShouldRemove.map((e: any) => e.id),
                            },
                        },
                        transaction,
                    })
                )
            }

            for (let i = 0; i < mentors.length; i++) {
                const tempMentor = mentors[i]
                const mentorBody: any = {
                    name: tempMentor.name,
                    description: tempMentor.description,
                    images: tempMentor.images,
                    recommended: tempMentor.recommended,
                    thumbnails: tempMentor.thumbnails,
                    shop_id: shopId,
                    order: i,
                }

                let mentor = null
                if (tempMentor.id) {
                    if (!tempMentor.id.includes('-')) {
                        mentor = await this.exec(Mentor.create(mentorBody, {transaction}))
                    } else {
                        const result = await this.exec(
                            Mentor.update(mentorBody, {
                                where: {
                                    id: tempMentor.id,
                                },
                                transaction,
                                returning: true,
                            })
                        )
                        mentor = result[1][0]
                    }
                }
            }
            transaction.commit()
            return {
                is_success: true,
            }
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async likeMentor(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // mentor id
        return await this.onChangeMentorLikeStatus(user_id, id, LIKE_STATUS.LIKE)
    }

    async unlikeMentor(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // mentor id
        return await this.onChangeMentorLikeStatus(user_id, id, LIKE_STATUS.UNLIKE)
    }

    onChangeMentorLikeStatus = async (
        user_id: string,
        mentor_id: string,
        status: boolean
    ) => {
        const transaction = await sequelize.transaction()
        try {
            const bodyFS: any = {user_id: user_id, mentor_id: mentor_id}

            const tempFind = await this.exec(
                FavouriteMentor.findOne({
                    where: bodyFS,
                    attributes: ['id'],
                })
            )

            if (status) {
                if (!tempFind)
                    await this.exec(FavouriteMentor.create(bodyFS, {transaction}))
            } else {
                if (tempFind) {
                    await this.exec(
                        FavouriteMentor.destroy({
                            where: {
                                id: tempFind.id,
                            },
                            transaction,
                        })
                    )
                }
            }

            const mentor = await this.exec(this.model.findById(mentor_id), {
                allowNull: false,
            })

            const like_count = status ? mentor.like + 1 : mentor.like - 1
            mentor.like = like_count

            const body: any = {
                like: like_count,
            }

            await this.exec(
                this.model.update(body, {
                    where: {
                        id: mentor.id,
                    },
                    transaction: transaction,
                })
            )

            transaction.commit()
            return {
                mentor: mentor,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async getMentorByThema(option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
    }) {
        return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
    }

    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {latitude, longitude, order_option, distance_order, user_id, shopFilter} = params
        const distance = [
            Sequelize.fn(
                'ST_DistanceSphere',
                Sequelize.col('shop.position'),
                Sequelize.fn('ST_MakePoint', longitude, latitude)
            ),
            'distance',
        ]
        option.include = [{
            association: 'shop',
            where: shopFilter ? shopFilter : {
                id: {
                    [Op.not]: null
                }
            },
            attributes: {
                include: [distance],
            },
            require: true,
            include: [{
                association: 'category',
                require: true,
                where: {
                    id: {
                        [Op.not]: null
                    }
                }
            }],
        }]
        if (option.filter && option.filter.thema_id) {
            const thema_id = option.filter.thema_id;
            const mentorInclude = option.include.findIndex(item => item.association === 'shop')
            option.include[mentorInclude].include[0].where.thema_id = thema_id
            delete option.filter.thema_id
        }
        const options = this.applyFindOptions(option)

        if (order_option && order_option === SHOP_LIST_BOARD_ORDER.MOST_COMMENT) {
            options.order = [['shop', 'soft_comment_count', 'desc']]
        } else if (
            order_option &&
            order_option === SHOP_LIST_BOARD_ORDER.MOST_FAVORITE
        ) {
            options.order = [['shop', 'like', 'desc']]
        } else if (distance_order) {
            options.order = [
                [
                    Sequelize.fn(
                        'ST_DistanceSphere',
                        Sequelize.col('shop.position'),
                        Sequelize.fn('ST_MakePoint', longitude, latitude)
                    ),
                    distance_order === 'ASC' ? 'asc' : 'desc',
                ],
            ]
        }
        const dataMentor = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                options
            )
        )
        for (const mentor of dataMentor.rows) {
            const is_like = await FavouriteMentor.findOne({where: {user_id, mentor_id: mentor.id}})
            mentor.dataValues.is_like = !!is_like
        }
        return dataMentor
    }

    async getItem(
        params: any,
        option: ICrudOption = {
            scope: ['defaultScope'],
        },
        is_get ?: boolean
    ) {
        const {user_id} = params;
        const {id} = option.filter;
        const mentor = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )
        if (is_get) {
            await this.exec(ViewMentor.create({user_id: user_id, mentor_id: id} as any));
            mentor.view++
            await mentor.save()
        }
        return mentor
    }

    async rankMentor(params: any) {
        //get list mentor order by favorite of day
        const {latitude, longitude, order_option, distance_order, user_id} = params
        const distance = [
            Sequelize.fn(
                'ST_DistanceSphere',
                Sequelize.col('tbl_shop.position'),
                Sequelize.fn('ST_MakePoint', longitude, latitude)
            ),
            'distance',
        ]
        const startOfDay = moment().utc().add(9, 'hour').startOf('days').subtract(9, 'hour').toDate();
        const endOfDay = moment().utc().add(9, 'hour').endOf('days').subtract(9, 'hour').toDate();
        const shopApprove = await Shop.findAll({
            where: {
                state: SHOP_STATE.APPROVED
            },
            attributes: ['id']
        })
        const listMentor = await ViewMentor.findAll({
            where: {
                created_at: {
                    [Op.between]: [startOfDay, endOfDay]
                }
            },
            attributes: ['mentor_id', [sequelize.fn('COUNT', 'mentor_id'), 'view_count']],
            group: ['mentor_id', 'mentor.id', 'mentor.shop_id'],
            include: [{
                association: "mentor",
                where: {
                    id: {
                        [Op.not]: null
                    },
                    shop_id: {
                        [Op.in]: shopApprove.map(i => i.id)
                    }
                },
            }],
            order: [[sequelize.literal('view_count'), 'DESC']] as any,
            limit: 5
        })
        if (listMentor.length < 5) {
            const listMentor2 = await Mentor.findAll({
                where: {
                    id: {
                        [Op.notIn]: listMentor.map(i => i.mentor_id)
                    }
                },
                include: [
                    {
                        'association': 'shop',
                        where: {
                            state: SHOP_STATE.APPROVED
                        },
                    }
                ],
                order: [['view', 'DESC']],
                limit: 10 - listMentor.length,
            })
            const listMentorFilter = listMentor2.map(i => ({
                dataValues: {
                    mentor_id: i.id,
                    view_count: i.dataValues.view,
                    mentor: i
                }
            }))
            listMentor.push(...listMentorFilter)

        }
        for (const mentor of listMentor) {
            mentor.dataValues.view_count = Number(mentor.dataValues.view_count)
            mentor.dataValues.mentor.dataValues.shop = await Shop.findOne({
                where: {
                    id: mentor.dataValues.mentor.shop_id,
                },
                attributes: {
                    include: [distance]
                } as any
            })
            if (user_id) {
                const is_like = await Favourite.findOne({where: {user_id, shop_id: mentor.dataValues.mentor.id}})
                mentor.dataValues.mentor.dataValues.shop.dataValues.is_like = !!is_like
            } else {
                mentor.dataValues.mentor.dataValues.shop.dataValues.is_like = false
            }
        }
        return listMentor.map(i => i.dataValues)
    }

    async getListV2(params: any, option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
    }) {
        const {user_id, latitude, longitude, order_option, distance_order} =
            params
        const distance = [
            Sequelize.fn(
                'ST_DistanceSphere',
                Sequelize.col('shop.position'),
                Sequelize.fn('ST_MakePoint', longitude, latitude)
            ),
            'distance',
        ]

        if (option.attributes) {
            option.attributes = [...option.attributes, distance]
        } else {
            option.attributes = {
                include: [distance],
            } as any
        }

        const options = this.applyFindOptions(option)

        options.distinct = true

        if (order_option && order_option === SHOP_LIST_BOARD_ORDER.MOST_COMMENT) {
            options.order = [['shop', 'soft_comment_count', 'desc']]
        } else if (
            order_option &&
            order_option === SHOP_LIST_BOARD_ORDER.MOST_FAVORITE
        ) {
            options.order = [['shop', 'like', 'desc']]
        } else if (distance_order) {
            options.order = [
                [
                    Sequelize.fn(
                        'ST_DistanceSphere',
                        Sequelize.col('shop.position'),
                        Sequelize.fn('ST_MakePoint', longitude, latitude)
                    ),
                    distance_order === 'ASC' ? 'asc' : 'desc',
                ],
            ]
        }
        const {where, attributes, order, limit, offset, include} = options

        const dataMentor = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll({
                limit,
                offset,
                order,
                include: [{
                    association: 'shop',
                    attributes,
                    where,
                    include
                }] as any
            })
        )
        for (const mentor of dataMentor.rows) {
            const is_like = await FavouriteMentor.findOne({where: {user_id, mentor_id: mentor.id}})
            mentor.dataValues.is_like = !!is_like
        }
        return dataMentor

    }

}
