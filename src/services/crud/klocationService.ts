import { CrudService, ICrudOption } from '../crudService.pg';
import { KLocation } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService } from '@/services';
export class KLocationService extends CrudService<typeof KLocation> {
  constructor() {
    super(KLocation);
  }

  async update(params: any, option?: ICrudOption) {
         // start here
         const transaction = await sequelize.transaction();
        //  4bb834b0-3a9a-11eb-8baa-4519734871c0
         try {

           transaction.commit();
          return true;
        } catch (error) {
          transaction.rollback();
        }
        return false;
        // end here

    const item = await this.exec(this.model.findById(option.filter.id), {
      allowNull: false,
    });
    await this.exec(item.update(params));
    return await this.getItem(option);
  }
}
