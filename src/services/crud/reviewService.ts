import { config } from '@/config'
import {
    FCM_ACTIONS,
    FCM_ACTIONS_MESSAGE,
    HISTORY,
    REVIEW_TYPE,
    REVIEW_SUB_TYPE,
    RESERVATION_STATUS,
    POINT_ACTION, getFcmActionMessage,
} from '@/const'
import { DislikePost, DislikeReview, FavouriteReview, sequelize, Sequelize } from '@/models'
import {
    Category,
    Notification,
    Post,
    Recruit,
    Report,
    Review,
    Shop,
    Thema,
    User,
    Block,
    Feedback,
    Reservation,
} from '@/models/tables'
import {
    errorService,
    firebaseService,
    notificationService,
    recordService,
    reportService,
    shopService,
    userService,
    pointService,
    rewardService,
} from '@/services'
import * as moment from 'moment'
import { CrudService, ICrudOption } from '../crudService.pg'
import { Op } from "sequelize";

const TYPE = {
    INCREASE: 'INCREASE',
    DECREASE: 'DECREASE',
}
const EXECUTE = {
    LIKE_REVIEW: 'LIKE_REVIEW',
    UNLIKE_REVIEW: 'UNLIKE_REVIEW',
    DISLIKE_REVIEW: 'DISLIKE_REVIEW',
    UNDISLIKE_REVIEW: 'UNDISLIKE_REVIEW',
};

const REVIEW_ERR_CODE = 300
const TYPE_MISSING_ERROR = REVIEW_ERR_CODE + 1

export class ReviewService extends CrudService<typeof Review> {
    constructor() {
        super(Review)
    }

    async getListByAdmin(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
    }

    async unreportFunc(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // post id

        const transaction = await sequelize.transaction()
        try {
            // create report
            const item = await this.exec(this.model.findById(id), {
                allowNull: false,
            })
            const num = await reportService.removeReport(
                user_id,
                undefined,
                id,
                undefined,
                transaction
            )

            if (item.parent_id) {
                await this.updateLast5Comment(item.parent_id, transaction)
            }
            item.report = num

            transaction.commit()

            return item
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async reportFunc(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // post id

        const transaction = await sequelize.transaction()
        try {
            // create report

            const num = await reportService.createReport(
                user_id,
                undefined,
                id,
                undefined,
                transaction
            )
            let item = await this.exec(this.model.findById(id))
            if (!item) {
                item = {
                    id
                }
            }

            if (item.parent_id) {
                await this.updateLast5Comment(item.parent_id, transaction)
            }
            item.report = num

            transaction.commit()

            return item
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async softDeleteOne(params: any) {
        const transaction = await sequelize.transaction()
        const {id} = params
        try {
            const item = await this.exec(
                Review.findOne({
                    where: {
                        id,
                    },
                })
            )

            await this.exec(
                Review.update(
                    {
                        status: false,
                    },
                    {
                        where: {
                            id,
                        },
                        transaction,
                    }
                )
            )

            await this.updateCountNumber(
                item.shop_id,
                item.recruit_id,
                item.post_id,
                transaction
            )

            transaction.commit()
            return item
        } catch (err) {
            transaction.rollback()
            throw err
        }

        return {result: false}
    }

    async softDeleteAll(user_id: string, params: any) {
        const transaction = await sequelize.transaction()
        try {
            const deletedReviewsList = await this.exec(
                Review.findAll({
                    where: {
                        user_id,
                        status: true,
                    },
                    attributes: ['id', 'shop_id', 'post_id', 'recruit_id'],
                })
            )

            const result = await this.exec(
                this.model.update(
                    {
                        status: false,
                    },
                    {
                        where: {
                            user_id,
                        },
                        transaction,
                    }
                )
            )

            const distinguishIds: Array<any> = []

            for (let i = 0; i < deletedReviewsList.length; i++) {
                const item = deletedReviewsList[i]
                const foundShopReview = distinguishIds.find(
                    (e: any) => e.shop_id && item.shop_id && e.shop_id === item.shop_id
                )
                const foundRecruitReview = distinguishIds.find(
                    (e: any) =>
                        e.recruit_id && item.recruit_id && e.recruit_id === item.recruit_id
                )
                const foundPostReview = distinguishIds.find(
                    (e: any) => e.post_id && item.post_id && e.post_id === item.post_id
                )
                if (foundShopReview || foundRecruitReview || foundPostReview) {
                    continue
                }
                distinguishIds.push(item)
            }

            for (let i = 0; i < distinguishIds.length; i++) {
                // UPDATE COUNT NUMBER for shop, post and recruit:
                const item = distinguishIds[i]
                await this.updateCountNumber(
                    item.shop_id,
                    item.recruit_id,
                    item.post_id,
                    transaction
                )
            }
            transaction.commit()
            return result
        } catch (err) {
            transaction.rollback()
            throw err
        }
    }

    async update(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })

            if (params && params.parent_id && params.parent_id !== item.parent_id) {
                const parentObj = await this.exec(
                    this.model.findOne({
                        where: {
                            id: params.parent_id,
                        },
                        attributes: ['user_id'],
                    })
                )
                if (parentObj) {
                    params.parent_user_id = parentObj.user_id
                }
            }

            await this.exec(
                this.model.update(
                    {
                        ...params,
                        created_at_unix_timestamp: moment().valueOf(),
                    },
                    {
                        where: {
                            id: item.id,
                        },
                        transaction,
                    }
                )
            )

            if (item.parent_id) {
                await this.updateLast5Comment(item.parent_id, transaction)
            }

            transaction.commit()
            return await this.getItem(option)
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async updateLast5Comment(parentId: String, transaction: any) {
        const listLast5Comments = await this.getDefaultRepliesComment(
            parentId,
            transaction
        )
        await this.exec(
            this.model.update(
                {
                    last_5_comments: listLast5Comments,
                },
                {
                    where: {
                        id: parentId,
                    },
                    transaction,
                }
            )
        )
    }

    async updateCountNumber(
        shop_id: string = null,
        post_id: string = null,
        recruit_id: string = null,
        transaction: any = undefined
    ) {
        try {
            let currentQueryModel = null

            if (shop_id) {
                currentQueryModel = Shop
            } else if (recruit_id) {
                currentQueryModel = Recruit
            } else if (post_id) {
                currentQueryModel = Post
            }

            const commentCount = await this.exec(
                this.model.count({
                    where: {
                        post_id,
                        shop_id,
                        recruit_id,
                    },
                    transaction: transaction,
                })
            )

            const softCommentCount = await this.exec(
                this.model.count({
                    where: {
                        post_id,
                        shop_id,
                        recruit_id,
                        status: true,
                    },
                    transaction: transaction,
                })
            )

            const id = shop_id || post_id || recruit_id

            let body: any = {
                comment: commentCount,
                soft_comment_count: softCommentCount,
            }

            if (shop_id) {
                const shopInfo = await this.exec(
                    Shop.findOne({
                        where: {
                            id: shop_id,
                        },
                    })
                )
                const convertedBodyData = shopService.syncSystemData(shopInfo, body)
                body = {...convertedBodyData}
            }

            return await this.exec(
                currentQueryModel.update(body, {
                    where: {
                        id,
                    },
                    transaction,
                })
            )
        } catch (error) {
            throw error
        }
    }

    async create(params: any, option?: ICrudOption) {
        let {shop_id, recruit_id, post_id, user_id} = params
        const POINT_FOR_REVIEW = 200
        const transaction = await sequelize.transaction()
        try {
            let user
            if (shop_id) {
                user = await this.exec(
                    Shop.findByPk(shop_id, {
                        attributes: ['user_id'],
                        include: [
                            {
                                association: 'category',
                                attributes: ['id'],
                                include: [
                                    {
                                        association: 'thema',
                                        attributes: ['review_require'],
                                    },
                                ],
                            },
                        ],
                    }),
                    {allowNull: false}
                )
                if (params.feedbacks && !params.parent_id) {
                    if (user.category.thema.review_require) {
                        // const reservationCanFeedback = await this.exec(
                        //     Reservation.findOne({
                        //       where: {
                        //         user_id,
                        //         shop_id,
                        //         feedback_status: true,
                        //       },
                        //       order: [['created_at', 'asc']],
                        //       lock: Sequelize.Transaction.LOCK.UPDATE,
                        //       transaction,
                        //     })
                        // )
                        // if (!reservationCanFeedback) {
                        //   throw errorService.database.queryFail(
                        //       'You must make an reservation before rating',
                        //       400
                        //   )
                        // }
                        if (user_id !== user.user_id) {
                            await pointService.changePoint(
                                {
                                    user_id,
                                    action: POINT_ACTION.REVIEW,
                                    point: POINT_FOR_REVIEW,
                                },
                                transaction
                            )
                            const message = getFcmActionMessage(FCM_ACTIONS_MESSAGE.POINT_FOR_REVIEW_SHOP, {
                                params_1: POINT_FOR_REVIEW,
                            })

                            const action = FCM_ACTIONS.POINT_FOR_REVIEW_SHOP
                            const entity_id = shop_id


                            const bodyNoti: any = {
                                user_id: user.user_id,
                                title: message,
                                content: message,
                                data: {message: message},
                                action: Number(action),
                                interacting_type: 'POINT_FOR_REVIEW_SHOP',
                                interacting_content_id: entity_id,
                            }
                            await Promise.all([
                                // firebaseService.sendNotification(
                                //     user.user_id,
                                //     message,
                                //     action,
                                //     entity_id
                                // ),
                                this.exec(
                                    Notification.create(bodyNoti, {
                                        transaction,
                                    })
                                )
                            ])
                        }
                    }

                    const bodyFeedback: any = []
                    for (const feedback_item_id of params.feedbacks) {
                        bodyFeedback.push({feedback_item_id})
                    }
                    params.feedbacks = bodyFeedback
                }
            } else if (recruit_id) {
                user = await this.exec(
                    Recruit.findByPk(recruit_id, {attributes: ['user_id']}),
                    {allowNull: false}
                )
            } else if (post_id) {
                user = await this.exec(
                    Post.findByPk(post_id, {attributes: ['user_id']}),
                    {allowNull: false}
                )
            }
            const block = await this.exec(
                Block.findOne({
                    where: {user_id: user.user_id, user_id_blocked: user_id},
                })
            )

            if (block)
                throw errorService.database.queryFail(
                    'Cant comment because you have blocked',
                    400
                )

            if (params && params.parent_id) {
                const item = await this.exec(
                    Review.findOne({
                        where: {
                            id: params.parent_id,
                        },
                        attributes: ['shop_id', 'post_id', 'recruit_id'],
                    })
                )
                shop_id = item.shop_id
                post_id = item.post_id
                recruit_id = item.recruit_id
            }

            if (recruit_id) {
                params.type = REVIEW_TYPE.RECRUIT
            } else if (post_id) {
                params.type = REVIEW_TYPE.POST
            } else {
                params.type = REVIEW_TYPE.SHOP
            }
            if (params && params.parent_id) {
                const parentObj = await this.exec(
                    this.model.findOne({
                        where: {
                            id: params.parent_id,
                        },
                        attributes: ['user_id'],
                        transaction,
                    })
                )
                if (parentObj) {
                    params.parent_user_id = parentObj.user_id
                }
            }

            if (params.type == REVIEW_TYPE.SHOP && !params.parent_id) {
                // level up activity_1
                await userService.earningExp(
                    HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_1.NAME,
                    params.user_id,
                    transaction
                )
                // Add reward for review
                await this.exec(rewardService.addReward(user_id, "WRITE_REVIEW", transaction));
            } else if (params.type == REVIEW_TYPE.POST && !params.parent_id) {
                // level up activity_4
                await userService.earningExp(
                    HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_4.NAME,
                    params.user_id,
                    transaction
                )
                //  Add reward for comment
                await this.exec(rewardService.addReward(user_id, "WRITE_COMMENT", transaction));
            } else {
                // level up activity_5
                await userService.earningExp(
                    HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_5.NAME,
                    params.user_id,
                    transaction
                )
                //  Add reward for comment
                await this.exec(rewardService.addReward(user_id, "WRITE_COMMENT", transaction));
            }

            const comment: any = await this.exec(
                this.model.create(params, {
                    include: [{association: 'feedbacks'}],
                    transaction,
                })
            )

            if (params && params.parent_id) {
                // Send notification to parent
                if (params.type === REVIEW_TYPE.POST) {
                    const parentComment = await this.exec(
                        this.model.findOne({
                            where: {
                                id: params.parent_id,
                            },
                            attributes: ['user_id', 'post_id'],
                            transaction,
                        })
                    )
                    if (parentComment && parentComment.user_id) {
                        const message = FCM_ACTIONS_MESSAGE.REPLY_COMMENT_POST
                        // firebaseService.sendNotification(
                        //     parentComment.user_id,
                        //     message,
                        //     FCM_ACTIONS.REPLY_COMMENT_POST,
                        //     parentComment.post_id
                        // )

                        const entity_id = parentComment.post_id
                        const bodyNoti: any = {
                            user_id: parentComment.user_id,
                            title: message,
                            content: message,
                            data: {message: message},
                            action: Number(FCM_ACTIONS.REPLY_COMMENT_POST),
                            interacting_type: 'REPLY_COMMENT_POST',
                            interacting_content_id: entity_id,
                        }

                        await this.exec(
                            Notification.create(bodyNoti, {
                                transaction,
                            })
                        )
                    }
                }
            } else {
                // send notification to OWNER
                const message = shop_id
                    ? FCM_ACTIONS_MESSAGE.COMMENT_SHOP
                    : post_id
                        ? FCM_ACTIONS_MESSAGE.COMMENT_POST
                        : ''

                const action = shop_id
                    ? FCM_ACTIONS.COMMENT_SHOP
                    : post_id
                        ? FCM_ACTIONS.COMMENT_POST
                        : ''
                const entity_id = shop_id ? shop_id : post_id ? post_id : recruit_id
                // firebaseService.sendNotification(
                //     user.user_id,
                //     message,
                //     action,
                //     entity_id
                // )

                const bodyNoti: any = {
                    user_id: user.user_id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action: Number(action),
                    interacting_type: shop_id
                        ? 'COMMENT_SHOP'
                        : post_id
                            ? 'COMMENT_POST'
                            : undefined,
                    interacting_content_id: entity_id,
                }

                await this.exec(
                    Notification.create(bodyNoti, {
                        transaction,
                    })
                )
                if (post_id) {
                    const message = FCM_ACTIONS_MESSAGE.COMMENT_POST_DONE
                    const action = FCM_ACTIONS.COMMENT_POST_DONE
                    // firebaseService.sendNotification(
                    //     user.user_id,
                    //     message,
                    //     action,
                    //     entity_id
                    // )

                    const bodyNoti: any = {
                        user_id: user.user_id,
                        title: message,
                        content: message,
                        data: {message: message},
                        action: Number(action),
                        interacting_type: 'COMMENT_POST_DONE',
                        interacting_content_id: entity_id,
                    }

                    await this.exec(
                        Notification.create(bodyNoti, {
                            transaction,
                        })
                    )
                }

                if (shop_id) {
                    const shop = await this.exec(
                        Shop.findOne({
                            where: {
                                id: shop_id,
                            },
                            attributes: ['title'],
                        })
                    )
                    const message = getFcmActionMessage(FCM_ACTIONS_MESSAGE.COMMENT_SHOP_DONE, {
                        params_1: shop.title,
                    })
                    const action = FCM_ACTIONS.COMMENT_SHOP_DONE
                    const bodyNoti: any = {
                        user_id: user.user_id,
                        title: message,
                        content: message,
                        data: {message: message},
                        action: Number(action),
                        interacting_type: 'COMMENT_SHOP_DONE',
                        interacting_content_id: entity_id,
                    }
                    await Promise.all([
                        // firebaseService.sendNotification(
                        //     user.user_id,
                        //     message,
                        //     action,
                        //     entity_id
                        // ),
                        this.exec(
                            Notification.create(bodyNoti, {
                                transaction,
                            })
                        ),
                    ])
                }
            }

            await this.updateCountNumber(shop_id, post_id, recruit_id, transaction)

            await recordService.increaseCurrentRecordData(
                {
                    reply_count: true,
                },
                transaction
            )

            if (params && params.parent_id) {
                await this.updateLast5Comment(params.parent_id, transaction)
            }

            transaction.commit()
            return {
                comment,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async deleteAll(option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            // update comment cound:
            const listDeletedComment =
                option &&
                option.filter &&
                option.filter.id &&
                option.filter.id['$in'] &&
                option.filter.id['$in'].length
                    ? option.filter.id['$in']
                    : []
            const shopIds = []
            const recruitIds = []
            const postIds = []

            if (listDeletedComment.length) {
                for (let i = 0; i < listDeletedComment.length; i++) {
                    const deletedComment = await this.exec(
                        this.model.findOne({
                            where: {
                                id: listDeletedComment[i],
                            },
                            attributes: ['id', 'post_id', 'shop_id', 'recruit_id', 'user_id'],
                        })
                    )
                    if (deletedComment) {
                        if (deletedComment.post_id) {
                            const post = await this.exec(
                                Post.findOne({
                                    where: {
                                        id: deletedComment.post_id,
                                    },
                                    attributes: ['id', 'category_id'],
                                })
                            )
                            if (post) {
                                postIds.push(post.id)
                                await this.deleteCommentNotiFunc(deletedComment, post)
                                await this.exec(
                                    Report.destroy({
                                        where: {
                                            post_id: post.id,
                                        },
                                        transaction,
                                    })
                                )
                            }
                        } else if (deletedComment.shop_id) {
                            const shop = await this.exec(
                                Shop.findOne({
                                    where: {
                                        id: deletedComment.shop_id,
                                    },
                                    attributes: ['id', 'title'],
                                })
                            )
                            if (shop) {
                                shopIds.push(shop.id)
                                await this.deleteReviewNotiFunc(deletedComment, shop)
                            }
                        } else if (deletedComment.recruit_id) {
                            const recruit = await this.exec(
                                Recruit.findOne({
                                    where: {
                                        id: deletedComment.recruit_id,
                                    },
                                    attributes: ['id'],
                                    transaction,
                                })
                            )
                            if (recruit) {
                                recruitIds.push(recruit.id)
                                // await this.deleteReviewNotiFunc(deletedComment, shop);
                            }
                        }
                        if (deletedComment.parent_id) {
                            await this.updateLast5Comment(
                                deletedComment.parent_id,
                                transaction
                            )
                        }
                    }
                }
            }

            option.transaction = transaction
            const result = await this.exec(
                this.model.destroy(this.applyDestroyOptions(option))
            )

            // update comment number for shop:
            for (let i = 0; i < shopIds.length; i++) {
                await this.updateCountNumber(
                    shopIds[i],
                    undefined,
                    undefined,
                    transaction
                )
            }

            // update comment number for recruit:
            for (let i = 0; i < recruitIds.length; i++) {
                await this.updateCountNumber(
                    undefined,
                    undefined,
                    recruitIds[i],
                    transaction
                )
            }

            // update comment number for post:
            for (let i = 0; i < postIds.length; i++) {
                await this.updateCountNumber(
                    undefined,
                    postIds[i],
                    undefined,
                    transaction
                )
            }

            transaction.commit()
            return result
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async deleteReviewNotiFunc(item: any, shop: any) {
        await notificationService.sendFCMandCreateNoti(
            item.user_id,
            FCM_ACTIONS_MESSAGE.YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED,
            FCM_ACTIONS.YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED,
            item.id,
            {
                params_1: shop.title,
            }
        )
    }

    async deleteCommentNotiFunc(item: any, post: any) {
        const category = await this.exec(
            Category.findOne({
                where: {
                    id: post.category_id,
                },
                attributes: ['name', 'thema_id'],
            })
        )
        if (category) {
            const thema = await this.exec(
                Thema.findOne({
                    where: {
                        id: category.thema_id,
                    },
                    attributes: ['name'],
                })
            )
            if (thema) {
                await notificationService.sendFCMandCreateNoti(
                    item.user_id,
                    FCM_ACTIONS_MESSAGE.YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED,
                    FCM_ACTIONS.YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED,
                    item.id,
                    {
                        params_1: thema.name,
                        params_2: category.name,
                    }
                )
            }
        }
    }

    async delete(option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.getItem(option), {allowNull: false})
            if (!item.parent_id) {
                // delete parent and child commens:
                await this.exec(
                    this.model.destroy({
                        where: {
                            parent_id: item.id,
                        },
                        transaction,
                    })
                )
            }

            if (item.post_id) {
                await this.exec(
                    Report.destroy({
                        where: {
                            post_id: item.post_id,
                        },
                        transaction,
                    })
                )
            }

            await this.exec(
                this.model.destroy({
                    where: {
                        id: item.id,
                    },
                    transaction,
                })
            )

            if (item.shop_id && item.user_id) {
                // noti for shop here
                const shop = await this.exec(
                    Shop.findOne({
                        where: {
                            id: item.shop_id,
                        },
                        attributes: ['title'],
                        transaction,
                    })
                )

                await this.deleteReviewNotiFunc(item, shop)
            } else if (item.recruit_id && item.user_id) {
                // noti for recruit here
            } else if (item.post_id && item.user_id) {
                // noti for post here
                const post = await this.exec(
                    Post.findOne({
                        where: {
                            id: item.post_id,
                        },
                        attributes: ['category_id'],
                    })
                )

                if (post) {
                    await this.deleteCommentNotiFunc(item, post)
                }
            }

            // UPDATE COUNT NUMBER for shop, post and recruit:
            await this.updateCountNumber(
                item.shop_id,
                item.recruit_id,
                item.post_id,
                transaction
            )

            if (item.parent_id) {
                await this.updateLast5Comment(item.parent_id, transaction)
            }

            transaction.commit()

            return {
                item,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const res = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions({...option, filter: {...option.filter, status: true}})
            )
        )
        return res
    }

    async getListFilter(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params
        const block = await this.exec(
            Block.findAll({
                where: {user_id: user_id},
                attributes: ['user_id_blocked'],
                raw: true,
            })
        )
        const _block = block.map((item: any) => item.user_id_blocked)
        option.include = [
            ...option.include,
            {
                model: User,
                as: 'user',
            },
        ]

        option.filter = {
            ...option.filter,
            user_id: {$notIn: _block},
        }

        option.order = [['created_at_unix_timestamp', 'DESC']]
        const options = this.applyFindOptions({...option, filter: {...option.filter, status: true}})
        options.distinct = true

        const commentData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        )

        for (let i = 0; i < commentData.rows.length; i++) {
            if (
                commentData.rows[i].dataValues.last_5_comments &&
                commentData.rows[i].dataValues.last_5_comments.length
            ) {
                commentData.rows[i].dataValues.replies = commentData.rows[
                    i
                    ].dataValues.last_5_comments.filter(
                    (item: any) =>
                        _block.findIndex((__block: string) => __block === item.user_id) ===
                        -1
                )
            } else {
                commentData.rows[i].dataValues.replies = []
            }
            const foundReport = await this.exec(
                Report.findOne({
                    where: {
                        review_id: commentData.rows[i].id,
                        user_id: params.user_id,
                    },
                    attributes: ['id'],
                })
            )
            commentData.rows[i].dataValues.is_report = !!foundReport
        }

        return commentData
    }

    async getListForShop(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params
        const block = await this.exec(
            Block.findAll({
                where: {user_id: user_id},
                attributes: ['user_id_blocked'],
                raw: true,
            })
        )
        const _block = block.map((item: any) => item.user_id_blocked)

        option.filter = {
            ...option.filter,
            status: true,
            user_id: {$notIn: _block},
        }

        option.order = option.order ? option.order : [['created_at', 'DESC']]
        option.order.push(['review_chilrds', 'created_at', 'DESC'])
        const options = this.applyFindOptions(option)

        const [rows, countReply, count] = await Promise.all([
            this.exec(
                this.modelWithScope(option.scope).findAll({
                    attributes: {
                        include: [
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*) FROM tbl_report WHERE tbl_review.id = tbl_report.review_id\
                   AND tbl_report.deleted_at IS NULL ${
                                        user_id ? `AND tbl_report.user_id = \'${user_id}'\ ` : ''
                                    })`
                                ),
                                'is_report',
                            ],
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*) FROM tbl_favourite_review WHERE tbl_review.id = tbl_favourite_review.review_id\
                   AND tbl_favourite_review.deleted_at IS NULL ${
                                        user_id ? `AND tbl_favourite_review.user_id = \'${user_id}'\ ` : ''
                                    })`
                                ),
                                'is_like',
                            ],
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*) FROM tbl_dislike_review WHERE tbl_review.id = tbl_dislike_review.review_id\
                   AND tbl_dislike_review.deleted_at IS NULL ${
                                        user_id ? `AND tbl_dislike_review.user_id = \'${user_id}'\ ` : ''
                                    })`
                                ),
                                'is_dislike',
                            ],
                        ],
                        exclude: ['last_5_comments'],
                    },
                    include: [
                        {association: 'user', attributes: ['id', 'nickname', 'avatar']},
                        {
                            association: 'review_chilrds',
                            attributes: {
                                include: [
                                    [
                                        sequelize.literal(
                                            `(SELECT COUNT(*) FROM tbl_report WHERE review_chilrds.id = tbl_report.review_id\
          AND tbl_report.deleted_at IS NULL ${
                                                user_id ? `AND tbl_report.user_id = \'${user_id}'\ ` : ''
                                            })`
                                        ),
                                        'is_report',
                                    ],
                                    [
                                        sequelize.literal(
                                            `(SELECT COUNT(*) FROM tbl_favourite_review WHERE review_chilrds.id = tbl_favourite_review.review_id\
          AND tbl_favourite_review.deleted_at IS NULL ${
                                                user_id ? `AND tbl_favourite_review.user_id = \'${user_id}'\ ` : ''
                                            })`
                                        ),
                                        'is_like',
                                    ],
                                    [
                                        sequelize.literal(
                                            `(SELECT COUNT(*) FROM tbl_dislike_review WHERE review_chilrds.id = tbl_dislike_review.review_id\
          AND tbl_dislike_review.deleted_at IS NULL ${
                                                user_id ? `AND tbl_dislike_review.user_id = \'${user_id}'\ ` : ''
                                            })`
                                        ),
                                        'is_dislike',
                                    ],
                                ],
                                exclude: ['last_5_comments'],
                            },
                            required: false,
                            include: [
                                {
                                    association: 'user', required: false
                                }
                            ],
                        },
                        {
                            association: 'feedback_items',
                            attributes: ['icon', 'content'],
                            through: {
                                attributes: [],
                            },
                        },
                    ],
                    where: options.where,
                    limit: options.limit,
                    order: options.order,
                    offset: options.offset,
                })
            ),
            this.modelWithScope(option.scope).findAll({
                attributes: [
                    [
                        sequelize.fn('count', sequelize.col('review_chilrds.id')),
                        'count_reply',
                    ],
                ],
                include: [
                    {
                        association: 'review_chilrds',
                        attributes: [],
                        where: {
                            user_id: {$notIn: _block},
                        },
                        required: false,
                    },
                ],
                group: ['tbl_review.id'],
                subQuery: false,
                where: options.where,
                limit: options.limit,
                // order: options.order,
                offset: options.offset,
                raw: true,
            }),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: options.where as any,
                    include: options.include,
                })
            ),
        ])

        for (let i = 0; i < rows.length; i++) {
            if (Number(rows[i].dataValues.is_report) <= 0 || !user_id) {
                rows[i].dataValues.is_report = false
            } else {
                rows[i].dataValues.is_report = true
            }
            if (Number(rows[i].dataValues.is_like) <= 0 || !user_id) {
                rows[i].dataValues.is_like = false
            } else {
                rows[i].dataValues.is_like = true
            }
            if (Number(rows[i].dataValues.is_dislike) <= 0 || !user_id) {
                rows[i].dataValues.is_dislike = false
            } else {
                rows[i].dataValues.is_dislike = true
            }
            for (const row of rows[i].dataValues.review_chilrds) {
                if (Number(row.dataValues.is_report) <= 0 || !user_id) {
                    row.dataValues.is_report = false
                } else {
                    row.dataValues.is_report = true
                }
                if (Number(row.dataValues.is_like) <= 0 || !user_id) {
                    row.dataValues.is_like = false
                } else {
                    row.dataValues.is_like = true
                }
                if (Number(row.dataValues.is_dislike) <= 0 || !user_id) {
                    row.dataValues.is_dislike = false
                } else {
                    row.dataValues.is_dislike = true
                }
            }
            rows[i].dataValues = {...rows[i].dataValues, ...countReply[i]}
        }

        return {count, rows}
    }

    async getDefaultRepliesComment(
        parentCommentId: String,
        transaction: any = undefined
    ) {
        const childComments = await this.exec(
            this.model.findAll({
                where: {
                    parent_id: parentCommentId,
                    status: true,
                },
                transaction,
                include: [
                    {
                        model: User,
                        as: 'user',
                    },
                ],
                limit: 5,
                offset: 0,
                order: [['created_at_unix_timestamp', 'DESC']],
            })
        )
        return childComments
    }

    async getListIncludeBlock(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id} = params
        const block = await this.exec(
            Block.findAll({
                where: {user_id: user_id},
                attributes: ['user_id_blocked'],
                raw: true,
            })
        )
        const _block = block.map((item: any) => item.user_id_blocked)

        option.filter = {
            ...option.filter,
            user_id: {$notIn: _block},
        }
        const res = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        return res
    }

    async createByAdmin(params: any, option?: ICrudOption) {
        let {post_id} = params
        const transaction = await sequelize.transaction()
        try {
            const user = await this.exec(
                Post.findByPk(post_id, {attributes: ['user_id', 'employee_id']}),
                {allowNull: false}
            )

            if (params && params.parent_id) {
                const item = await this.exec(
                    Review.findOne({
                        where: {
                            id: params.parent_id,
                        },
                        attributes: ['post_id'],
                    })
                )
                post_id = item.post_id
            }
            params.type = REVIEW_TYPE.POST
            if (params && params.parent_id) {
                const parentObj = await this.exec(
                    this.model.findOne({
                        where: {
                            id: params.parent_id,
                        },
                        attributes: ['user_id'],
                        transaction,
                    })
                )
                if (parentObj) {
                    params.parent_user_id = parentObj.user_id
                }
            }

            const comment: any = await this.exec(
                this.model.create(params, {
                    include: [{association: 'feedbacks'}],
                    transaction,
                })
            )

            if (params && params.parent_id) {
                // Send notification to parent

                const parentComment = await this.exec(
                    this.model.findOne({
                        where: {
                            id: params.parent_id,
                        },
                        attributes: ['user_id', 'post_id'],
                        transaction,
                    })
                )
                if (parentComment && parentComment.user_id) {
                    const message = FCM_ACTIONS_MESSAGE.REPLY_COMMENT_POST
                    // firebaseService.sendNotification(
                    //     parentComment.user_id,
                    //     message,
                    //     FCM_ACTIONS.REPLY_COMMENT_POST,
                    //     parentComment.post_id
                    // )

                    const entity_id = parentComment.post_id
                    const bodyNoti: any = {
                        user_id: parentComment.user_id,
                        title: message,
                        content: message,
                        data: {message: message},
                        action: FCM_ACTIONS.REPLY_COMMENT_POST,
                        interacting_type: 'REPLY_COMMENT_POST',
                        interacting_content_id: entity_id,
                    }

                    await this.exec(
                        Notification.create(bodyNoti, {
                            transaction,
                        })
                    )
                }

            } else {
                // send notification to OWNER
                const message = FCM_ACTIONS_MESSAGE.COMMENT_POST

                const action = FCM_ACTIONS.COMMENT_POST
                const entity_id = post_id
                // firebaseService.sendNotification(
                //     user.user_id ? user.user_id : user.employee_id,
                //     message,
                //     action,
                //     entity_id
                // )

                const bodyNoti: any = {
                    user_id: user.user_id,
                    employee_id: user.employee_id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action,
                    interacting_type: 'COMMENT_POST',
                    interacting_content_id: entity_id,
                }

                await this.exec(
                    Notification.create(bodyNoti, {
                        transaction,
                    })
                )
            }

            await this.updateCountNumber(undefined, post_id, undefined, transaction)

            await recordService.increaseCurrentRecordData(
                {
                    reply_count: true,
                },
                transaction
            )

            if (params && params.parent_id) {
                await this.updateLast5Comment(params.parent_id, transaction)
            }

            transaction.commit()
            return {
                comment,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async likeReview(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id
        return await this.onChangeReviewLikeStatus(EXECUTE.LIKE_REVIEW, user_id, id);
    }

    async unlikeReview(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post_id
        return await this.onChangeReviewLikeStatus(EXECUTE.UNLIKE_REVIEW, user_id, id);
    }

    async dislikeReview(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post id
        return await this.onChangeReviewDislikeStatus(
            EXECUTE.DISLIKE_REVIEW,
            user_id,
            id
        );
    }

    async undislikeReview(params: any, option?: ICrudOption) {
        const {user_id} = params;
        const {id} = option.filter; // post_id
        return await this.onChangeReviewDislikeStatus(
            EXECUTE.UNDISLIKE_REVIEW,
            user_id,
            id
        );
    }

    onChangeReviewLikeStatus = async (
        status: string,
        user_id: string,
        review_id: string
    ) => {
        const transaction = await sequelize.transaction();
        try {
            const bodyLP: any = {user_id: user_id, review_id: review_id};

            const tempFind = await this.exec(
                FavouriteReview.findOne({
                    where: bodyLP,
                    attributes: ["id"]
                })
            );
            if (status === EXECUTE.LIKE_REVIEW && tempFind) {
                // call api like when the review is already liked
                throw errorService.database.queryFail(
                    'You have already liked this review!'
                );
            } else if (status === EXECUTE.UNLIKE_REVIEW && !tempFind) {
                // call api unlike when the review not like
                throw errorService.database.queryFail(
                    'You have already unliked this review!'
                );
            }

            const review = await this.exec(this.model.findById(review_id), {
                allowNull: false,
            });

            if (status === EXECUTE.LIKE_REVIEW) {
                // level up activity_3
                // await userService.earningExp(HISTORY.USER.TYPES.LEVEL.TYPES.ACTIVITY_3.NAME, review.user_id, transaction);

                await this.exec(FavouriteReview.create(bodyLP, {transaction}));
            } else {
                await this.exec(FavouriteReview.destroy({
                    where: {
                        id: tempFind.id
                    },
                    transaction
                }));
            }

            const like_count = await this.exec(
                FavouriteReview.count({
                    where: {
                        review_id,
                    },
                    transaction
                })
            );

            await this.exec(
                this.model.update(
                    {
                        like: like_count,
                    },
                    {
                        where: {
                            id: review_id,
                        },
                        transaction: transaction,
                    }
                )
            );

            review.like = like_count;

            transaction.commit();
            return {
                review,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    };

    onChangeReviewDislikeStatus = async (
        status: string,
        user_id: string,
        review_id: string
    ) => {
        const transaction = await sequelize.transaction();
        try {
            const bodyDP: any = {user_id: user_id, review_id: review_id};

            const tempFind = await this.exec(
                DislikeReview.findOne({
                    where: bodyDP,
                    attributes: ["id"]
                })
            );
            if (status === EXECUTE.DISLIKE_REVIEW && tempFind) {
                // call api like when the review is already liked
                throw errorService.database.queryFail(
                    'You have already disliked this review!'
                );
            } else if (status === EXECUTE.UNDISLIKE_REVIEW && !tempFind) {
                // call api unlike when the review not like
                throw errorService.database.queryFail(
                    'You have already undisliked this review!'
                );
            }

            if (status === EXECUTE.DISLIKE_REVIEW) {
                await this.exec(DislikeReview.create(bodyDP, {transaction}));
            } else {
                await this.exec(tempFind.destroy());
            }

            const review = await this.exec(this.model.findById(review_id), {
                allowNull: false,
            });

            const dislike_count =
                status === EXECUTE.DISLIKE_REVIEW ? review.dislike + 1 : review.dislike - 1;
            review.dislike = dislike_count;

            await this.exec(
                this.model.update(
                    {
                        dislike: dislike_count,
                    },
                    {
                        where: {
                            id: review.id,
                        },
                        transaction: transaction,
                    }
                )
            );

            transaction.commit();
            return {
                review,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    };

    async scheduleCongratulation() {
        const startOfWeek = moment().utc().add(9, 'hour').startOf('week').subtract(9, 'hour').toDate();
        const endOfWeek = moment().utc().add(9, 'hour').endOf('week').subtract(9, 'hour').toDate();
        const reviews = await FavouriteReview.findAll(
            {
                where: {
                    created_at: {
                        [Op.between]: [startOfWeek, endOfWeek]
                    }
                },
                include: [{
                    association: "review",
                    where: {
                        id: {
                            [Op.not]: null
                        }
                    },
                }],
                attributes: ['review_id', [sequelize.fn('count', sequelize.col('review_id')), 'count']],
                group: ['review_id'],
                order: [[sequelize.literal('count'), 'DESC']] as any,
                limit: 1
            }
        )
        const [review] = reviews;
        if (review) {
            const message = FCM_ACTIONS_MESSAGE.CONGRATULATION_COMMENT
            const bodyNoty: any = {
                user_id: review.dataValues.review.user_id,
                title: message,
                content: message,
                data: {message: message},
                action: Number(FCM_ACTIONS.CONGRATULATION_COMMENT),
                interacting_type: 'CONGRATULATION_COMMENT',
                interacting_content_id: review.dataValues.review.id,
            }
            await Promise.all([
                Notification.create(bodyNoty),
                // firebaseService.sendNotification(review.dataValues.review.user_id, message, FCM_ACTIONS.CONGRATULATION_COMMENT, review.dataValues.review.id)
            ])
        }
    }

    async countByUser(user_id: string) {
        const [totalReview , totalComment] = await Promise.all([
            this.model.count({
                where: {
                    user_id ,
                    shop_id : {
                        [Op.not]: null
                    }
                }
            }),
            this.model.count({
                where: {
                    user_id,
                    post_id : {
                        [Op.not]: null
                    }
                }
            })
        ])

        return {
            totalReview,
            totalComment
        }

    }
}
