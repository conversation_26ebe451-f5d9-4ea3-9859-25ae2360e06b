import { CrudService, ICrudOption } from '../crudService.pg'
import { Favourite } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'

import { tokenService, firebaseService, errorService } from '@/services'
import { calcDistance } from '../socketService/util'
import { REVIEW_TYPE } from '@/const'
export class FavouriteService extends CrudService<typeof Favourite> {
  constructor() {
    super(Favourite)
  }

  async getUserFavouriteShop(userId: string) {
    if (userId) {
      return await this.exec(
        this.model.findAll({
          attributes: ['id', 'shop_id'],
          where: {
            user_id: userId,
            type: REVIEW_TYPE.SHOP,
          },
        })
      )
    }
    return []
  }
  async getListFavourite(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    const {latitude, longitude} = params

    const options = this.applyFindOptions(option)
    options.distinct = true
    const listData = await this.exec(
      this.modelWithScope(option.scope).findAndCountAll(options)
    )
    listData.total_rows = listData.count

    if (
      listData &&
      listData.rows &&
      listData.rows[0] &&
      listData.rows[0].shop
    ) {
      for (let i = 0; i < listData.rows.length; i++) {
        const shop =
          listData &&
          listData.rows[i] &&
          listData.rows[i].dataValues &&
          listData.rows[i].dataValues.shop &&
          listData.rows[i].dataValues.shop.dataValues
            ? listData.rows[i].dataValues.shop.dataValues
            : undefined
        if (shop) {
          shop.is_like = true

          if (latitude && longitude) {
            shop.distance = calcDistance(
              {
                latitude: latitude,
                longitude: longitude,
              },
              {
                latitude: shop.latitude,
                longitude: shop.longitude,
              }
            ) // Distance in km
          }
        }
      }
    }

    return listData
  }
}
