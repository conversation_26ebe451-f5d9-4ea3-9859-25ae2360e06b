import { CrudService, ICrudOption } from '../crudService.pg'
import { firebaseService } from '@/services'
import { PushNotification } from '@/models/tables'
import { Op } from 'sequelize'
import moment = require('moment')
import { PUSH_NOTIFICATION_TYPE } from '@/const'

export class PushNotificationService extends CrudService<
  typeof PushNotification
> {
  constructor() {
    super(PushNotification)
  }
  async sendFcm(pushNotification: any) {
    if (pushNotification.type === 'USER') {
      this.sendFcmToUser(pushNotification)
    } else if (pushNotification.type === 'GROUP') {
      this.sendFcmToUser(pushNotification)
    }
  }
  async sendFcmToUser(pushNotification: any) {
    const { id, user_id, message_title, message_content } = pushNotification

    const user_ids = []
    user_ids.push('main')
    const fcmAction = '0'
    // await firebaseService.sendNotification("main", message_title, fcmAction);
    // await firebaseService.sendNotificationV2(
    //   user_ids,
    //   message_title,
    //   message_content,
    //   fcmAction
    // )
    if (pushNotification.frequency === 'ONE_TIME') {
      await this.exec(
        this.model.destroy({
          where: {
            id: pushNotification.id,
          },
        })
      )
    }
    return true
  }
  async sendFcmToGroup(pushNotification: any) {
    const { id, message_title, message_content } = pushNotification
    const user_ids = []
    user_ids.push('main')
    const fcmAction = '0'
    // await firebaseService.sendNotificationV2(
    //   user_ids,
    //   message_title,
    //   message_content,
    //   fcmAction
    // )
    return true
  }
  async pushNotification() {
    try {
      const now = moment().startOf('minute')
      const time = now.format('HH:mm')

      const notifications = await this.model.findAll({
        where: {
          status: true,
          [Op.or]: [
            {
              time,
              frequency: { [Op.not]: PUSH_NOTIFICATION_TYPE.ONE_TIME },
            },
            {
              sending_unix_timestamp: now.valueOf(),
              frequency: PUSH_NOTIFICATION_TYPE.ONE_TIME,
            },
          ],
        },
      })
      if (notifications.length) {
        for (const notification of notifications) {
          const { id, message_title, message_content, sending_unix_timestamp } =
            notification
          const user_ids = []
          user_ids.push('main')
          const fcmAction = '0'
          // firebaseService.sendNotificationV2(
          //   user_ids,
          //   message_title,
          //   message_content,
          //   fcmAction
          // )
          if (notification.frequency === PUSH_NOTIFICATION_TYPE.ONE_TIME) {
            // firebaseService.sendNotificationV2(
            //   user_ids,
            //   message_title,
            //   message_content,
            //   fcmAction
            // )
            this.exec(
              this.model.destroy({
                where: {
                  id,
                },
              })
            )
          } else {
            const timeDiff = now.diff(
              moment(Number(sending_unix_timestamp)),
              'days'
            )
            // if (
            //   (notification.frequency === PUSH_NOTIFICATION_TYPE['7_DAYS'] &&
            //     timeDiff % 7 === 0) ||
            //   (timeDiff % 30 === 0 &&
            //     notification.frequency === PUSH_NOTIFICATION_TYPE['30_DAYS'])
            // ) {
            //   firebaseService.sendNotificationV2(
            //     user_ids,
            //     message_title,
            //     message_content,
            //     fcmAction
            //   )
            // }
          }
        }
      }
    } catch (error) {
      console.log(error)
    }
  }
}
