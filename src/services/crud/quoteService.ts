// @ts-ignore
import { Op } from "sequelize";
import { CrudService, ICrudOption } from "../crudService.pg";
import { JobRequest, Quote } from "@/models";
import { config } from "@/config";
import { errorService } from "@/services";

export class QuoteService extends CrudService<typeof Quote> {
  constructor() {
    super(Quote);
  }
  async getListV2(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const { user_id: currentUserId, mode } = params;

    const quoteFilter: any = {};
    const include: any[] = [];

    if (option.filter && option.filter.user_id) {
      quoteFilter.user_id = option.filter.user_id;
      delete option.filter.user_id;
    }

    if (option.filter && option.filter.job_id) {
      quoteFilter.job_id = option.filter.job_id;
      delete option.filter.job_id;
    }

    if (option.filter && option.filter.is_confirmed) {
      quoteFilter.is_confirmed = option.filter.is_confirmed;
      delete option.filter.is_confirmed;
    }

    //filter by job.category
    const jobInclude: any = {
      association: "job",
      required: true,
    };

    if (option.filter && option.filter.category) {
      jobInclude.where = {
        category: option.filter.category,
      };
      delete option.filter.category;
    }

    include.push(jobInclude);

    // include user
    include.push({
      association: "user",
      required: false,
    });

    option.filter = quoteFilter;
    option.include = include;
    option.attributes = undefined;

    return await this.exec(
      this.modelWithScope(option.scope).findAndCountAll(
        this.applyFindOptions(option)
      )
    );
  }

  async getListRelated(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const { user_id: currentUserId, mode } = params;
   

    const quoteWhere: any = {};
    const jobWhere: any = {
      user_id: currentUserId,
    };
     if (option.filter) {
      if (option.filter.category) {
         jobWhere.category = option.filter.category;
      }      
      if (option.filter.is_confirmed !== undefined) {
        quoteWhere.is_confirmed = option.filter.is_confirmed
      }
      if (option.filter.job_id) {
        quoteWhere.job_id = option.filter.job_id;
      }
    }

    return await this.model.findAndCountAll({
      ...option,
      where: quoteWhere,
      include: [
        {
          model: JobRequest,
          where: jobWhere,
          required: true,
          as: 'job',
        },
      ],
    });
  }

  async toggleConfirmQuote(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const { user_id, quote_id, is_confirmed } = params;

    const quote = (await this.model.findOne({
      where: { id: quote_id },
      include: [
        {
          association: "job",
        },
      ],
    })) as any;

    if (!quote) {
      throw errorService.database.recordNotFound();
    }

    if (!quote.job || quote.job.user_id !== user_id) {
      throw errorService.auth.permissionDeny(
        "You do not have permission to confirm this quote"
      );
    }

    quote.is_confirmed = is_confirmed;
    await quote.save();
    return quote;
  }
}
