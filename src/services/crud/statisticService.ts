import { CrudService, ICrudOption } from '../crudService.pg';
import { User, Record } from '@/models/tables';
import * as moment from 'moment';
import { sequelize, Sequelize } from '@/models';
import { errorService } from '..';
export const START_WEEK_DAY = 0;
export const END_WEEK_DAY = 6;

function getMonthDateRange(year: any, month: any) {
  // month in moment is 0 based, so 9 is actually october, subtract 1 to compensate
  // array is 'year', 'month', 'day', etc
  const startDate = moment([year, month - 1]);

  // Clone the value before .endOf()
  const endDate = moment(startDate).endOf('month');

  // just for demonstration:
  console.log(startDate.toDate());
  console.log(endDate.toDate());

  // make sure to call toDate() for plain JavaScript date type
  return { start: startDate, end: endDate };
}

export class StatisticService extends CrudService<any> {
  constructor() {
    super(undefined);
  }
  async statisticDashboardChart(params: any, option?: ICrudOption) {
   
  }
 
  splitTimeMonth(from_date: any, end_date: any, timezone = -1) {
    if (timezone === -1) {
      timezone = 9;
    }

    const time = [];
    while (new Date(from_date).valueOf() <= new Date(end_date).valueOf()) {
      const month = moment
        .utc(from_date)
        .utcOffset(timezone * 60)
        .format('YYYY-MM');
      time.push(month);
      const temp = moment.utc(from_date).utcOffset(timezone * 60);
      temp.set('date', 1);
      temp.set('hour', 1);
      temp.set('month', temp.get('month') + 1);
      from_date = temp.format();
    }
    return time;
  }
  splitTimeDay(from_date: any, end_date: any, timezone = -1) {
    if (timezone === -1) {
      timezone = 9;
    }

    const time = [];
    while (new Date(from_date).valueOf() <= new Date(end_date).valueOf()) {
      const date = moment
        .utc(from_date)
        .utcOffset(timezone * 60)
        .format('YYYY-MM-DD');
      time.push(date);
      const temp = moment.utc(from_date).utcOffset(timezone * 60);
      temp.set('date', temp.get('date') + 1);
      from_date = temp.format();
    }

    return time;
  }
  splitTimeHour(start_date: any, end_date: any, timezone = -1) {
    if (timezone === -1) {
      timezone = 9;
    }
    const times = [];

    let startDateInTimestamp = start_date.valueOf() - timezone * 3600 * 1000;
    const endDateInTimestamp = end_date.valueOf() - timezone * 3600 * 1000;
    while (startDateInTimestamp <= endDateInTimestamp) {
      // const date = moment(start_date).format();
      times.push(
        moment
          .utc(startDateInTimestamp)
          .utcOffset(timezone * 60)
          .format()
      );
      // const temp = moment(start_date.valueOf() + 3600 * 1000);
      // // temp.set('hour', temp.get('hour') + 1);
      startDateInTimestamp += 3600 * 1000;
    }
    return times;
  }
  async statisticVisitor(params: any, option?: ICrudOption) {
    const { type, type_of_month, value_of_day, area_type } = params;

    let timezone = params.timezone;
    if (!timezone) {
      timezone = 9;
    }
    let amountMonth = 0;
    const date = moment(Date.now()).format('YYYY-MM-DD');
    let times;
    if (type === 'month' && type_of_month) {
      if (type_of_month === '12_month') {
        amountMonth = 12;
      }
      if (type_of_month === '6_month') {
        amountMonth = 6;
      }
      if (type_of_month === '3_month') {
        amountMonth = 3;
      }
      if (type_of_month === '1_month') {
        amountMonth = 1;
      }
      const from_date_in_this_year = moment(date)
        .startOf('month')
        .add(-amountMonth + 1, 'month')
        .format('YYYY-MM-DD');
      const to_date_in_this_year = moment(date)
        .endOf('month')
        .format('YYYY-MM-DD');
      console.log(
        'from ' + from_date_in_this_year + ' to ' + to_date_in_this_year
      );
      times = this.splitTimeMonth(
        from_date_in_this_year,
        to_date_in_this_year,
        timezone
      );
    }
    if (type === 'day' && value_of_day) {
      times = this.splitTimeDay(
        value_of_day.from_date,
        value_of_day.to_date,
        timezone
      );
    }
    const visitor_chart_domestic: any = [];
    const visitor_chart_oversea: any = [];
    if (area_type === 'domestic' || area_type === 'total') {
      console.log('@@@ 1', area_type);
      for (const time of times) {
        const start = moment
          .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
          .utcOffset(timezone * 60);
        const end = moment
          .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
          .utcOffset(timezone * 60);

        if (type === 'month') {
          end.set('month', end.get('month') + 1);
          end.set('date', 0);
          end.set('hour', 23);
          end.set('minute', 59);
          end.set('second', 59);
        }
        if (type === 'day') {
          end.set('date', end.get('date') + 1);
        }
        let male = await User.count({
          where: {
            sign_in_time_unix_timestamp: {
              $gte: start.valueOf(),
              $lte: end.valueOf(),
            },
            sex: 'MALE',
            nationality: 'ko',
          },
        });
        if (!male) male = 0;
        let female = await User.count({
          where: {
            sign_in_time_unix_timestamp: {
              $gte: start.valueOf(),
              $lte: end.valueOf(),
            },
            sex: { $ne: 'MALE' },
            nationality: 'ko',
          },
        });
        if (!female) female = 0;
        const visitor = male + female;
        console.log('bambi log thu xem time', start.format(), end.format());
        console.log('bambi log thu xem visitor', visitor);
        let index = 0;
        for (let i = 0; i < visitor_chart_domestic.length; i++) {
          index++;
        }
        visitor_chart_domestic.push({
          index: index,
          time: time,
          visitor,
          male,
          female,
        });
      }
    }

    if (area_type === 'overseas' || area_type === 'total') {
      console.log('@@@ 2', area_type);

      for (const time of times) {
        const start = moment
          .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
          .utcOffset(timezone * 60);
        const end = moment
          .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
          .utcOffset(timezone * 60);
        if (type === 'month') {
          end.set('month', end.get('month') + 1);
          end.set('date', 0);
          end.set('hour', 23);
          end.set('minute', 59);
          end.set('second', 59);
        }
        if (type === 'day') {
          end.set('date', end.get('date') + 1);
        }
        let male = await User.count({
          where: {
            sign_in_time_unix_timestamp: {
              $gte: start.valueOf(),
              $lte: end.valueOf(),
            },
            sex: 'MALE',
            $or: [
              {
                nationality: { $ne: 'ko' },
              },
              {
                nationality: null,
              },
            ],
          },
        });

        if (!male) male = 0;
        let female = await User.count({
          where: {
            sign_in_time_unix_timestamp: {
              $gte: start.valueOf(),
              $lte: end.valueOf(),
            },
            sex: { $ne: 'MALE' },
            $or: [
              {
                nationality: { $ne: 'ko' },
              },
              {
                nationality: null,
              },
            ],
          },
        });
        if (!female) female = 0;
        const visitor = male + female;

        console.log(
          'bambi log thu xem time oversea',
          start.format(),
          end.format()
        );
        console.log('bambi log thu xem visitor oversea', visitor);

        let index = 0;
        for (let i = 0; i < visitor_chart_oversea.length; i++) {
          index++;
        }
        visitor_chart_oversea.push({
          index: index,
          time: time,
          visitor,
          male,
          female,
        });
      }
    }

    let results = {};
    if (area_type === 'total') {
      const visitor_chart_all: any[] = [];
      visitor_chart_domestic.forEach((elementDomestic: any) => {
        visitor_chart_oversea.forEach((elementOverSea: any) => {
          if (elementDomestic.time === elementOverSea.time) {
            visitor_chart_all.push({
              index: elementDomestic.index,
              time: elementDomestic.time,
              visitor: elementDomestic.visitor + elementOverSea.visitor,
              male: elementDomestic.male + elementOverSea.male,
              female: elementDomestic.female + elementOverSea.female,
            });
          }
        });
      });

      results = {
        statistic: visitor_chart_all,
        domestic_statistic: visitor_chart_domestic,
        oversea_statistic: visitor_chart_oversea,
      };
    } else if (area_type === 'domestic') {
      results = {
        statistic: visitor_chart_domestic,
      };
    } else if (area_type === 'overseas') {
      results = {
        statistic: visitor_chart_oversea,
      };
    }

    return results;
  }

  async statisticVisitorPageView(params: any, option?: ICrudOption) {
    const { type, type_of_month, value_of_day, area_type } = params;
    const date = moment(Date.now()).format('YYYY-MM-DD');

    const IS_AVERAGE = area_type && area_type.includes('average');
    console.log(
      '29148 : StatisticService -> statisticVisitorPageView -> IS_AVERAGE',
      IS_AVERAGE
    );
    const IS_VISITOR = area_type && area_type.includes('visitor');
    console.log(
      '29148 : StatisticService -> statisticVisitorPageView -> IS_VISITOR',
      IS_VISITOR
    );
    const statistic: any = [];
    if (IS_AVERAGE) {
      const records = await this.exec(
        Record.findAll({
          order: [['created_at_unix_timestamp', 'DESC']],
        })
      );
      let year;
      let month;
      let count = 0;
      let dayNumberOfMonth;

      const onResetData = (newRecord: any) => {
        count = 0;
        dayNumberOfMonth = moment(
          parseInt(newRecord.created_at_unix_timestamp)
        ).daysInMonth();
        month = moment(parseInt(newRecord.created_at_unix_timestamp)).month();
        year = moment(parseInt(newRecord.created_at_unix_timestamp)).year();
      };

      for (let i = 0; i < records.length; i++) {
        ////// RESET DATA:
        if (
          year !==
            moment(parseInt(records[i].created_at_unix_timestamp)).year() ||
          i === 0
        ) {
          // change year => RESET:
          onResetData(records[i]);
          // add to array:
          statistic.push({
            year,
            data: [],
            monthly_average: 0,
            daily_average: 0,
            total: 0,
          });
        }
        if (
          month !==
            moment(parseInt(records[i].created_at_unix_timestamp)).month() ||
          i === 0
        ) {
          // change month => RESET:
          onResetData(records[i]);
          // add to array:
          statistic[statistic.length - 1].data.push({
            month,
            number: count,
            average: count > 0 ? count / dayNumberOfMonth : 0,
          });
        }

        ////
        year = moment(parseInt(records[i].created_at_unix_timestamp)).year();
        month = moment(parseInt(records[i].created_at_unix_timestamp)).month();
        dayNumberOfMonth = moment(
          parseInt(records[i].created_at_unix_timestamp)
        ).daysInMonth();

        count += IS_VISITOR
          ? records[i].visitor_count
          : records[i].page_view_count;

        // Update number count of each month:
        statistic[statistic.length - 1].data[
          statistic[statistic.length - 1].data.length - 1
        ].number = count;

        // Update average:
        statistic[statistic.length - 1].data[
          statistic[statistic.length - 1].data.length - 1
        ].average = count > 0 ? Math.round(count / dayNumberOfMonth) : 0;

        // Get total count number in year:
        let totalInYear = 0;
        for (let j = 0; j < statistic[statistic.length - 1].data.length; j++) {
          totalInYear += statistic[statistic.length - 1].data[j].number;
        }

        // Update total:
        statistic[statistic.length - 1].total = totalInYear;

        // Update monthly average:
        statistic[statistic.length - 1].monthly_average = Math.round(
          totalInYear / 12
        );

        // Update daily average:
        statistic[statistic.length - 1].daily_average = Math.round(
          totalInYear / 365
        );

        console.log('29148 year, month ne:', year, month, count);
      }
    } else {
      let timezone = params.timezone;
      if (!timezone) {
        timezone = 9;
      }
      let times;
      value_of_day.from_date = moment(value_of_day.from_date, 'YYYY-MM-DD')
        .weekday(START_WEEK_DAY)
        .format('YYYY-MM-DD');
      value_of_day.to_date = moment(value_of_day.to_date, 'YYYY-MM-DD')
        .weekday(END_WEEK_DAY)
        .format('YYYY-MM-DD');
      if (type === 'day' && value_of_day) {
        times = this.splitTimeDay(
          value_of_day.from_date,
          value_of_day.to_date,
          timezone
        );
      }
      const calc_chart: any = [];
      console.log(
        '29148 binh ne from ' +
          value_of_day.from_date +
          ' to ' +
          value_of_day.to_date
      );
      console.log(
        '29148 : StatisticService -> statisticVisitorPageView -> value_of_day.from_date',
        value_of_day.from_date
      );
      console.log('@@@ 1', area_type);
      console.log('@@@ 1.5: ', times);
      let checkingWeek = 0;
      let count = 0;
      for (const time of times) {
        console.log('@@@ 2 ', time);

        const start = moment
          .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
          .utcOffset(timezone * 60);
        const end = moment
          .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
          .utcOffset(timezone * 60);
        if (!checkingWeek || start.day() === START_WEEK_DAY) {
          console.log('29148 chuan bi reset week ne:', start);
          checkingWeek++;
          count = 0;
          statistic.push({
            week: checkingWeek,
            data: [],
            difference: 0,
          });
        }
        if (type === 'month') {
          end.set('month', end.get('month') + 1);
          end.set('date', 0);
          end.set('hour', 23);
          end.set('minute', 59);
          end.set('second', 59);
        }
        if (type === 'day') {
          end.set('date', end.get('date') + 1);
        }
        console.log('@@@ 3 ', type);

        const record = await this.exec(
          Record.findOne({
            where: {
              created_at_unix_timestamp: {
                $gte: start.valueOf(),
                $lte: end.valueOf(),
              },
            },
            attributes: ["visitor_count", "page_view_count"],
          })
        );
        // calc_chart
        console.log('bambi log thu xem time', start.format(), end.format());
        console.log('29148 record ne: ', record);

        let index = 0;
        for (let i = 0; i < calc_chart.length; i++) {
          index++;
        }

        if (record) {
          count += IS_VISITOR ? record.visitor_count : record.page_view_count;
          statistic[statistic.length - 1].data.push({
            time: moment(time, 'YYYY-MM-DD').format('MM-DD'),
            number: IS_VISITOR ? record.visitor_count : record.page_view_count,
          });
        } else {
          statistic[statistic.length - 1].data.push({
            time: moment(time, 'YYYY-MM-DD').format('MM-DD'),
            number: 0,
          });
        }
        statistic[statistic.length - 1].number = count;
        statistic[statistic.length - 1].average = Math.round(count / 7);
        if (statistic && statistic.length > 1) {
          const percent =
            (statistic[statistic.length - 1].number * 100) /
            statistic[statistic.length - 2].number;
          statistic[statistic.length - 1].difference =
            percent > 0 ? Math.round(percent - 100) : 0;
        }
      }
    }

    return statistic;
  }

  async statisticTraffic(params: any, option?: ICrudOption) {
    const { type, type_of_month, value_of_day, area_type } = params;
    const visitor_chart: any = [];

    let timezone = params.timezone;
    if (!timezone) {
      timezone = 9;
    }
    let amountMonth = 0;
    const date = moment(Date.now()).format('YYYY-MM-DD');
    let times;
    let monthTimes;

    if (type === 'month' && type_of_month) {
      if (type_of_month === '12_month') {
        amountMonth = 12;
      }
      if (type_of_month === '6_month') {
        amountMonth = 6;
      }
      if (type_of_month === '3_month') {
        amountMonth = 3;
      }
      if (type_of_month === '1_month') {
        amountMonth = 1;
      }

      const from_date_in_this_year = moment(date)
        .startOf('month')
        .add(-amountMonth + 1, 'month')
        .format('YYYY-MM-DD');
      const to_date_in_this_year = moment(date)
        .endOf('month')
        .format('YYYY-MM-DD');
      console.log(
        'from ' + from_date_in_this_year + ' to ' + to_date_in_this_year
      );
      times = this.splitTimeMonth(
        from_date_in_this_year,
        to_date_in_this_year,
        timezone
      );
      monthTimes = this.splitTimeMonth(
        from_date_in_this_year,
        to_date_in_this_year,
        timezone
      );

      // get data foreach months:
      for (let i = monthTimes.length - 1; i >= 0; i--) {
        const dateRange = getMonthDateRange(
          parseInt(monthTimes[i].split('-')[0]),
          parseInt(monthTimes[i].split('-')[1])
        );
        const dayTimes = this.splitTimeDay(
          dateRange.start,
          dateRange.end,
          timezone
        );

        const monthData: any = {
          index: monthTimes.length - i - 1,
          month: `${
            dayTimes[0]
              ? moment
                  .utc(
                    moment.utc(dayTimes[0]).valueOf() - timezone * 3600 * 1000
                  )
                  .utcOffset(timezone * 60)
                  .format('YYYY-MM')
              : ''
          }`,
          data: [],
          total: 0,
        };

        for (const time of dayTimes) {
          console.log('@@@ 2 ', time);

          const start = moment
            .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
            .utcOffset(timezone * 60);
          const end = moment
            .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
            .utcOffset(timezone * 60);

          end.set('date', end.get('date') + 1);

          console.log('@@@ 3 ', type);

          const records = await this.exec(
            Record.findAll({
              where: {
                created_at_unix_timestamp: {
                  $gte: start.valueOf(),
                  $lte: end.valueOf(),
                },
              },
            })
          );
          // visitor_chart
          let index = 0;
          for (let i = 0; i < monthData.data.length; i++) {
            index++;
          }
          let visitor_count = 0;
          for (let i = 0; i < records.length; i++) {
            visitor_count +=
              records[i] && records[i].visitor_count
                ? records[i].visitor_count
                : 0;
          }
          monthData.total += visitor_count;
          monthData.data.push({
            index: index,
            date: start.format('YYYY-MM-DD'),
            // time: time,
            visitor_count,
          });
        }
        visitor_chart.push(monthData);
      }
    }
    const results = {
      statistic: visitor_chart,
    };

    return results;
  }

  async statisticByPeriod(params: any, option?: ICrudOption) {
    const { type, type_of_month, value_of_day, year } = params;

    let timezone = params.timezone;
    if (!timezone) {
      timezone = 9;
    }
    const amountMonth = 0;
    const date = moment(Date.now()).format('YYYY-MM-DD');
    let times;

    if (type === 'day' && value_of_day) {
      times = this.splitTimeDay(
        value_of_day.from_date,
        value_of_day.to_date,
        timezone
      );
    }
    if (type === 'month' && year) {
      const from_date_in_this_year = moment()
        .startOf('year')
        .set('year', year)
        .format('YYYY-MM-DD');
      const to_date_in_this_year = moment()
        .endOf('year')
        .set('year', year)
        .format('YYYY-MM-DD');
      console.log(
        'from ' + from_date_in_this_year + ' to ' + to_date_in_this_year
      );
      times = this.splitTimeMonth(
        from_date_in_this_year,
        to_date_in_this_year,
        timezone
      );
    }

    const periodChart: any = [];

    console.log('@@@ 1.5: ', times);

    for (const time of times) {
      console.log('@@@ 2 ', time);

      const start = moment
        .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
        .utcOffset(timezone * 60);
      const end = moment
        .utc(moment.utc(time).valueOf() - timezone * 3600 * 1000)
        .utcOffset(timezone * 60);

      if (type === 'month') {
        end.set('month', end.get('month') + 1);
        end.set('date', 0);
        end.set('hour', 23);
        end.set('minute', 59);
        end.set('second', 59);
      }
      if (type === 'day') {
        end.set('date', end.get('date') + 1);
      }
      console.log('@@@ 3 ', type);

      const records = await this.exec(
        Record.findAll({
          where: {
            created_at_unix_timestamp: {
              $gte: start.valueOf(),
              $lte: end.valueOf(),
            },
          },
        })
      );
      console.log('bambi log thu xem time', start.format(), end.format());
      let index = 0;
      for (let i = 0; i < periodChart.length; i++) {
        index++;
      }
      let page_view_count = 0;
      let visitor_count = 0;
      let new_member_count = 0;
      let partnership_inquiry_count = 0;
      let new_post_count = 0;
      let reply_count = 0;
      for (let i = 0; i < records.length; i++) {
        page_view_count +=
          records[i] && records[i].page_view_count
            ? records[i].page_view_count
            : 0;
        visitor_count +=
          records[i] && records[i].visitor_count ? records[i].visitor_count : 0;
        new_member_count +=
          records[i] && records[i].new_member_count
            ? records[i].new_member_count
            : 0;
        partnership_inquiry_count +=
          records[i] && records[i].partnership_inquiry_count
            ? records[i].partnership_inquiry_count
            : 0;
        new_post_count +=
          records[i] && records[i].new_post_count
            ? records[i].new_post_count
            : 0;
        reply_count +=
          records[i] && records[i].reply_count ? records[i].reply_count : 0;
      }
      periodChart.push({
        index: index,
        time: time,
        page_view_count,
        visitor_count,
        new_member_count,
        partnership_inquiry_count,
        new_post_count,
        reply_count,
      });
    }

    const results = {
      statistic: periodChart,
    };

    return results;
  }
}
