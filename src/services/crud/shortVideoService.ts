import { CrudService, ICrudOption } from '../crudService.pg'
import { ShortVideo, FavouriteShortVideo } from '@/models/tables'
import { sequelize } from '@/models'
import * as fs from 'fs'
import { config } from '@/config'

const LIKE_STATUS = {
  LIKE: true,
  UNLIKE: false,
}

const VIDEO_PATH = 'video/'

export class ShortVideoService extends CrudService<typeof ShortVideo> {
  constructor() {
    super(ShortVideo)
  }

  async settingIndex(params: any, option?: ICrudOption) {
    const {id} = option.filter
    const {action} = params
    const transaction = await sequelize.transaction()
    const ACTION = {
      MOVE_TOP: 'MOVE_TOP',
      MOVE_BOTTOM: 'MOVE_BOTTOM',
      FORWARD: 'FORWARD',
    }
    try {
      const item = await this.exec(ShortVideo.findOne({where: {id: id}}), {
        allowNull: false,
      })

      let subIndex = 0

      switch (action) {
        case ACTION.MOVE_TOP: {
          const minIndex = await this.exec(
            ShortVideo.findOne({
              order: [[sequelize.fn('min', sequelize.col('index')), 'ASC']],
              group: ['tbl_short_video.id'],
            }),
            {allowNull: false}
          )
          subIndex = getRandomArbitrary(0, minIndex.index)
          break
        }
        case ACTION.MOVE_BOTTOM: {
          const maxIndex = await this.exec(
            ShortVideo.findOne({
              order: [[sequelize.fn('max', sequelize.col('index')), 'DESC']],
              group: ['tbl_short_video.id'],
            }),
            {allowNull: false}
          )
          subIndex = getRandomArbitrary(maxIndex.index, maxIndex.index + 1)
          break
        }
        case ACTION.FORWARD: {
          const subItem = await this.exec(
            ShortVideo.findOne({
              where: {index: {$lt: item.index}},
              order: [sequelize.fn('max', sequelize.col('index'))],
              group: ['tbl_short_video.id'],
            }),
            {allowNull: false}
          )

          const newIndex = getRandomArbitrary(subItem.index, item.index)

          subIndex = subItem.index
          await subItem.update({index: newIndex}, {transaction})
          break
        }
        default: {
          const subItem = await this.exec(
            ShortVideo.findOne({
              where: {index: {$gt: item.index}},
              order: [sequelize.fn('min', sequelize.col('index'))],
              group: ['tbl_short_video.id'],
            }),
            {allowNull: false}
          )
          const newIndex = getRandomArbitrary(item.index, subItem.index)
          subIndex = subItem.index
          await subItem.update({index: newIndex}, {transaction})
          break
        }
      }

      await item.update({index: subIndex}, {transaction})

      await transaction.commit()
      return {
        is_success: true,
      }
    } catch (err) {
      transaction.rollback()
      throw err
    }
  }

  async getList(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    const shortVideos = await this.exec(
      this.modelWithScope(option.scope).findAndCountAll(
        this.applyFindOptions(option)
      )
    )
    for (let i = 0; i < shortVideos.rows.length; i++) {
      const shortVideo = shortVideos.rows[i].dataValues
      shortVideos.rows[i].dataValues.is_like = false
      if (params.user_id) {
        const is_like = await this.exec(
          FavouriteShortVideo.findOne({
            where: {user_id: params.user_id, short_video_id: shortVideo.id},
          })
        )
        shortVideos.rows[i].dataValues.is_like = !!is_like
      }
    }
    return shortVideos
  }

  async update(params: any, option?: ICrudOption) {
    const item = await this.exec(this.model.findById(option.filter.id), {
      allowNull: false,
    })
    this.removeOldVideo(item)

    await this.exec(item.update(params))
    return await this.getItem(option)
  }

  async delete(option?: ICrudOption) {
    const item = await this.exec(this.getItem(option), {allowNull: false})
    this.removeOldVideo(item)

    return await this.exec(item.destroy())
  }

  async likeShortVideo(params: any, option?: ICrudOption) {
    const {user_id} = params
    const {id} = option.filter
    return await this.onChangeShortVideoLikeStatus(
      user_id,
      id,
      LIKE_STATUS.LIKE
    )
  }
  async unlikeShortVideo(params: any, option?: ICrudOption) {
    const {user_id} = params
    const {id} = option.filter // mentor id
    return await this.onChangeShortVideoLikeStatus(
      user_id,
      id,
      LIKE_STATUS.UNLIKE
    )
  }

  removeOldVideo(item: any) {
    const subName = item.video.split('.')[0]
    const files = fs.readdirSync(VIDEO_PATH)
    for (const file of files) {
      if (file.includes(subName)) {
        fs.unlink(VIDEO_PATH + item.video)
      }
    }
  }

  onChangeShortVideoLikeStatus = async (
    user_id: string,
    short_video_id: string,
    status: boolean
  ) => {
    const transaction = await sequelize.transaction()
    try {
      const bodyFS: any = {user_id: user_id, short_video_id: short_video_id}

      const tempFind = await this.exec(
        FavouriteShortVideo.findOne({
          where: bodyFS,
          attributes: ['id'],
        })
      )

      if (status) {
        if (!tempFind)
          await this.exec(FavouriteShortVideo.create(bodyFS, {transaction}))
      } else {
        if (tempFind) {
          await this.exec(
            FavouriteShortVideo.destroy({
              where: {
                id: tempFind.id,
              },
              transaction,
            })
          )
        }
      }

      const shortVideo = await this.exec(this.model.findById(short_video_id), {
        allowNull: false,
      })

      const like_count = status ? shortVideo.like + 1 : shortVideo.like - 1
      shortVideo.like = like_count

      const body: any = {
        like: like_count,
      }

      await this.exec(
        this.model.update(body, {
          where: {
            id: shortVideo.id,
          },
          transaction: transaction,
        })
      )

      transaction.commit()
      return {
        shortVideo: shortVideo,
      }
    } catch (error) {
      transaction.rollback()
      throw error
    }
  }
}

function getRandomArbitrary(min: number, max: number) {
  return Math.random() * (max - min) + min
}
