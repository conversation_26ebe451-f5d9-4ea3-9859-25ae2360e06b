import { CrudService, ICrudOption } from '../crudService.pg'
import {config} from "@/config";
import {Navbar} from "@/models";
import {Sequelize} from "@/models/base.pg";
export class NavbarService extends CrudService<typeof Navbar> {
  constructor() {
    super(Navbar)
  }
  // async getList(
  //     option: ICrudOption = {
  //       limit: config.database.defaultPageSize,
  //       offset: 0,
  //       scope: ['defaultScope'],
  //     }
  // ) {
  //   return await this.exec(
  //       this.modelWithScope(option.scope).findAll()
  //   )
  // }
  async dragDropItem(id: string, params: any) {
    let {prev_index_number, next_index_number} = params
    let currElIndexNumber;
    if (prev_index_number === undefined) {
      currElIndexNumber = next_index_number - 512;
    } else if (next_index_number === undefined) {
      currElIndexNumber = prev_index_number + 512;
    } else {
      currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
    }
    try {
      const update = await this.model.update({index: currElIndexNumber}, {
        where: {
          id
        }
      })
      if (
          Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
          Math.abs(currElIndexNumber - next_index_number) <= 1
      ) {
        const items =await this.model.findAll({
          attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
        })
        await Promise.all(
            items.map(async (element) => {
              await this.model.update({index : element.dataValues.orderedData * 1024 }, {where : {
                  id : element.id
                }})
            })
        );
      }
      return update
    } catch (e) {
      console.log(e)
    }
  }
}
