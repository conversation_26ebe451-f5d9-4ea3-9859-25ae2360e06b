import * as moment from 'moment'

import {
    RESERVATION_STATUS,
    FCM_ACTIONS,
    FCM_ACTIONS_MESSAGE,
    getFcmActionMessage,
    POINT_ACTION,
} from '@/const'
import {sequelize} from '@/models'
import {Reservation, Shop, User, Notification} from '@/models/tables'
import {errorService, smsService} from '@/services'
import {CrudService, ICrudOption} from '../crudService.pg'
import {firebaseService, pointService} from '@/services'
import {config} from "@/config";
import {createMessage, MessageType} from "@/services/smsService";
import {socketService} from "@/services/socketService";
import {SOCKET_EVENTS} from "@/services/socketService/constant/event";

const ITEM_WAS_DELETED_ERR_CODE = 405
const {Op} = sequelize.Sequelize

const KOREA_TZ = 60 * 60 * 9 * 1000

const numberWithDash = (x: number) => {
    return x
        .toString()
        .replace(/-/g, '')
        .replace(/\B(?=(\d{4})+(?!\d))/g, '-')
}

function numberWithCommas(x: number) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export class ReservationService extends CrudService<typeof Reservation> {
    constructor() {
        super(Reservation)
    }

    async createReserve(params: any, option?: ICrudOption) {
        try {
            const {user_id, shop_id} = params
            params.state = RESERVATION_STATUS.PENDING
            let count_service = 0
            for (const price of params.prices) {
                count_service += price.quantity
            }
            params.count_service = count_service
            const [reservation, user, shop] = await Promise.all([
                this.create(params),
                this.exec(User.findByPk(user_id)),
                this.exec(Shop.findByPk(shop_id, {
                    include : {
                        association : "user"
                    }
                })),
            ])

            const message = getFcmActionMessage(FCM_ACTIONS_MESSAGE.NEW_RESERVATION, {
                params_1: user.nickname,
            })
            const messageForUser = getFcmActionMessage(FCM_ACTIONS_MESSAGE.NEW_RESERVATION_FOR_SHOP, {
                params_1: shop.title,
            })
            const entity_id = shop_id
            const bodyNoti: any = {
                user_id: shop.user_id,
                title: message,
                content: message,
                data: {
                    message: message,
                    params: {
                        params_1: user.nickname,
                    },
                },
                action: Number(FCM_ACTIONS.NEW_RESERVATION),
                interacting_type: 'NEW_RESERVATION',
                interacting_content_id: entity_id,
            }
            const bodyNotiForUser: any = {
                user_id: user_id,
                title: messageForUser,
                content: messageForUser,
                data: {
                    message: messageForUser,
                    params: {
                        params_1: shop.title,
                    },
                },
                action: Number(FCM_ACTIONS.NEW_RESERVATION),
                interacting_type: 'NEW_RESERVATION',
                interacting_content_id: entity_id,
            }
            await Promise.all([
                // firebaseService.sendNotification(
                //     shop.user_id,
                //     message,
                //     FCM_ACTIONS.NEW_RESERVATION,
                //     shop.id
                // ),
                // firebaseService.sendNotification(
                //     user_id,
                //     messageForUser,
                //     FCM_ACTIONS.NEW_RESERVATION,
                //     shop.id
                // ),
                this.exec(Notification.create(bodyNoti)),
                this.exec(Notification.create(bodyNotiForUser)),
                this.exec(User.update({reservation_status : true}, {where : {id : shop.user_id}}))
            ])



            let quantity = 0
            let count_price = 0
            let course = ''
            for (const [i, price] of params.prices.entries()) {
                quantity += price.quantity
                count_price += parseInt(price.discount) * price.quantity
                if (!course.includes(price.course.title))
                    course += `${price.course.title}`
                if (i + 1 < params.prices.length) course += `, `
            }
            const bodySms: any = {
                shop_title: shop.title,
                quantity: quantity,
                course_name: course,
                date: moment(parseInt(reservation.date) + KOREA_TZ).format(
                    'YYYY년 MM월 DD일 HH시mm분'
                ),
                username: user.username,
                nickname: user.nickname,
                price:
                    numberWithCommas(count_price) + reservation.prices[0].course.unit,
                shop_phone_number: numberWithDash(shop.contact_phone),
                user_phone_number: numberWithDash(reservation.contact),
            }

            await smsService.sendLongMessage(
                createMessage(MessageType.USER_RESERVATION, bodySms),
                params.contact
            )

            await smsService.sendLongMessage(
                createMessage(MessageType.SHOP_RESERVATION, bodySms),
                shop.contact_phone
            )
            if (shop.user.auto_approve_reservation){
                const body: any = {
                    state: RESERVATION_STATUS.APPROVED,
                    reason: null,
                }
                await this.updateReserveStatus(body, {filter: {id: reservation.id}})
            }
            socketService.emitToUser(shop.user_id, SOCKET_EVENTS.CREATE_RESERVATION, reservation.id)

            const TIME_OUT = 15 * 60 * 1000
            setTimeout(async () => {
                const _reservations = await this.exec(
                    Reservation.findByPk(reservation.id)
                )
                if (_reservations.state === RESERVATION_STATUS.PENDING) {
                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.REPEAT_RESERVATION,
                        {
                            params_1: user.nickname,
                        }
                    )
                    // firebaseService.sendNotification(
                    //     shop.user_id,
                    //     message,
                    //     FCM_ACTIONS.REPEAT_RESERVATION,
                    //     shop.id
                    // )

                    const entity_id = shop_id
                    const bodyNoti: any = {
                        user_id: shop.user_id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params: {
                                params_1: user.nickname,
                            },
                        },
                        action: FCM_ACTIONS.REPEAT_RESERVATION,
                        interacting_type: 'REPEAT_RESERVATION',
                        interacting_content_id: entity_id,
                    }
                    await this.exec(Notification.create(bodyNoti))

                    const _message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.REPEAT_RESERVATION_USER
                    )
                    // firebaseService.sendNotification(
                    //     user_id,
                    //     _message,
                    //     FCM_ACTIONS.REPEAT_RESERVATION,
                    //     shop.id
                    // )

                    const _entity_id = shop_id
                    const _bodyNoti: any = {
                        user_id: user_id,
                        title: _message,
                        content: _message,
                        data: {
                            message: _message,
                            params: {
                                params_1: user.nickname,
                            },
                        },
                        action: FCM_ACTIONS.REPEAT_RESERVATION_USER,
                        interacting_type: 'REPEAT_RESERVATION_USER',
                        interacting_content_id: _entity_id,
                    }
                    await this.exec(Notification.create(_bodyNoti))
                }
            }, TIME_OUT)

            return reservation
        } catch (e) {
            throw e
        }
    }

    async update(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update(params))
        const reservations = await this.getItem(option)
        const shop = await this.exec(Shop.findByPk(reservations.shop_id), {
            allowNull: false,
        })
        const user = await this.exec(User.findByPk(reservations.user_id), {
            allowNull: false,
        })

        const message = getFcmActionMessage(
            FCM_ACTIONS_MESSAGE.UPDATE_RESERVATION,
            {
                params_1: user.nickname,
            }
        )
        // firebaseService.sendNotification(
        //     shop.user_id,
        //     message,
        //     FCM_ACTIONS.UPDATE_RESERVATION,
        //     shop.id
        // )

        const entity_id = shop.id
        const bodyNoti: any = {
            user_id: shop.user_id,
            title: message,
            content: message,
            data: {
                message: message,
                params: {
                    params_1: user.nickname,
                },
            },
            action: FCM_ACTIONS.UPDATE_RESERVATION,
            interacting_type: 'UPDATE_RESERVATION',
            interacting_content_id: entity_id,
        }
        await this.exec(Notification.create(bodyNoti))
        return reservations
    }

    async updateReserveStatus(params: any, option?: ICrudOption) {
        try {
            const {state} = params
            const item = await this.exec(this.model.findByPk(option.filter.id))
            if (!item.buyer_status || !item.seller_status) {
                throw errorService.database.queryFail(
                    '다른 사람들이 예약을 삭제했습니다.',
                    ITEM_WAS_DELETED_ERR_CODE
                )
            }
            const reservation = await this.update(params, option)

            const shop = await this.exec(Shop.findByPk(reservation.shop_id), {
                allowNull: false,
            })
            const user = await this.exec(User.findByPk(reservation.user_id), {
                allowNull: false,
            })

            let quantity = 0
            let count_price = 0
            let course = ''
            for (const [i, price] of reservation.prices.entries()) {
                quantity += price.quantity
                count_price += parseInt(price.discount) * price.quantity
                if (!course.includes(price.course.title))
                    course += `${price.course.title}`
                if (i + 1 < reservation.prices.length) course += `, `
            }

            const bodySms: any = {
                shop_title: shop.title,
                quantity: quantity,
                course_name: course,
                date: moment(parseInt(reservation.date) + KOREA_TZ).format(
                    'YYYY년 MM월 DD일 HH시mm분'
                ),
                price:
                    numberWithCommas(count_price) + reservation.prices[0].course.unit,
                username: user.username,
                nickname: user.nickname,
                shop_phone_number: numberWithDash(shop.contact_phone),
                user_phone_number: numberWithDash(reservation.contact),
            }

            switch (state) {
                case RESERVATION_STATUS.APPROVED: {
                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.ACCEPTED_RESERVATION, {
                            params_1: shop.title,
                        }
                    )
                    // await firebaseService.sendNotification(
                    //     user.id,
                    //     message,
                    //     FCM_ACTIONS.ACCEPTED_RESERVATION,
                    //     reservation.id
                    // )

                    const entity_id = reservation.id
                    const bodyNoti: any = {
                        user_id: user.id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params: {
                                params_1: shop.title,
                            },
                        },
                        action: FCM_ACTIONS.ACCEPTED_RESERVATION,
                        interacting_type: 'ACCEPTED_RESERVATION',
                        interacting_content_id: entity_id,
                    }
                    await this.exec(Notification.create(bodyNoti))
                    await this.exec(User.update({reservation_status : true}, {where : {id : user.id}}))
                    await smsService.sendLongMessage(
                        createMessage(MessageType.SHOP_APPROVE, bodySms),
                        reservation.contact
                    )
                    socketService.emitToUser(reservation.user_id, SOCKET_EVENTS.APPROVED_RESERVATION, reservation.id)
                    break
                }
                case RESERVATION_STATUS.REJECTED: {
                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.RESERVATION_NOT_AVAILABLE,
                        {
                            params_1: shop.title,
                        }
                    )
                    // await firebaseService.sendNotification(
                    //     user.id,
                    //     message,
                    //     FCM_ACTIONS.RESERVATION_NOT_AVAILABLE,
                    //     reservation.id
                    // )

                    const entity_id = reservation.id
                    const bodyNoti: any = {
                        user_id: user.id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params: {
                                params_1: shop.title,
                            },
                        },
                        action: FCM_ACTIONS.RESERVATION_NOT_AVAILABLE,
                        interacting_type: 'RESERVATION_NOT_AVAILABLE',
                        interacting_content_id: entity_id,
                    }
                    await this.exec(Notification.create(bodyNoti))
                    await this.exec(User.update({reservation_status : true}, {where : {id : user.id}}))
                    await smsService.sendLongMessage(
                        createMessage(MessageType.USER_CANCEL, bodySms),
                        reservation.contact
                    )
                    socketService.emitToUser(reservation.user_id, SOCKET_EVENTS.REJECTED_RESERVATION, reservation.id)
                    break
                }
                case RESERVATION_STATUS.CANCELLED: {
                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.CANCEL_RESERVATION,
                        {
                            params_1: user.nickname,
                        }
                    )
                    const messageForUser = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.CANCEL_RESERVATION_FOR_USER,
                        {
                            params_1: shop.title,
                        }
                    )


                    const entity_id = reservation.id
                    const bodyNoti: any = {
                        user_id: shop.user_id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params: {
                                params_1: user.nickname,
                            },
                        },
                        action: FCM_ACTIONS.CANCEL_RESERVATION,
                        interacting_type: 'CANCEL_RESERVATION',
                        interacting_content_id: entity_id,
                    }
                    const bodyNotiForUser: any = {
                        user_id: user.id,
                        title: messageForUser,
                        content: messageForUser,
                        data: {
                            message: messageForUser,
                            params: {
                                params_1: shop.title,
                            },
                        },
                        action: FCM_ACTIONS.CANCEL_RESERVATION,
                        interacting_type: 'CANCEL_RESERVATION',
                        interacting_content_id: entity_id,
                    }

                    socketService.emitToUser(shop.user_id, SOCKET_EVENTS.CANCELLED_RESERVATION, reservation.id),
                    await Promise.all([
                        // firebaseService.sendNotification(
                        //     shop.user_id,
                        //     message,
                        //     FCM_ACTIONS.CANCEL_RESERVATION,
                        //     shop.id
                        // ),
                        // firebaseService.sendNotification(
                        //     user.id,
                        //     messageForUser,
                        //     FCM_ACTIONS.CANCEL_RESERVATION,
                        //     shop.id
                        // ),
                        this.exec(Notification.create(bodyNoti)),
                        this.exec(Notification.create(bodyNotiForUser))
                    ])
                    await smsService.sendLongMessage(
                        createMessage(MessageType.SHOP_REJECT, bodySms),
                        shop.contact_phone
                    )
                    break
                }
                default: {
                    break
                }
            }
            return reservation
        } catch (error) {
            throw error
        }
    }

    async deleteReserve(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findOne(option))
        const body: any = {}
        if (params.user_id === item.seller_id) {
            body.seller_status = false
        } else if (params.user_id === item.user_id) {
            body.buyer_status = false
        }
        return this.update(body, option)
    }

    async deleteByTypeReserve(params: any, option?: ICrudOption) {
        const {state, caller_id} = params
        const transaction = await sequelize.transaction()
        try {
            if (state === RESERVATION_STATUS.ALL) {
                await this.exec(
                    this.model.update(
                        {
                            seller_status: false,
                        },
                        {
                            where: {
                                seller_id: caller_id,
                            },
                            transaction,
                        }
                    )
                )
                await this.exec(
                    this.model.update(
                        {
                            buyer_status: false,
                        },
                        {
                            where: {
                                user_id: caller_id,
                            },
                            transaction,
                        }
                    )
                )
            } else {
                await this.exec(
                    this.model.update(
                        {
                            seller_status: false,
                        },
                        {
                            where: {
                                seller_id: caller_id,
                                state: state,
                            },
                            transaction,
                        }
                    )
                )
                await this.exec(
                    this.model.update(
                        {
                            buyer_status: false,
                        },
                        {
                            where: {
                                user_id: caller_id,
                                state: state,
                            },
                            transaction,
                        }
                    )
                )
            }
            transaction.commit()
            return {
                code: 200,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async scheduleCompleteReservationData() {
        const POINT_FOR_RESERVATION = 1000
        const body: any = {
            state: RESERVATION_STATUS.COMPLETED,
            feedback_status: true,
        }
        const res = await this.exec(
            this.model.findAll({
                where: {
                    date: {
                        $lte: moment().valueOf() - 90 * 60000,
                    },
                    state: RESERVATION_STATUS.APPROVED,
                },
            })
        )
        for (const reservation of res) {
            await reservation.update(body)
            const shop = await this.exec(
                Shop.findByPk(reservation.shop_id, {
                    attributes: ['category_id'],
                    include: [
                        {
                            association: 'category',
                            attributes: ['thema_id'],
                            include: [{association: 'thema', attributes: ['bonus_point']}],
                        },
                    ],
                })
            )
            const message = getFcmActionMessage(
                FCM_ACTIONS_MESSAGE.COMPLETE_RESERVATION, {
                    params_1: shop.title,
                }
            )
            // firebaseService.sendNotification(
            //     reservation.user_id,
            //     message,
            //     FCM_ACTIONS.COMPLETE_RESERVATION,
            //     reservation.id
            // )

            const entity_id = reservation.id
            const bodyNoti: any = {
                user_id: reservation.user_id,
                title: message,
                content: message,
                data: {
                    message: message,
                    params: {
                        params_1: shop.title,
                    },
                },
                action: FCM_ACTIONS.COMPLETE_RESERVATION,
                interacting_type: 'COMPLETE_RESERVATION',
                interacting_content_id: entity_id,
            }
            await this.exec(Notification.create(bodyNoti))
            if (shop.category.thema.bonus_point) {
                const message = getFcmActionMessage(
                    FCM_ACTIONS_MESSAGE.POINTS_ARE_ADDED, {
                        params_1: shop.title,
                    }
                )
                // await pointService.changePoint(
                //     {
                //         user_id: reservation.user_id,
                //         action: POINT_ACTION.RESERVATION,
                //         point: POINT_FOR_RESERVATION,
                //     },
                //     undefined
                // )
                const bodyNoti: any = {
                    user_id: reservation.user_id,
                    title: message,
                    content: message,
                    data: {
                        message: message,
                        params: {
                            params_1: shop.title,
                            params_2 : POINT_FOR_RESERVATION
                        },
                    },
                    action: FCM_ACTIONS.POINTS_ARE_ADDED,
                    interacting_type: 'POINTS_ARE_ADDED',
                    interacting_content_id: entity_id,
                }
                await this.exec(Notification.create(bodyNoti))
            }
        }
    }

    async scheduleDeniedReservationData() {
        const body: any = {
            state: RESERVATION_STATUS.REJECTED,
        }
        const result = await this.exec(
            Reservation.update(body, {
                where: {
                    created_at_unix_timestamp: {
                        $lte: moment().valueOf() - 30 * 60000,
                    },
                    state: RESERVATION_STATUS.PENDING,
                },
            })
        )
        return result
    }

    async scheduleClearShopReservation() {
        const shop = await Shop.findAll({
            attributes: ['id', 'reservation_times'],
            where: sequelize.where(
                sequelize.fn('array_length', sequelize.col('reservation_times'), 1),
                {[Op.gt]: 0}
            ),
            raw: true,
        })
        for (const item of shop) {
            const tempReservation = []
            for (const reservationTime of item.reservation_times) {
                if (reservationTime.date === 'default') {
                    tempReservation.push(reservationTime)
                    continue
                } else {
                    const now = new Date()
                    now.setHours(0, 0, 0, 0)
                    const time = new Date(reservationTime.date)
                    if (time.getTime() >= now.getTime())
                        tempReservation.push(reservationTime)
                }
            }

            await Shop.update(
                {reservation_times: tempReservation},
                {where: {id: item.id}}
            )
        }
        return 'success'
    }

    async countByState(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const [total, pending, approve, unsuccess, complete, reject, cancel] = await Promise.all([
            this.exec(
                this.modelWithScope(option.scope).count({where: {...option.filter}})
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: {...option.filter, state: RESERVATION_STATUS.PENDING},
                })
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: {...option.filter, state: RESERVATION_STATUS.APPROVED},
                })
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: {
                        ...option.filter,
                        state: {
                            $in: [RESERVATION_STATUS.REJECTED, RESERVATION_STATUS.CANCELLED],
                        },
                    },
                })
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: {...option.filter, state: RESERVATION_STATUS.COMPLETED},
                })
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: {...option.filter, state: RESERVATION_STATUS.REJECTED},
                })
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: {...option.filter, state: RESERVATION_STATUS.CANCELLED},
                })
            ),
        ])

        return {
            total,
            pending,
            approve,
            unsuccess,
            complete,
            reject,
            cancel
        }
    }

    async getShopRanking(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const options = this.applyFindOptions(option)
        const [list, total] = await Promise.all([
            this.exec(
                this.modelWithScope(option.scope).findAll({
                    ...options,
                    attributes: [
                        [
                            sequelize.fn('count', sequelize.col('shop_id')),
                            'count_reservation',
                        ],
                        'shop_id',
                    ],
                    where: options.where,
                    include: [{association: 'shop'}],
                    group: ['shop_id', 'shop.id'],
                    order: [[sequelize.fn('count', sequelize.col('shop_id')), 'DESC']],
                })
            ),
            this.exec(
                this.modelWithScope(option.scope).count({
                    where: options.where as any,
                    include: [{association: 'shop'}],
                    distinct: true,
                    col: 'shop_id',
                })
            ),
        ])

        return {total, list}
    }

    async feedbackAvailable(params: { user_id: string; shop_id: string }) {
        const {user_id, shop_id} = params
        const shop = await this.exec(
            Shop.findByPk(shop_id, {
                include: [
                    {
                        association: 'category',
                        attributes: ['id'],
                        include: [
                            {
                                association: 'thema',
                            },
                        ],
                    },
                ],
            }),
            {allowNull: false}
        )

        if (!shop.category.thema.review_require)
            return {
                can_feedback: true,
            }
        const reservationWithFeedback = await this.exec(
            this.model.findOne({
                where: {
                    user_id,
                    shop_id,
                    feedback_status: true,
                },
            })
        )

        return {
            can_feedback: !!reservationWithFeedback,
        }
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        },
        search_value ?: string
    ) {
        if (search_value) {
            const associations = option.include.map(item => item.association)
            if (associations.includes('user')) {
                const userInclude = option.include.findIndex(item => item.association === 'user')
                option.include[userInclude].where = {
                    [Op.or]: {
                        email: {$iLike: '%' + search_value + '%'},
                        phone: {$iLike: '%' + search_value + '%'},
                        nickname: {$iLike: '%' + search_value + '%'},
                        username: {$iLike: '%' + search_value + '%'},
                    }
                }
            } else {
                option.include.push({
                    association: "user",
                    where: {
                        [Op.or]: {
                            email: {$iLike: '%' + search_value + '%'},
                            phone: {$iLike: '%' + search_value + '%'},
                            nickname: {$iLike: '%' + search_value + '%'},
                            username: {$iLike: '%' + search_value + '%'},
                        }
                    }
                })
            }
        }
        const reservations = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
        for (const reservation of reservations.rows) {
            const count_pending_reservation = await this.exec(
                this.model.count(
                    {
                        where: {
                            shop_id: reservation.dataValues.shop_id,
                            state: RESERVATION_STATUS.PENDING,
                        },
                    }
                )
            )

            const count_success_reservation = await this.exec(
                this.model.count(
                    {
                        where: {
                            shop_id: reservation.dataValues.shop_id,
                            state: RESERVATION_STATUS.APPROVED,
                        },
                    }
                )
            )
            const count_cancel_reservation = await this.exec(
                this.model.count(
                    {
                        where: {
                            shop_id: reservation.dataValues.shop_id,
                            state: RESERVATION_STATUS.CANCELLED,
                        },
                    }
                )
            )
            const complete_reservation = await this.exec(
                this.model.findAll(
                    {
                        where: {
                            shop_id: reservation.dataValues.shop_id,
                            state: RESERVATION_STATUS.CANCELLED,
                        },
                    }
                )
            )
            const total_revenue = complete_reservation.reduce((acc: number, cur: any) => {
                let total = 0
                for (const price of cur.prices) {
                    total += Number(price.price) * Number(price.quantity)
                }
                return acc + total
            }, 0)
            const total_of_shop = await this.exec(
                this.model.count(
                    {
                        where: {
                            shop_id: reservation.dataValues.shop_id,
                        },
                    }
                )
            )
            reservation.dataValues.count_pending_reservation = count_pending_reservation
            reservation.dataValues.count_success_reservation = count_success_reservation
            reservation.dataValues.total_of_shop = total_of_shop
            reservation.dataValues.count_cancel_reservation = count_cancel_reservation
            reservation.dataValues.total_revenue = total_revenue
        }
        return reservations
    }

    async scheduleNotification() {
        const reservations = await this.getReservations();

        for (const reservation of reservations) {
            try {
                const seconds = moment(reservation.date).diff(moment(), 'seconds');
                const shop = await this.exec(Shop.findByPk(reservation.shop_id));
                const user = await this.exec(User.findByPk(reservation.user_id));

                if (seconds < 1800 && seconds > 1450) {
                    await this.sendNotifications(reservation, shop, user, FCM_ACTIONS_MESSAGE.THIRTY_MINUTES_BEFORE_BOOKING_TIME_SHOP, FCM_ACTIONS_MESSAGE.THIRTY_MINUTES_BEFORE_BOOKING_TIME_USER);
                }

                if (seconds < 600 && seconds > 250) {
                    await this.sendNotifications(reservation, shop, user, FCM_ACTIONS_MESSAGE.TEN_MINUTES_BEFORE_BOOKING_TIME_SHOP, FCM_ACTIONS_MESSAGE.TEN_MINUTES_BEFORE_BOOKING_TIME_USER);
                }
            }
            catch (e) {
                
            }
        }
    }

    async getReservations() {
        return this.exec(
            this.model.findAll({
                where: {
                    state: RESERVATION_STATUS.APPROVED,
                    date: {
                        $gte: moment().add(10, 'minutes').valueOf(),
                    },
                },
            })
        );
    }

    async sendNotifications(reservation: any, shop: any, user: any, shopMessage: string, userMessage: string) {
        const message = getFcmActionMessage(shopMessage, {params_1: user.nickname});
        const messageForUser = getFcmActionMessage(userMessage, {params_1: shop.title});
        const entity_id = reservation.id;

        const bodyNoti = this.createNotificationBody(shop.user_id, message, entity_id);
        const bodyNotiForUser = this.createNotificationBody(user.id, messageForUser, entity_id);

        await Promise.all([
            // firebaseService.sendNotification(shop.user_id, message, FCM_ACTIONS.REMIND_RESERVATION, reservation.id),
            // firebaseService.sendNotification(user.id, messageForUser, FCM_ACTIONS.REMIND_RESERVATION, reservation.id),
            this.exec(Notification.create(bodyNoti)),
            this.exec(Notification.create(bodyNotiForUser))
        ]);
    }

    createNotificationBody(userId: string, message: string, entityId: string) : any {
        return {
            user_id: userId,
            title: message,
            content: message,
            data: {
                message: message,
                params: {
                    params_1: userId,
                },
            },
            action: Number(FCM_ACTIONS.REMIND_RESERVATION),
            interacting_type: 'REMIND_RESERVATION',
            interacting_content_id: entityId,
        };
    }


}
