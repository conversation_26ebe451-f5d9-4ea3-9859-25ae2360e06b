import { CrudService } from '../crudService'
import { AdminSetting } from '@/models'

const DEFAULT_ID = 1

export class AdminSettingService extends CrudService<typeof AdminSetting> {
  constructor() {
    super(AdminSetting)
  }
  async getAdminSetting() {
    return await AdminSetting.findOne({ where: { id: DEFAULT_ID } })
  }

  async updateAdminSetting() {
    const setting = await this.exec(
      AdminSetting.findOne({ where: { id: DEFAULT_ID } })
    )
    return await AdminSetting.update(
      { status: !setting.status },
      { where: { id: DEFAULT_ID } }
    )
  }

  async updateMentorStatus() {
    const setting = await this.exec(
      AdminSetting.findOne({ where: { id: DEFAULT_ID } })
    )
    return await setting.update({
      mentor_status: !setting.mentor_status,
    })
  }

  async updateReportLimit(params : any) {
    const {report_limit} = params
    const setting = await this.exec(
        AdminSetting.findOne({ where: { id: DEFAULT_ID } })
    )
    return await setting.update({
      report_limit,
    })
  }
}
