import {CrudService, ICrudOption} from '../crudService.pg'
import {SettingDistrict, SettingProvince} from '@/models/tables'
import {Sequelize} from "@/models/base.pg";
import {AREA_SETTING} from "@/const";

const fs = require('fs')
const xlsx = require('node-xlsx')

export class SettingDistrictService extends CrudService<typeof SettingDistrict> {
    constructor() {
        super(SettingDistrict)
    }

    async importExcel(params: any) {
        const data = await xlsx.parse(params.url)
        const dataExcel = data[0].data
        const [provinces, ...rest] = dataExcel
        const dataImport: Record<string, any> = []
        for (const [index, province] of provinces.entries()) {
            const [provinceCreate] = await SettingProvince.findOrCreate({
                where: {
                    name: province
                },
                defaults: {
                    name: province,
                    region: AREA_SETTING.KOREA
                }
            })
            for (const e of rest) {
                const obj: Record<string, any> = {}
                obj['name'] = e[index]
                obj['setting_province_id'] = provinceCreate.id
                dataImport.push(obj)
            }
        }

        fs.unlinkSync(params.url)
        const t = await this.transaction()
        try {
            const created = await SettingDistrict.bulkCreate(dataImport as any, {transaction: t})
            await t.commit()
            return created
        } catch (e) {
            await t.rollback()
        }
    }

    async create(params: any, option?: ICrudOption) {
        const lastItem = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastItem ? (Number(lastItem.dataValues.index) + 1024) : 1024
        return await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
    }

    async dragDropItem(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const items = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    items.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }
}