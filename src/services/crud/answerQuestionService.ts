import { CrudService, ICrudOption } from '../crudService.pg'
import { AnswerQuestion } from '@/models'
import { config } from '@/config'

export class AnswerQuestionService extends CrudService<typeof AnswerQuestion> {
  constructor() {
    super(AnswerQuestion)
  }
  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    option.filter = { ...option.filter, parent_id: { $eq: null } }

    return await this.exec(
      this.modelWithScope(option.scope).findAndCountAll(
        this.applyFindOptions(option)
      )
    )
  }
}
