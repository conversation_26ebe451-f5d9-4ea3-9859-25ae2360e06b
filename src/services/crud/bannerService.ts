import { CrudService, ICrudOption } from '../crudService.pg'
import { Banner } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'

import { tokenService, firebaseService, errorService } from '@/services'
export class BannerService extends CrudService<typeof Banner> {
  constructor() {
    super(Banner)
  }

  async create(params: any, option?: ICrudOption) {
    const bannerAmount = await Banner.count({})
    if (bannerAmount >= 5) {
      throw errorService.database.queryFail('Banner cannot exceed 5 items')
    }
    return await this.exec(
      this.model.create(params, this.applyCreateOptions(option))
    )
  }
}
