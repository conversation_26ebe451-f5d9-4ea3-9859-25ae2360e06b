import { CrudService, ICrudOption } from '../crudService.pg'
import { NavIcon} from '@/models/tables'
import {config} from "@/config";
export class NavIconService extends CrudService<typeof NavIcon> {
  constructor() {
    super(NavIcon)
  }
  async getList(
      option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
      }
  ) {
    return await this.exec(
        this.modelWithScope(option.scope).findAll()
    )
  }
}
