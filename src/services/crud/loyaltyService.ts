import { Shop, Loyalty, TicketUsed } from '@/models/tables'
import { CrudService, ICrudOption } from '../crudService'
import { errorService } from '@/services'
import { sequelize } from '@/models'

export class LoyaltyService extends CrudService<typeof Loyalty> {
  constructor() {
    super(Loyalty)
  }

  async create(params: any, option?: ICrudOption) {
    const transaction = await sequelize.transaction()
    const {quantily, loyalty} = params
    try {
      const listExist: any = this.exec(
        Loyalty.findAndCountAll({
          where: {
            shop_id: loyalty.shop_id,
            user_id: loyalty.user_id,
          },
          raw: true,
        })
      )
      const count = listExist.count
      for (let i = 1; i <= quantily; i++) {
        const bodyLoyalty: any = {
          user_id: loyalty.user_id,
          shop_id: loyalty.shop_id,
          count: count + i,
        }
        await this.exec(Loyalty.create(bodyLoyalty, {transaction}))
      }
      transaction.commit()
      return {is_success: true}
    } catch (err) {
      transaction.rollback()
      throw err
    }
  }

  async getSearchList(params: any, option: ICrudOption) {
    const {search} = params

    const loyaltyOptions: ICrudOption = {
      ...option,
      include: option.filter.shop_id
        ? [
            {
              association: 'user',
              include: [],
              distinct: true,
              where: {nickname: {$iLike: '%' + search + '%'}},
            },
            {
              association: 'shop',
              include: [],
              distinct: true,
            },
          ]
        : [
            {
              association: 'shop',
              include: [],
              distinct: true,
              where: {title: {$iLike: '%' + search + '%'}},
            },
            {
              association: 'user',
              include: [],
              distinct: true,
            },
          ],
    }
    return await this.getList(loyaltyOptions)
  }
  async useVoucher(params: any, option?: ICrudOption) {
    const loyalty = params.loyalty
    const transaction = await sequelize.transaction()
    try {
      if (loyalty.length) {
        const voucher = await this.exec(
          Loyalty.findOne({
            where: {id: loyalty[0]},
            include: [{model: Shop, as: 'shop', include: []}],
          })
        )
        if (
          loyalty.length !== voucher.dataValues.shop.dataValues.number_loyalty
        )
          throw errorService.database.queryError({
            message: 'Not enough quantity!',
            code: 400,
          })

        await this.exec(
          Loyalty.update({status: false}, {where: {id: loyalty}, transaction})
        )

        const bodyTicketUsed: any = {
          shop_id: voucher.dataValues.shop_id,
          user_id: voucher.dataValues.user_id,
        }

        await TicketUsed.create(bodyTicketUsed, {transaction})
      }
      transaction.commit()
      return {is_success: true}
    } catch (error) {
      transaction.rollback()
      throw error
    }
  }
}
