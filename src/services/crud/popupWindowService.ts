import { config } from "@/config";
import { CrudService, ICrudOption } from "../crudService.pg";
import { PopupWindow } from "@/models/tables";
import { sequelize } from "@/models";
import { errorService } from "..";
import { Op } from "sequelize";

export class PopupWindowService extends CrudService<typeof PopupWindow> {
  constructor() {
    super(PopupWindow);
  }
  async create(params: any, option?: ICrudOption) {
    const count = await PopupWindow.count({
      where: {
        deleted_at: null,
      },
    });
    if (count >= 30) {
      throw errorService.router.badRequest(
        "A maximum of 30 popup windows can be created."
      );
    }
    if (params.is_mobile_featured) {
      const mobileCount = await PopupWindow.count({
        where: {
          is_mobile_featured: true,
          is_exposed: true,
          deleted_at: null,
        },
      });

      if (mobileCount >= 2) {
        throw errorService.router.badRequest(
          "A maximum of 2 popups can be displayed on mobile."
        );
      }
    }
    // 3. Gán display_order = max + 1
    const maxOrder = await PopupWindow.max("display_order", {
      where: { deleted_at: null },
    });
    params.display_order = (Number.isInteger(maxOrder) ? maxOrder : -1) + 1;
    return await this.exec(
      this.model.create(params, this.applyCreateOptions(option))
    );
  }
  async update(params: any, option?: ICrudOption) {
    const item = await this.exec(this.model.findByPk(option.filter.id), {
      allowNull: false,
    });

    const isUpdatingMobileFeatured = "is_mobile_featured" in params;
    const isUpdatingExposed = "is_exposed" in params;

    // set is_mobile_featured = true
    if (isUpdatingMobileFeatured && params.is_mobile_featured === true) {
      const willBeExposed =
        typeof params.is_exposed !== "undefined"
          ? params.is_exposed
          : item.is_exposed;

      if (willBeExposed) {
        const mobileCount = await PopupWindow.count({
          where: {
            is_exposed: true,
            is_mobile_featured: true,
            deleted_at: null,
            id: { [Op.ne]: item.id },
          },
        });

        if (mobileCount >= 2) {
          throw errorService.router.badRequest(
            "A maximum of 2 popups can be displayed on mobile."
          );
        }
      }
    }

    // set is_exposed = true
    if (isUpdatingExposed && params.is_exposed === true) {
      const willBeMobileFeatured =
        typeof params.is_mobile_featured !== "undefined"
          ? params.is_mobile_featured
          : item.is_mobile_featured;

      if (willBeMobileFeatured) {
        const mobileCount = await PopupWindow.count({
          where: {
            is_exposed: true,
            is_mobile_featured: true,
            deleted_at: null,
            id: { [Op.ne]: item.id }, // ignore itself
          },
        });

        if (mobileCount >= 2) {
          throw errorService.router.badRequest(
            "A maximum of 2 popups can be displayed on mobile."
          );
        }
      }
    }

    if ("display_order" in params) {
      delete params.display_order;
    }

    await this.exec(item.update(params));
    return await this.getItem(option);
  }

  async getListV2(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const filter: any = {
      is_exposed: true,
      deleted_at: null,
    };

    if (option.filter && option.filter.is_mobile_featured === true) {
      filter.is_mobile_featured = true;
      option.limit = 2;
    } else {
      option.limit = 15;
    }

    option.filter = {
      ...(option.filter || {}),
      ...filter,
    };
    option.order = [
      ["display_order", "ASC"],
      ["created_at", "DESC"],
    ];
    return await this.exec(
      this.modelWithScope(option.scope || ["defaultScope"]).findAndCountAll(
        this.applyFindOptions(option)
      )
    );
  }
  async updateOrder(params: any, option?: ICrudOption) {
    const { insert_after_id, moving_ids } = params;

    if (!Array.isArray(moving_ids) || moving_ids.length === 0) {
      throw errorService.router.badRequest("Missing or invalid 'moving_ids'.");
    }

    const transaction = await sequelize.transaction();

    try {
      // 1. get list
      const allPopups = await PopupWindow.findAll({
        where: {
          deleted_at: null,
        },
        order: [["display_order", "ASC"]],
        transaction,
      });

      for (let i = 0; i < allPopups.length; i++) {
        if (allPopups[i].display_order !== i) {
          await PopupWindow.update(
            { display_order: i },
            {
              where: { id: allPopups[i].id },
              transaction,
            }
          );
        }
      }

      // Filter popup not into moving_ids
      const movingSet = new Set(moving_ids);
      const filtered = allPopups.filter((p) => !movingSet.has(p.id));

      // Find insertIndex if have insert_after_id
      let insertIndex = -1;
      if (insert_after_id) {
        insertIndex = filtered.findIndex((p) => p.id === insert_after_id);
        if (insertIndex === -1) {
          throw errorService.router.badRequest(
            "insert_after_id not found in current visible popups."
          );
        }
      }

      // reorder
      let reordered: { id: string }[] = [];

      if (insertIndex === -1) {
        //push in top
        reordered = [...moving_ids.map((id) => ({ id })), ...filtered];
      } else {
        reordered = [
          ...filtered.slice(0, insertIndex + 1),
          ...moving_ids.map((id) => ({ id })),
          ...filtered.slice(insertIndex + 1),
        ];
      }

      //update display_order
      for (let i = 0; i < reordered.length; i++) {
        await PopupWindow.update(
          { display_order: i },
          {
            where: { id: reordered[i].id },
            transaction,
          }
        );
      }

      await transaction.commit();

      return {
        code: 200,
        type: "popup_order_updated",
        message: "Popup order updated successfully.",
      };
    } catch (err) {
      await transaction.rollback();
      throw {
        code: err.code || 500,
        type: err.type || "popup_order_update_failed",
        message: err.message || "Failed to update popup order.",
      };
    }
  }
}
