import { CrudService, ICrudOption } from '../crudService.pg'
import {
    Shop,
    ShopTag,
    User,
    Category,
    Thema,
    Tag,
    Review,
    Favourite,
    Notification,
    RecentReading,
    Event,
    History,
    Price,
    Mentor,
    FavouriteMentor,
    Course,
    Feedback,
    Reservation, ShopOffRange, ShopTimeRange, CallShop, ViewShop,
    Video,
} from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'
import * as moment from 'moment'
import {
    firebaseService,
    errorService,
    recordService,
    userService,
    notificationService,
    postService,
    commentService,
    settingService,
    favouriteService, smsService,
    videoService,
    rewardService,
} from '@/services'
import {
    SHOP_STATE,
    USER_TYPE,
    FCM_ACTIONS,
    TIMEZONE,
    getFcmActionMessage,
    FCM_ACTIONS_MESSAGE,
    ERROR,
    REVIEW_TYPE,
    HISTORY,
    SYSTEM_DATA_ATTRIBUTES_ARR,
    REVIEW_SUB_TYPE,
} from '@/const'
import { ROLE, RESERVATION_STATUS, SHOP_LIST_BOARD_ORDER } from '../../const'
import { deg2rad, calcDistance } from '../socketService/util'
import { shuffle, uniq, find, nthArg } from 'lodash'
import { Op, Sequelize } from 'sequelize'
import {
    checkExpired, generateSlug,
    isBase64EncodeStr,
    removeBase64EncodeStr,
} from '../../config/utils'
import { param } from 'jquery'
// import {BUCKET_NAME, FILE_IMAGE_PATH, ID, SECRET} from '@/routers/v1/image'
import { RecordService } from './recordService'
import { parse } from 'querystring'
import { EventService } from './eventService'
import { socketService } from "@/services/socketService";
import { SOCKET_EVENTS } from "@/services/socketService/constant/event";
import * as uuid from "uuid";

const NodeGeocoder = require('node-geocoder')
const xlsx = require('node-xlsx')
const fs = require('fs')
const AWS = require('aws-sdk')

const FILE_IMAGE_PATH = 'image/'
const KEY_CLOUDIMAGE = 'ce8391bac'
const ID = process.env.AWS_ID
const SECRET = process.env.AWS_SECRET_KEY
const BUCKET_NAME = process.env.AWS_BUCKET_NAME

export const LIKE_STATUS = {
    LIKE_POST: 'LIKE_POST',
    UNLIKE_POST: 'UNLIKE_POST',
}
//
const options = {
    provider: 'google',

    // Optional depending on the providers
    // fetch: customFetchImplementation,
    apiKey: 'AIzaSyAeE2VSjaFdg1SX4Q924lRZAZmYP6PBUH8', // for Mapquest, OpenCage, Google Premier
    // formatter: null, // 'gpx', 'string', ...
}
const MILE_TO_KILOMETER = 1.609344
const KILOMETER_TO_MILE = 1 / 1.609344
const METER_TO_MILE = 1 / (1.609344 * 1000)
const MILE_TO_DEGREE = 1 / 68.703

const SHOP_ERR_CODE = 200

const JUMP_UP_LIMIT_IS_INSUFFICIENT = SHOP_ERR_CODE + 1
const CANNOT_JUMP_UP_RECURRING = SHOP_ERR_CODE + 2
const CANNOT_TURN_OFF_JUMP_UP_RECURRING = SHOP_ERR_CODE + 3
const UPLOADING_BASE_64_ERR = SHOP_ERR_CODE + 5
export const DESCRIPTION_CANNOT_BE_EMPTY = SHOP_ERR_CODE + 312

export class ShopService extends CrudService<typeof Shop> {
    constructor() {
        super(Shop)
    }

    async testChangeExpiredDate(params: any, option?: ICrudOption) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        delete item.user_id
        await this.exec(item.update(params))
        return await this.getItem(option)
    }

    async setReShop(params: any, option?: ICrudOption) {
        const t = await this.transaction()
        option.transaction = t
        try {
            delete params.user_id
            const result = await this.exec(
                this.model.update(params, {where: option.filter, transaction: t})
            )
            t.commit()
            return result
        } catch (err) {
            t.rollback()
            throw err
        }
    }

    async deleteAll(option?: ICrudOption) {
        const t = await this.transaction()
        option.transaction = t
        try {
            // const result: any = [];
            const listShopIds = option.filter.id['$in']
            for (let i = 0; i < listShopIds.length; i++) {
                //
                const shopInfo = await this.exec(
                    Shop.findOne({
                        where: {
                            id: listShopIds[i],
                        },
                        attributes: ['id', 'user_id', 'expired_date', 'title', 'state'],
                        transaction: t,
                    })
                )
                if (shopInfo) {
                    await this.onDeleteOneShop(ROLE.SUPERADMIN, shopInfo, t, true)
                }
            }
            const result = {
                success: true,
            }
            t.commit()
            return result
        } catch (err) {
            t.rollback()
            throw err
        }
    }

    syncSystemData = (data: any, params: any) => {
        let newData: any = {}

        const systemData: any = {}
        if (params && params.comment >= 0) {
            systemData.comment = params.comment
        }
        if (params && params.soft_comment_count >= 0) {
            systemData.soft_comment_count = params.soft_comment_count
        }
        if (params && params.view >= 0) {
            systemData.view = params.view
        }
        if (params && params.view_records >= 0) {
            systemData.view_records = params.view_records
        }
        if (params && params.view_daily >= 0) {
            systemData.view_daily = params.view_daily
        }
        if (params && params.view_weekly >= 0) {
            systemData.view_weekly = params.view_weekly
        }
        if (params && params.view_monthly >= 0) {
            systemData.view_monthly = params.view_monthly
        }
        if (params && params.view_last_3_months >= 0) {
            systemData.view_last_3_months = params.view_last_3_months
        }
        if (params && params.like >= 0) {
            systemData.like = params.like
        }
        if (params && params.expired_date) {
            systemData.expired_date = params.expired_date
        }
        if (params && params.jump_interval >= 0) {
            systemData.jump_interval = params.jump_interval
        }
        if (params && params.next_jump_time) {
            systemData.next_jump_time = params.next_jump_time
        }
        if (params && params.jump_order) {
            systemData.jump_order = params.jump_order
        }
        if (params && params.jump_count >= 0) {
            systemData.jump_count = params.jump_count
        }

        newData = {...systemData}

        if (data.old_shop) {
            newData.old_shop = Object.assign(data.old_shop, systemData)
        }

        if (data.denied_shop) {
            newData.denied_shop = Object.assign(data.denied_shop, systemData)
        }

        return newData
    }

    ignoreShopSystemData = async (data: any) => {
        const newData = {...data}
        delete newData.comment
        delete newData.soft_comment_count
        delete newData.view_records
        delete newData.view
        delete newData.view_daily
        delete newData.view_weekly
        delete newData.view_monthly
        delete newData.view_last_3_months
        delete newData.like
        return newData
    }
    convertedCorrespondShopData = async (
        data: any,
        oldShop: any,
        deniedShop: any,
        deniedMessage: any
    ) => {
        const temp = {...data}
        temp.old_shop = oldShop
        temp.denied_shop = deniedShop
        temp.denied_message = deniedMessage
        if (oldShop) {
            temp.old_shop = this.ignoreShopSystemData(oldShop)
        }
        if (deniedShop) {
            temp.deniedShop = this.ignoreShopSystemData(deniedShop)
        }
        return this.ignoreShopSystemData(temp)
    }

    getJoiningTableOfDeniedShop = async (deniedShopData: any) => {
        const includeOfTags: any = [
            {association: 'tag', include: [], distinct: true},
        ]
        const includeOfCategory: any = [
            {association: 'thema', include: [], distinct: true},
        ]
        const newDeniedShopData = {...deniedShopData}
        const tags = await this.exec(
            ShopTag.findAll({
                where: {
                    shop_id: newDeniedShopData.id,
                },
                include: includeOfTags,
            })
        )

        const category = await this.exec(
            Category.findOne({
                where: {
                    id: newDeniedShopData.category_id,
                },
                include: includeOfCategory,
            })
        )
        newDeniedShopData.tags = tags
        newDeniedShopData.category = category
        return newDeniedShopData
    }

    async rejectAll(params: any, option?: ICrudOption) {
        const t = await this.transaction()
        option.transaction = t
        try {
            const listShopIds = option.filter.id['$in']
            for (let i = 0; i < listShopIds.length; i++) {
                const shopInfo = await this.exec(
                    Shop.findOne({
                        where: {
                            id: listShopIds[i],
                        },
                    })
                )

                await socketService.emitToUser(shopInfo.dataValues.user_id, SOCKET_EVENTS.REJECT_SHOP, shopInfo)

                if (shopInfo) {
                    let updatedData: any = {}
                    // check if that shop have old_shop
                    if (shopInfo.old_shop) {
                        // reject an editting of an approved shop => need to update all fields in old_shop back to outside:
                        const newOldShop = await this.convertedCorrespondShopData(
                            {...shopInfo.dataValues.old_shop},
                            null,
                            null,
                            null
                        )
                        const outsideContent = await this.convertedCorrespondShopData(
                            {
                                ...shopInfo.dataValues,
                                state: SHOP_STATE.REJECTED,
                                denied_message: params.denied_message,
                            },
                            null,
                            null,
                            null
                        )
                        const newDeniedShop = await this.convertedCorrespondShopData(
                            outsideContent,
                            null,
                            null,
                            null
                        )
                        const temp: any = {...newOldShop}

                        temp.denied_shop = await this.getJoiningTableOfDeniedShop({
                            ...newDeniedShop,
                        })
                        temp.old_shop = null
                        updatedData = {...temp}
                    } else {
                        // this is reject an original shop, only need to change the state to REJECTED are enough:
                        updatedData = {
                            state: SHOP_STATE.REJECTED,
                            denied_message: params.denied_message,
                        }
                    }

                    delete updatedData.id
                    delete updatedData.user_id
                    await this.model.update(updatedData, {
                        where: {
                            id: listShopIds[i],
                        },
                        transaction: t,
                    })
                }
            }

            t.commit()
            return {
                success: true,
            }
        } catch (err) {
            t.rollback()
            throw err
        }
    }

    async approveAll(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const listShopIds = option.filter.id['$in']
            option.transaction = transaction
            const shops = []

            for (let i = 0; i < listShopIds.length; i++) {
                const item = await this.exec(
                    this.model.findById(listShopIds[i], {
                        attributes: ['id', 'expired_date', 'user_id', 'state'],
                    }),
                    {
                        allowNull: false,
                    }
                )
                if (item) {
                    shops.push(item)
                }
            }

            // B2: update params to each shop and start edit shop:
            // const approvedNotiList = [];
            const newApprovedList = []
            const reApprovedList = []
            for (let i = 0; i < shops.length; i++) {
                const item = shops[i]
                if (item && item.expired_date && parseInt(item.expired_date)) {
                    reApprovedList.push(item)
                } else {
                    newApprovedList.push(item)
                }
            }

            const newApprovedListIds = newApprovedList.map((e) => e.id)
            const reApprovedListIds = reApprovedList.map((e) => e.id)

            if (newApprovedListIds.length) {
                const body1 = {...params}
                delete body1.user_id
                await this.exec(
                    Shop.update(
                        {
                            ...body1,
                            state: params.state,
                            expired_date: moment(parseInt(params.expired_date))
                                .utc()
                                .utcOffset(TIMEZONE * 60)
                                .valueOf(),
                            start_date: moment()
                                .utc()
                                .utcOffset(TIMEZONE * 60)
                                .valueOf(),
                            old_shop: null,
                            denied_message: null,
                            denied_shop: null,
                            params_update: null,
                        },
                        {
                            where: {
                                id: {$in: newApprovedListIds},
                            },
                            transaction,
                        }
                    )
                )
            }

            if (reApprovedListIds.length) {
                const body2 = {...params}
                delete body2.start_date
                delete body2.user_id
                await this.exec(
                    Shop.update(
                        {
                            ...body2,
                            state: params.state,
                            old_shop: null,
                            denied_message: null,
                            denied_shop: null,
                            params_update: null,
                        },
                        {
                            where: {
                                id: {$in: reApprovedListIds},
                            },
                            transaction,
                        }
                    )
                )
            }

            for (let i = 0; i < shops.length; i++) {
                const item = shops[i]
                if (item.user_id) {
                    // change post counter of user:
                    await this.changeUserPostCounter(
                        SHOP_STATE.APPROVED,
                        item.state,
                        item.user_id,
                        transaction
                    )
                    // cap nhat remaining date:

                    const userShopList = await this.exec(
                        Shop.findAll({
                            where: {
                                $or: [
                                    {
                                        state: SHOP_STATE.APPROVED,
                                    },
                                    {
                                        old_shop: {$ne: null},
                                    },
                                ],
                                user_id: item.user_id,
                            },
                            attributes: ['id', 'state', 'user_id', 'expired_date'],
                            transaction,
                        })
                    )
                    await this.updateUserRemainingDate(
                        userShopList, // add this new item to the list and apply new list
                        item.user_id,
                        transaction
                    )
                }
            }

            // B3: send noti to approvedNotiList, changeExpiredDateNotiList:
            for (let i = 0; i < shops.length; i++) {
                const item = shops[i]
                const message = FCM_ACTIONS_MESSAGE.APPROVE_SHOP
                // firebaseService.sendNotification(
                //     item.user_id,
                //     message,
                //     FCM_ACTIONS.APPROVE_SHOP
                // )

                const bodyNoti: any = {
                    user_id: item.user_id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action: FCM_ACTIONS.APPROVE_SHOP,
                }

                await this.exec(
                    Notification.create(bodyNoti, {
                        transaction,
                    })
                )
            }

            transaction.commit()
            for (const shop of shops) {
                await socketService.emitToUser(shop.user_id, SOCKET_EVENTS.APPROVED_SHOP, shop)
            }
            return {
                code: 200,
            }
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async changeUserPostCounter(
        newStatus: String,
        oldStatus: String,
        user_id: String,
        transaction: any
    ) {
        await userService.updateUserPostType(user_id, transaction)
        if (newStatus === SHOP_STATE.REMOVED) {
            const user = await this.exec(
                User.findOne({
                    where: {
                        id: user_id,
                    },
                    attributes: ['post_limit'],
                    transaction,
                })
            )
            await this.exec(
                User.update(
                    {
                        post_limit: user.post_limit - 1,
                    },
                    {
                        where: {
                            id: user_id,
                        },
                        transaction,
                    }
                )
            )
        }
    }

    async periodicJumpUp() {
        const shopListOptions: ICrudOption = {
            where: {
                jump_interval: {$ne: null},
                next_jump_time: {$lt: moment().valueOf() + 60000 * 5},
            },
            attributes: [
                'id',
                'jump_count',
                'user_id',
                ...SYSTEM_DATA_ATTRIBUTES_ARR,
            ],
            // include: [ // cannot use include here but it will require findOne user because if we use include here, the user data who have multiple jump shops will alway get OLD JUMP_LIMIT to validate, example user A have 3 jump_limit and 5 jump shops, ==> result should be 3 jump shop and 2 invalid shop (0 jump limit). But if we use include (all shop will have 3 jump_limit) and it make all 5 shops validated.
            //   { association: 'user', include: [], distinct: true },
            // ],
        }
        const shops = await this.exec(Shop.findAll(shopListOptions))
        if (shops.length) {
            for (let i = 0; i < shops.length; i++) {
                const shop = shops[i]
                if (shop.user_id) {
                    const user = await this.exec(
                        User.findOne({
                            where: {
                                id: shop.user_id,
                            },
                            attributes: ['jump_limit'],
                        })
                    )
                    const params = {
                        type: 'RECURRING',
                        minutes: shop.jump_interval,
                    }
                    try {
                        await this.setupJumpUpFunc(null, params, shop, user)
                    } catch (error) {
                        console.log(
                            '29148  ~ file: shopService.ts ~ line 684 ~ ShopService ~ periodicJumpUp ~ error',
                            error
                        )
                    }
                }
            }
        }
    }

    async onUpdatePostOwner(
        oldOwnerId: String,
        newOwnerId: String,
        shopInfo: any,
        transaction: any = undefined
    ) {
        const oldOwnerShops = await this.exec(
            Shop.findAll({
                where: {
                    user_id: oldOwnerId,
                },
                transaction,
                attributes: ['id', 'state', 'is_random_20_shop'],
            })
        )
        const newOwnerShops = await this.exec(
            Shop.findAll({
                where: {
                    user_id: newOwnerId,
                },
                transaction,
                attributes: ['id', 'state', 'is_random_20_shop'],
            })
        )

        const filteredOldOwnerList = oldOwnerShops.filter(
            (e: any) => e.id !== shopInfo.id
        )

        const filteredNewOwnerList = newOwnerShops.filter(
            (e: any) => e.id !== shopInfo.id
        )

        const newUpdatedOldOwnerShopList = [...filteredOldOwnerList]
        const newUpdatedNewOwnerShopList = [shopInfo, ...filteredNewOwnerList]

        const oldUserPendingNumber = newUpdatedOldOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.PENDING
        ).length
        const oldUserActiveNumber = newUpdatedOldOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.APPROVED
        ).length
        const oldUserRejectedNumber = newUpdatedOldOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.REJECTED
        ).length
        const oldUserExpiredNumber = newUpdatedOldOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.EXPIRED
        ).length
        const oldCurrentRecommendationPost = newUpdatedNewOwnerShopList.filter(
            (e: any) => e.is_random_20_shop
        ).length

        const newUserPendingNumber = newUpdatedNewOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.PENDING
        ).length
        const newUserActiveNumber = newUpdatedNewOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.APPROVED
        ).length
        const newUserRejectedNumber = newUpdatedNewOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.REJECTED
        ).length
        const newUserExpiredNumber = newUpdatedNewOwnerShopList.filter(
            (e: any) => e.state === SHOP_STATE.EXPIRED
        ).length
        const newCurrentRecommendationPost = newUpdatedNewOwnerShopList.filter(
            (e: any) => e.is_random_20_shop
        ).length

        const oldObj: any = {
            current_active_post: oldUserActiveNumber,
            current_pending_post: oldUserPendingNumber,
            current_rejected_post: oldUserRejectedNumber,
            current_expired_post: oldUserExpiredNumber,
            current_recommendation_post: oldCurrentRecommendationPost,
        }

        const newObj: any = {
            current_active_post: newUserActiveNumber,
            current_pending_post: newUserPendingNumber,
            current_rejected_post: newUserRejectedNumber,
            current_expired_post: newUserExpiredNumber,
            current_recommendation_post: newCurrentRecommendationPost,
        }
        if (oldOwnerId) {
            await this.exec(
                User.update(oldObj, {
                    where: {
                        id: oldOwnerId,
                    },
                    transaction,
                })
            )
        }

        if (newOwnerId) {
            await this.exec(
                User.update(newObj, {
                    where: {
                        id: newOwnerId,
                    },
                    transaction,
                })
            )
        }
    }

    async checkPostLimit(user_id: String, transaction: any = undefined) {
        const user = await this.exec(
            User.findOne({
                where: {
                    id: user_id,
                },
                attributes: [
                    'current_active_post',
                    'current_pending_post',
                    'post_limit',
                ],
                transaction: transaction,
            })
        )

        if (user) {
            const USED_POST_NUMBER =
                user.current_active_post + user.current_pending_post
            if (user.post_limit === 0 || user.post_limit <= USED_POST_NUMBER) {
                // check post limit
                throw errorService.database.queryFail(
                    'this user have reached post limit number, please contract administrator to extends limit'
                )
            }
            // post dc admin approved:
        }
    }

    async forceExpiredMultiple(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()

        try {
            // days
            const finalIncreaseDayNum = params.days

            const listShopIds = option.filter.id['$in']
            option.transaction = transaction

            const shops = await this.exec(
                Shop.findAll({
                    attributes: [
                        'id',
                        'title',
                        'user_id',
                        'expired_date',
                        'state',
                        ...SYSTEM_DATA_ATTRIBUTES_ARR,
                    ],
                    where: {
                        id: {$in: listShopIds},
                    },
                })
            )

            const currentTimeUnix = moment
                .utc()
                .utcOffset(TIMEZONE * 60)
                .valueOf()
            if (shops && shops.length > 0) {
                const listExpiredShop = []
                const listReducedTimeShop = []
                for (let i = 0; i < shops.length; i++) {
                    const shop = shops[i]
                    const newExpiredTime =
                        parseInt(shop.expired_date, 10) -
                        60 * 60 * 24 * 1000 * finalIncreaseDayNum
                    // ngay cu  ==> con 60 ngay so w ngay hien tai
                    // ngay moi ==> con 20 ngay so w ngay hien tai
                    // ngay moi < hien tai ==> expired
                    if (newExpiredTime <= currentTimeUnix) {
                        listExpiredShop.push(shop)
                    } else {
                        listReducedTimeShop.push(shop)
                    }
                }

                if (listExpiredShop && listExpiredShop.length) {
                    // set expired for shop do not have old_shop field
                    const defaultShopIds = listExpiredShop
                        .filter((e: any) => e && !e.old_shop)
                        .map((e: any) => e.id)
                    await this.exec(
                        Shop.update(
                            {
                                state: SHOP_STATE.EXPIRED,
                                expired_date: currentTimeUnix,
                            },
                            {
                                where: {
                                    id: {$in: defaultShopIds},
                                    old_shop: null,
                                },
                                transaction,
                            }
                        )
                    )

                    // set expired for shop have old_shop field
                    const oldShopShopIds = listExpiredShop.filter(
                        (e: any) => e && e.old_shop
                    )
                    for (let i = 0; i < oldShopShopIds.length; i++) {
                        const shopInfo = oldShopShopIds[i]
                        if (shopInfo && shopInfo.old_shop) {
                            const oldShopData = shopInfo.old_shop
                            oldShopData.state = SHOP_STATE.EXPIRED
                            oldShopData.expired_date = currentTimeUnix
                            await this.exec(
                                Shop.update(
                                    {
                                        old_shop: oldShopData,
                                    },
                                    {
                                        where: {
                                            id: shopInfo.id,
                                        },
                                        transaction,
                                    }
                                )
                            )
                        }
                    }

                    for (let i = 0; i < listExpiredShop.length; i++) {
                        if (shops[i].user_id) {
                            await this.doExpiredPost(listExpiredShop[i], transaction)
                        }
                    }
                }

                if (listReducedTimeShop && listReducedTimeShop.length) {
                    //
                    for (let i = 0; i < listReducedTimeShop.length; i++) {
                        const item = listReducedTimeShop[i]
                        const shop_state = SHOP_STATE.APPROVED
                        const newExpiredTime =
                            parseInt(item.expired_date, 10) -
                            60 * 60 * 24 * 1000 * finalIncreaseDayNum
                        const constantShopInfo = {expired_date: newExpiredTime}
                        const shopInfo = {...item}
                        const syncedData = this.syncSystemData(shopInfo, constantShopInfo)
                        syncedData.state = shop_state
                        const outsideContent = await this.convertedCorrespondShopData(
                            syncedData,
                            null,
                            null,
                            null
                        )
                        const body: any = {...outsideContent}
                        delete body.id
                        delete body.user_id

                        const newItem = await this.exec(item.update(body, {transaction}))
                        if (item.user_id) {
                            // change post counter of user:
                            await this.changeUserPostCounter(
                                shop_state,
                                item.state,
                                item.user_id,
                                transaction
                            )
                            // cap nhat remaining date:

                            const userShopList = await this.exec(
                                Shop.findAll({
                                    where: {
                                        $or: [
                                            {
                                                state: SHOP_STATE.APPROVED,
                                            },
                                            {
                                                old_shop: {$ne: null},
                                            },
                                        ],
                                        user_id: item.user_id,
                                    },
                                    attributes: ['id', 'state', 'user_id', 'expired_date'],
                                    transaction,
                                })
                            )
                            await this.updateUserRemainingDate(
                                userShopList, // add this new item to the list and apply new list
                                item.user_id,
                                transaction
                            )
                        }
                    }

                    // noti
                    const changeExpiredDateNotiList = listReducedTimeShop
                    for (let i = 0; i < changeExpiredDateNotiList.length; i++) {
                        const item = changeExpiredDateNotiList[i]
                        const oldExpiredDateUnix = moment
                            .utc(
                                parseInt(item.expired_date) ? parseInt(item.expired_date) : null
                            )
                            .utcOffset(TIMEZONE * 60)
                            .valueOf()

                        const newExpiredTime =
                            parseInt(item.expired_date, 10) -
                            60 * 60 * 24 * 1000 * finalIncreaseDayNum
                        const newExpiredDateUnix = newExpiredTime

                        const return_data = {
                            params_1: item.title,
                            params_2: moment(newExpiredDateUnix)
                                .utc()
                                .utcOffset(TIMEZONE * 60)
                                .set({hour: 12, minute: 0, second: 0})
                                .format('YYYY-MM-DD'),
                        }
                        const fcmMessage =
                            newExpiredDateUnix > oldExpiredDateUnix
                                ? FCM_ACTIONS_MESSAGE.EXTEND_SHOP_EXPIRE_DATE
                                : FCM_ACTIONS_MESSAGE.DECREASE_SHOP_EXPIRE_DATE
                        const fcmAction =
                            newExpiredDateUnix > oldExpiredDateUnix
                                ? FCM_ACTIONS.EXTEND_SHOP_EXPIRE_DATE
                                : FCM_ACTIONS.DECREASE_SHOP_EXPIRE_DATE

                        const message = getFcmActionMessage(fcmMessage, return_data)

                        // await firebaseService.sendNotification(
                        //     item.user_id,
                        //     message,
                        //     fcmAction,
                        //     item.id
                        // )

                        const entity_id = item.id
                        const bodyNoti: any = {
                            user_id: item.user_id,
                            title: message,
                            content: message,
                            data: {
                                message: message,
                                params: return_data,
                            },
                            action: fcmAction,
                            interacting_type:
                                newExpiredDateUnix > oldExpiredDateUnix
                                    ? 'EXTEND_SHOP_EXPIRE_DATE'
                                    : 'DECREASE_SHOP_EXPIRE_DATE',
                            interacting_content_id: entity_id,
                        }

                        await this.exec(Notification.create(bodyNoti))
                    }
                }
            }
            // return { status: shops.length };
            transaction.commit()
            return {
                code: 200,
            }
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
            transaction.rollback()
            throw error
        }
    }

    async updateExpirationDateMultiple(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        // 1604240431328 2 thang nua
        // 1599672973000 hien tai
        try {
            const dayNums = params.days
            const timeEnd = params.expired_date ? parseInt(params.expired_date) : null
            let finalIncreaseDayNum = dayNums
            const listShopIds = option.filter.id['$in']
            option.transaction = transaction
            const shops = []
            for (let i = 0; i < listShopIds.length; i++) {
                const item = await this.exec(
                    this.model.findById(listShopIds[i], {
                        attributes: [
                            'id',
                            'title',
                            'user_id',
                            'expired_date',
                            'state',
                            ...SYSTEM_DATA_ATTRIBUTES_ARR,
                        ],
                    }),
                    {
                        allowNull: false,
                    }
                )
                if (item) {
                    shops.push(item)
                }
            }
            // B1: validate condition for update:
            for (let i = 0; i < shops.length; i++) {
                const item = shops[i]
                // kiem tra post limit neu la truong hop approved 1 shop hoac gia han cho 1 shop da het han:
                // if (
                //   item.state !== SHOP_STATE.PENDING &&
                //   item.state !== SHOP_STATE.APPROVED &&
                //   (item.state === SHOP_STATE.EXPIRED || !parseInt(item.expired_date)) &&
                //   item.user_id
                // ) {
                //   await this.checkPostLimit(item.user_id, transaction)
                // }

                const oldExpiredDateUnix = moment
                    .utc(parseInt(item.expired_date) ? parseInt(item.expired_date) : null)
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()

                const anchorTime =
                    oldExpiredDateUnix > 0 &&
                    oldExpiredDateUnix >
                    moment()
                        .utc()
                        .utcOffset(TIMEZONE * 60)
                        .valueOf()
                        ? oldExpiredDateUnix
                        : moment()
                            .utc()
                            .utcOffset(TIMEZONE * 60)
                            .valueOf()

                if (!finalIncreaseDayNum) {
                    finalIncreaseDayNum = Math.ceil(
                        (moment(timeEnd)
                                .utc()
                                .utcOffset(TIMEZONE * 60)
                                .valueOf() -
                            anchorTime) /
                        (1000 * 60 * 60 * 24)
                    )
                }
                const newExpiredDate =
                    anchorTime + finalIncreaseDayNum * (60 * 60 * 24 * 1000)

                const newExpiredDateUnix = moment
                    .utc(newExpiredDate)
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()

                const eventOfShop = await this.exec(
                    Event.findOne({
                        where: {
                            shop_id: item.id,
                        },
                        attributes: ['end_time'],
                        transaction,
                    })
                )

                if (eventOfShop) {
                    const expiredTimeOfEvent = moment
                        .utc(parseInt(eventOfShop.end_time))
                        .utcOffset(TIMEZONE * 60)
                        .valueOf()
                    if (newExpiredDateUnix <= expiredTimeOfEvent) {
                        throw errorService.database.queryFail(
                            `End time of shop ${item.title} cannot less than end time of shops's event`
                        )
                    }
                }
            }

            // B2: update params to each shop and start edit shop:
            const approvedNotiList = []
            const changeExpiredDateNotiList = []
            for (let i = 0; i < shops.length; i++) {
                const item = shops[i]
                const timeNow = moment
                    .utc()
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()
                const oldExpiredDateUnix = moment
                    .utc(parseInt(item.expired_date) ? parseInt(item.expired_date) : null)
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()

                const anchorTime =
                    oldExpiredDateUnix > 0 &&
                    oldExpiredDateUnix >
                    moment()
                        .utc()
                        .utcOffset(TIMEZONE * 60)
                        .valueOf()
                        ? oldExpiredDateUnix
                        : moment()
                            .utc()
                            .utcOffset(TIMEZONE * 60)
                            .valueOf()

                if (!finalIncreaseDayNum) {
                    finalIncreaseDayNum = Math.ceil(
                        (moment(timeEnd)
                                .utc()
                                .utcOffset(TIMEZONE * 60)
                                .valueOf() -
                            anchorTime) /
                        (1000 * 60 * 60 * 24)
                    )
                }
                const newExpiredDate =
                    anchorTime + finalIncreaseDayNum * (60 * 60 * 24 * 1000)

                const newExpiredDateUnix = moment
                    .utc(newExpiredDate)
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()

                const shop_state =
                    newExpiredDateUnix < timeNow
                        ? SHOP_STATE.EXPIRED
                        : SHOP_STATE.APPROVED

                const constantShopInfo = {
                    state: shop_state,
                    expired_date: newExpiredDateUnix,
                }
                const shopInfo = {...item}
                const syncedData = this.syncSystemData(shopInfo, constantShopInfo)
                syncedData.state = shop_state
                const outsideContent = await this.convertedCorrespondShopData(
                    syncedData,
                    null,
                    null,
                    null
                )
                const body: any = {...outsideContent}
                if (
                    shop_state === SHOP_STATE.APPROVED &&
                    item.state !== SHOP_STATE.APPROVED
                ) {
                    // cập nhật start_date và gửi notification cho user:
                    body.start_date = anchorTime
                    if (item.user_id) {
                        approvedNotiList.push(item)
                    }
                } else if (
                    shop_state === SHOP_STATE.EXPIRED &&
                    item.state !== SHOP_STATE.EXPIRED
                ) {
                    // bắn noti expired
                    if (item.user_id) {
                        await this.doExpiredPost({...item, ...body}, transaction)
                    }
                } else if (newExpiredDateUnix !== oldExpiredDateUnix) {
                    // extend hoac decrease expire_date
                    changeExpiredDateNotiList.push({...item.dataValues})
                }

                delete body.id
                delete body.user_id
                // item.update code
                const newItem = await this.exec(item.update(body, {transaction}))
                if (item.user_id) {
                    // change post counter of user:
                    // await this.changeUserPostCounter(
                    //   shop_state,
                    //   item.state,
                    //   item.user_id,
                    //   transaction
                    // )
                    // cap nhat remaining date:

                    const userShopList = await this.exec(
                        Shop.findAll({
                            where: {
                                $or: [
                                    {
                                        state: SHOP_STATE.APPROVED,
                                    },
                                    {
                                        old_shop: {$ne: null},
                                    },
                                ],
                                user_id: item.user_id,
                            },
                            attributes: ['id', 'state', 'user_id', 'expired_date'],
                            transaction,
                        })
                    )
                    await this.updateUserRemainingDate(
                        userShopList, // add this new item to the list and apply new list
                        item.user_id,
                        transaction
                    )

                    await this.reCountShop(item.user_id, transaction)
                }
            }

            // B3: send noti to approvedNotiList, changeExpiredDateNotiList:
            for (let i = 0; i < approvedNotiList.length; i++) {
                const item = approvedNotiList[i]
                const message = FCM_ACTIONS_MESSAGE.APPROVE_SHOP
                // firebaseService.sendNotification(
                //     item.user_id,
                //     message,
                //     FCM_ACTIONS.APPROVE_SHOP
                // )

                const bodyNoti: any = {
                    user_id: item.user_id,
                    title: message,
                    content: message,
                    data: {message: message},
                    action: FCM_ACTIONS.APPROVE_SHOP,
                }

                await this.exec(
                    Notification.create(bodyNoti, {
                        transaction,
                    })
                )
            }

            for (let i = 0; i < changeExpiredDateNotiList.length; i++) {
                const item = changeExpiredDateNotiList[i]
                const oldExpiredDateUnix = moment
                    .utc(parseInt(item.expired_date) ? parseInt(item.expired_date) : null)
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()

                const anchorTime =
                    oldExpiredDateUnix > 0 &&
                    oldExpiredDateUnix >
                    moment()
                        .utc()
                        .utcOffset(TIMEZONE * 60)
                        .valueOf()
                        ? oldExpiredDateUnix
                        : moment()
                            .utc()
                            .utcOffset(TIMEZONE * 60)
                            .valueOf()

                if (!finalIncreaseDayNum) {
                    finalIncreaseDayNum = Math.ceil(
                        (moment(timeEnd)
                                .utc()
                                .utcOffset(TIMEZONE * 60)
                                .valueOf() -
                            anchorTime) /
                        (1000 * 60 * 60 * 24)
                    )
                }
                const newExpiredDate =
                    anchorTime + finalIncreaseDayNum * (60 * 60 * 24 * 1000)

                const newExpiredDateUnix = moment
                    .utc(newExpiredDate)
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()

                const return_data = {
                    params_1: item.title,
                    params_2: moment(newExpiredDateUnix)
                        .utc()
                        .utcOffset(TIMEZONE * 60)
                        .set({hour: 12, minute: 0, second: 0})
                        .format('YYYY-MM-DD'),
                }
                const fcmMessage =
                    newExpiredDateUnix > oldExpiredDateUnix
                        ? FCM_ACTIONS_MESSAGE.EXTEND_SHOP_EXPIRE_DATE
                        : FCM_ACTIONS_MESSAGE.DECREASE_SHOP_EXPIRE_DATE
                const fcmAction =
                    newExpiredDateUnix > oldExpiredDateUnix
                        ? FCM_ACTIONS.EXTEND_SHOP_EXPIRE_DATE
                        : FCM_ACTIONS.DECREASE_SHOP_EXPIRE_DATE

                const message = getFcmActionMessage(fcmMessage, return_data)

                // await firebaseService.sendNotification(
                //     item.user_id,
                //     message,
                //     fcmAction,
                //     item.id
                // )

                const entity_id = item.id
                const bodyNoti: any = {
                    user_id: item.user_id,
                    title: message,
                    content: message,
                    data: {
                        message: message,
                        params: return_data,
                    },
                    action: fcmAction,
                    interacting_type:
                        newExpiredDateUnix > oldExpiredDateUnix
                            ? 'EXTEND_SHOP_EXPIRE_DATE'
                            : 'DECREASE_SHOP_EXPIRE_DATE',
                    interacting_content_id: entity_id,
                }
                console.log(
                    '29148 : ShopService -> scheduleNotiBeforeExpiredShop -> bodyNoti',
                    bodyNoti
                )

                await this.exec(Notification.create(bodyNoti))
            }

            transaction.commit()
            return {
                code: 200,
            }
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    //

    async updateExpirationDateFunc(params: any, option?: ICrudOption) {
        throw errorService.database.queryFail('no more support this api', 500)
    }

    async setupJumpUpFunc(
        transaction: any,
        params: any,
        shopInfo: any,
        ownerInfo: any
    ) {
        const {type, minutes} = params
        const bodyShop: any = {}
        const bodyShopOwner: any = {}
        // update params:

        const jump_limit_left = ownerInfo.jump_limit
        if (jump_limit_left <= 0) {
            // show error
            bodyShop.jump_interval = null
            bodyShop.next_jump_time = null

            const convertedBodyData = this.syncSystemData(shopInfo, bodyShop)
            delete convertedBodyData.user_id

            await this.exec(
                Shop.update(convertedBodyData, {
                    where: {
                        id: shopInfo.id,
                    },
                })
            )
            throw errorService.database.queryFail(
                'jump up limit is insufficient',
                JUMP_UP_LIMIT_IS_INSUFFICIENT
            )
            return
        }

        if (type === 'RECURRING' && minutes > 0) {
            bodyShop.jump_interval = minutes
            bodyShop.next_jump_time = moment().valueOf() + minutes * 60 * 1000
        } else {
            bodyShop.jump_interval = null
            bodyShop.next_jump_time = null
        }
        bodyShop.jump_count = shopInfo.jump_count + 1
        bodyShop.jump_order = moment().valueOf()

        const convertedBodyData = this.syncSystemData(shopInfo, bodyShop)

        bodyShopOwner.jump_limit = jump_limit_left - 1

        // update to db:
        await this.exec(
            Shop.update(convertedBodyData, {
                where: {
                    id: shopInfo.id,
                },
                transaction,
            })
        )
        await this.exec(
            User.update(bodyShopOwner, {
                where: {
                    id: shopInfo.user_id,
                },
                transaction,
            })
        )
        // add jump up record history:
        const historyBody: any = {
            shop_id: shopInfo.id,
            user_id: shopInfo.user_id,
            category: HISTORY.SHOP.NAME,
            type_1: HISTORY.SHOP.TYPES.JUMP_UP,
        }
        await this.exec(
            History.create(historyBody, {
                transaction,
            })
        )
        // )
    }

    async setupJumpUpAPI(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        // 1604240431328 2 thang nua
        // 1599672973000 hien tai
        const body: any = {
            type: params.type,
            minutes: params.minutes,
        }
        try {
            const item = await this.exec(
                this.model.findOne({
                    where: {
                        id: option.filter.id,
                    },
                    attributes: [
                        'id',
                        'jump_count',
                        'user_id',
                        ...SYSTEM_DATA_ATTRIBUTES_ARR,
                    ],
                })
            )

            const shopOwner = await this.exec(
                User.findOne({
                    where: {
                        id: item.user_id,
                    },
                    attributes: ['jump_limit'],
                })
            )

            if (shopOwner) {
                await this.setupJumpUpFunc(transaction, body, item, shopOwner)
            }

            transaction.commit()
            return await this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async turnOnJumpUpAPI(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })

            if (item && !item.jump_interval) {
                throw errorService.database.queryFail(
                    'cannot jump up without setup interval',
                    CANNOT_JUMP_UP_RECURRING
                )
            }

            const body: any = {
                next_jump_time: moment().valueOf() + item.jump_interval * 60 * 1000,
            }

            await this.exec(
                Shop.update(body, {
                    where: {
                        id: item.id,
                    },
                    transaction,
                })
            )

            transaction.commit()
            return await this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async turnOffJumpUpAPI(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })

            if (item && !item.jump_interval) {
                throw errorService.database.queryFail(
                    'shop does not have jump up interval',
                    CANNOT_TURN_OFF_JUMP_UP_RECURRING
                )
            }

            const body: any = {
                next_jump_time: null,
            }

            await this.exec(
                Shop.update(body, {
                    where: {
                        id: item.id,
                    },
                    transaction,
                })
            )

            transaction.commit()
            return await this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async removeJumpUp(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })

            if (item && !item.jump_interval) {
                throw errorService.database.queryFail(
                    'shop does not have jump up interval',
                    CANNOT_TURN_OFF_JUMP_UP_RECURRING
                )
            }

            const body: any = {
                jump_interval: null,
            }

            await this.exec(
                Shop.update(body, {
                    where: {
                        id: item.id,
                    },
                    transaction,
                })
            )

            transaction.commit()
            return await this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async update(params: any, option?: ICrudOption) {
        const {tag_ids, account_type, user_role, time_ranges, employee_id, change_owner} = params
        delete params.expired_date

        const transaction = await sequelize.transaction()
        const isNormalUser = user_role === ROLE.USER
        try {
            
            let videos = null
            if (params.videos && Array.isArray(params.videos)) {
                videos = params.videos
                delete params.videos  
            }
            if (params.slug === '') {
                delete params.slug
            }
            if (params.description === '') {
                throw errorService.database.queryFail(
                    'description cannot be empty',
                    DESCRIPTION_CANNOT_BE_EMPTY
                )
            }
            if (params.images && params.images.length) {
                const filteredListImgs = params.images.filter(
                    (e: any) => !isBase64EncodeStr(e)
                )
                params.images = filteredListImgs
            }
            if (params.thumbnails && params.thumbnails.length) {
                const filteredListImgs = params.thumbnails.filter(
                    (e: any) => !isBase64EncodeStr(e)
                )
                params.thumbnails = filteredListImgs
            }
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })
            if (params.slug && params.slug !== item.slug) {
                let slug = generateSlug(params.slug)
                const checkSlug = await this.model.findOne({where: {slug}})
                if (checkSlug && checkSlug.id !== item.id) {
                    slug = slug + '-' + uuid.v4()
                }
                params.slug = slug
            }
            // if (params.user_id && params.user_id !== item.user_id) {
            //     if (!employee_id || !change_owner) throw errorService.database.queryFail('User is not owner of this shop')
            // }
            const CHANGE_OWNERSHIP_FLOW =
                params.user_id && params.user_id !== item.user_id
            const CHANGE_STATE_FLOW = params.state && params.state !== item.state
            const CHANGE_RECOMMENDATION =
                params.is_random_20_shop !== item.is_random_20_shop

            if (params.opening_hours && params.opening_hours !== item.opening_hours) {
                const regex2 = /\d\d:\d\d~\d\d:\d\d/g
                if (!regex2.test(params.opening_hours)) {
                    throw errorService.database.queryFail('Wrong format opening_hours')
                }
            }

            // check and remove base64 image from description:
            // check and remove base64 image from description:
            params.description = removeBase64EncodeStr(params.description)

            // check should use default geolocation to get new latitude and longtude:
            if (params.use_server_geolocation) {
                //
                const geocoder = NodeGeocoder(options)
                const result = await geocoder.geocode(` ${params.address}`)
                if (result && result.length > 0 && result[0]) {
                    params.latitude = result[0].latitude
                    params.longitude = result[0].longitude
                } else {
                    throw errorService.database.queryFail(
                        '검색한 상점주소가 확인되지 않습니다. 정확한 주소정보를 입력해주세요'
                    )
                }
            }

            // if (item.old_shop) {
            //   params.old_shop = { ...item.old_shop, description: removeBase64EncodeStr(item.old_shop.description)};
            // }
            // if (item.denied_shop) {
            //   params.denied_shop = { ...item.denied_shop, description: removeBase64EncodeStr(item.denied_shop.description)};
            // }
            if (item.params_update) {
                params.params_update = {
                    ...item.params_update,
                    description: removeBase64EncodeStr(item.params_update.description),
                }
            }

            if (CHANGE_OWNERSHIP_FLOW && employee_id && change_owner) {
                await Event.update({user_id: params.user_id}, {where: {shop_id: item.id}})
                // const haveEvent = await this.exec(
                //     Event.findAll({
                //         where: {
                //             shop_id: item.id,
                //         },
                //     })
                // )
                // if (haveEvent && haveEvent.length) {
                //     throw errorService.database.queryError(
                //         ERROR.CANNOT_DELETE_SHOP_ALREADY_HAVE_EVENT
                //     )
                // }
                // đổi owner flow:
                // B0: kiểm tra xem người nhận có phải biz account không:
                const receiverUserInfo = await this.exec(
                    User.findOne({
                        where: {
                            id: params.user_id,
                        },
                        attributes: ['account_type'],
                        transaction,
                    })
                )
                if (
                    receiverUserInfo &&
                    receiverUserInfo.account_type !== USER_TYPE.BIZ_USER
                ) {
                    throw errorService.database.queryFail(
                        'Receiver must be biz account to get ownership'
                    )
                }
                await this.exec(
                    User.update(
                        {
                            post_limit: sequelize.literal(`"post_limit" + 1`),
                        },
                        {
                            where: {
                                id: params.user_id,
                            },
                            transaction,
                        }
                    )
                )

                // B1: kiểm tra xem người nhận đủ điều kiện (post_limit) không:
                // await this.checkPostLimit(params.user_id, transaction)
                // B2 gắn body:

                const newShopInfo = {...item, ...params}

                const receiver_id = params.user_id
                const receivee_id = item.user_id
                // B4: gọi api update:
                delete newShopInfo.id
                await this.exec(item.update(newShopInfo, {transaction}))
                // B3 update sl active, pending, rejected, expire của người bị chuyển và người nhận:
                await this.onUpdatePostOwner(
                    receivee_id,
                    receiver_id,
                    newShopInfo,
                    transaction
                )
                // B4 cập nhật lại remaining_date của người bị chuyển và người nhận:
                const receiverShopList = await this.exec(
                    Shop.findAll({
                        where: {
                            state: SHOP_STATE.APPROVED,
                            user_id: receiver_id,
                        },
                        transaction,
                    })
                )
                const receiveeShopList = await this.exec(
                    Shop.findAll({
                        where: {
                            state: SHOP_STATE.APPROVED,
                            user_id: receivee_id,
                        },
                        transaction,
                    })
                )
                await this.updateUserRemainingDate(
                    receiverShopList,
                    receiver_id,
                    transaction
                )
                await this.updateUserRemainingDate(
                    receiveeShopList,
                    receivee_id,
                    transaction
                )

                // B5: bắn notification cho 2 user:
                // B5.1: notification của receiver:
                if (receiver_id) {
                    const receivee_info = await this.exec(
                        User.findOne({
                            where: {
                                id: receivee_id,
                            },
                            attributes: ['nickname'],
                        })
                    )

                    const receiver_noti_data = {
                        params_1: item.title,
                        params_2:
                            receivee_info && receivee_info.nickname
                                ? receivee_info.nickname
                                : 'Administrator',
                    }

                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.TRANSFER_SHOP_OWNERSHIP,
                        receiver_noti_data
                    )

                    // await firebaseService.sendNotification(
                    //     receiver_id,
                    //     message,
                    //     FCM_ACTIONS.TRANSFER_SHOP_OWNERSHIP,
                    //     item.id
                    // )
                    const entity_id = item.id
                    const bodyNoti: any = {
                        user_id: receiver_id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params: receiver_noti_data,
                        },
                        action: FCM_ACTIONS.TRANSFER_SHOP_OWNERSHIP,
                        interacting_type: 'TRANSFER_SHOP_OWNERSHIP',
                        interacting_content_id: entity_id,
                    }
                    await this.exec(Notification.create(bodyNoti))
                }
                // B5.2: notification của receivee:
                if (receivee_id) {
                    const receivee_noti_data = {
                        params_1: item.title,
                    }
                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.REMOVE_SHOP_OWNERSHIP,
                        receivee_noti_data
                    )

                    // await firebaseService.sendNotification(
                    //     receivee_id,
                    //     message,
                    //     FCM_ACTIONS.REMOVE_SHOP_OWNERSHIP,
                    //     item.id
                    // )
                    const entity_id = item.id
                    const bodyNoti: any = {
                        user_id: receivee_id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params: receivee_noti_data,
                        },
                        action: FCM_ACTIONS.REMOVE_SHOP_OWNERSHIP,
                        interacting_type: 'REMOVE_SHOP_OWNERSHIP',
                        interacting_content_id: entity_id,
                    }
                    await this.exec(Notification.create(bodyNoti))
                }
            } else if (CHANGE_STATE_FLOW) {
                // đổi state flow
                // B1 kiểm tra trường hợp đổi:
                const IS_REJECTED_POST =
                    params.state === SHOP_STATE.REJECTED &&
                    item.state !== SHOP_STATE.REJECTED // reject post

                const IS_RESUBMIT_POST =
                    params.state === SHOP_STATE.PENDING &&
                    item.state !== SHOP_STATE.PENDING // resubmit lại post

                const IS_APPROVED_POST =
                    params.state === SHOP_STATE.APPROVED &&
                    item.state !== SHOP_STATE.APPROVED

                const IS_EDIT_AFTER_APPROVED =
                    params.old_shop && params.state === SHOP_STATE.PENDING

                const IS_EDIT_AN_REJECTED_AFTER_APPROVED =
                    params.state === SHOP_STATE.PENDING &&
                    item.state === SHOP_STATE.APPROVED &&
                    item.denied_shop

                const OLD_STATE = item.state
                const NEW_STATE = params.state

                let newShopInfo: any = {}
                newShopInfo = {
                    ...params,
                }
                const shopInfo = {...item}

                if (
                    IS_APPROVED_POST ||
                    IS_REJECTED_POST ||
                    IS_RESUBMIT_POST ||
                    IS_EDIT_AFTER_APPROVED ||
                    IS_EDIT_AN_REJECTED_AFTER_APPROVED
                ) {
                    ////////////// BEGIN
                    //
                    if (IS_APPROVED_POST) {
                        newShopInfo.start_date = moment()
                            .utc()
                            .utcOffset(TIMEZONE * 60)
                            .valueOf()
                    }

                    // check validate post limit for RESUBMIT case
                    if (
                        IS_RESUBMIT_POST &&
                        item.state !== SHOP_STATE.APPROVED &&
                        item.user_id
                    ) {
                        await this.checkPostLimit(item.user_id, transaction)
                    }

                    /// binhbinh start
                    const outsideContent = await this.convertedCorrespondShopData(
                        {...item.dataValues},
                        null,
                        null,
                        null
                    )
                    if (IS_EDIT_AN_REJECTED_AFTER_APPROVED) {
                        // o ngoai item dang la apporoved
                        const newOldShop = await this.convertedCorrespondShopData(
                            {...item.dataValues, state: SHOP_STATE.APPROVED},
                            null,
                            null,
                            null
                        )
                        const newDeniedShop = await this.convertedCorrespondShopData(
                            {...item.denied_shop},
                            null,
                            null,
                            item.denied_shop.denied_message
                        )
                        const outsideContent = await this.convertedCorrespondShopData(
                            {...item.dataValues, ...newShopInfo},
                            null,
                            null,
                            null
                        )

                        const temp: any = outsideContent
                        temp.old_shop = newOldShop

                        temp.denied_shop = newDeniedShop
                        newShopInfo = temp
                    }
                    if (IS_EDIT_AFTER_APPROVED) {
                        //
                        const outsideContent = await this.convertedCorrespondShopData(
                            {...params},
                            null,
                            null,
                            null
                        )
                        // phai coi truoc do co 1 cai nao da reject chua
                        if (shopInfo.dataValues && shopInfo.dataValues.denied_shop) {
                            // van giu nguyen cai reject do
                            const newDeniedShop = await this.convertedCorrespondShopData(
                                {
                                    ...shopInfo.dataValues.denied_shop,
                                    state: SHOP_STATE.REJECTED,
                                },
                                null,
                                null,
                                shopInfo.dataValues.denied_shop.denied_message
                            )
                            newShopInfo = {...outsideContent, ...newShopInfo}
                            if (newDeniedShop) {
                                newShopInfo = {
                                    ...newShopInfo,
                                    denied_shop: {...newDeniedShop},
                                }
                            }
                        }
                    } else if (item.old_shop) {
                        //
                        if (IS_APPROVED_POST && item.state === SHOP_STATE.PENDING) {
                            // approved 1 cai edit
                            const outsideContent = await this.convertedCorrespondShopData(
                                {...shopInfo.dataValues, ...newShopInfo},
                                null,
                                null,
                                null
                            )
                            newShopInfo = outsideContent
                        } else if (IS_REJECTED_POST && item.state === SHOP_STATE.PENDING) {
                            // rejected 1 cai edit
                            // reject update state ben ngoai thanh reject va dem bỏ vao denied_shop
                            // dem old_Shop o trong ra ngoai
                            const newOldShop = await this.convertedCorrespondShopData(
                                {...shopInfo.dataValues.old_shop},
                                null,
                                null,
                                null
                            )
                            const outsideContent = await this.convertedCorrespondShopData(
                                {...shopInfo.dataValues, ...newShopInfo},
                                null,
                                null,
                                null
                            )
                            const newDeniedShop = await this.convertedCorrespondShopData(
                                {...outsideContent, state: SHOP_STATE.REJECTED},
                                null,
                                null,
                                params.denied_message
                            )

                            const temp: any = {...newOldShop}
                            temp.denied_shop = await this.getJoiningTableOfDeniedShop({
                                ...newDeniedShop,
                            })
                            temp.old_shop = null
                            newShopInfo = {...temp}
                        } else {
                            throw errorService.database.queryFail(
                                `state not supported: ${item.state} -> ${params.state}`
                            )
                        }
                    } else {
                        if (IS_APPROVED_POST) {
                            //
                            newShopInfo = {...outsideContent, ...newShopInfo}
                        } else if (IS_REJECTED_POST) {
                            //
                            newShopInfo = {...outsideContent, ...newShopInfo}
                        } else if (IS_RESUBMIT_POST) {
                            //
                            newShopInfo = {...outsideContent, ...newShopInfo}
                        }
                    }

                    delete newShopInfo.id
                    await this.exec(item.update(newShopInfo, {transaction}))

                    /// binhbinh end

                    // show noti:
                    if (IS_APPROVED_POST) {
                        // B1: gửi notification cho user:
                        if (item.user_id) {
                            const message = FCM_ACTIONS_MESSAGE.APPROVE_SHOP
                            // firebaseService.sendNotification(
                            //     item.user_id,
                            //     message,
                            //     FCM_ACTIONS.APPROVE_SHOP
                            // )

                            const bodyNoti: any = {
                                user_id: item.user_id,
                                title: message,
                                content: message,
                                data: {message: message},
                                action: FCM_ACTIONS.APPROVE_SHOP,
                            }

                            await this.exec(
                                Notification.create(bodyNoti, {
                                    transaction,
                                })
                            )
                        }
                    }
                    if (IS_REJECTED_POST) {
                        // B2: gửi noti thông báo reject đến user nếu thuộc dạng reject:
                        // gửi notification cho user:
                        if (item.user_id) {
                            const message = FCM_ACTIONS_MESSAGE.REJECT_SHOP
                            // firebaseService.sendNotification(
                            //     item.user_id,
                            //     message,
                            //     FCM_ACTIONS.REJECT_SHOP
                            // )

                            const bodyNoti: any = {
                                user_id: item.user_id,
                                title: message,
                                content: message,
                                data: {message: message},
                                action: FCM_ACTIONS.REJECT_SHOP,
                            }

                            await this.exec(
                                Notification.create(bodyNoti, {
                                    transaction,
                                })
                            )
                        }
                    }

                    // B3: update lại post counter và remaining date:
                    if (item.user_id) {
                        // change post counter of user:
                        await this.changeUserPostCounter(
                            NEW_STATE,
                            OLD_STATE,
                            item.user_id,
                            transaction
                        )

                        // cap nhat remaining date:

                        const userShopList = await this.exec(
                            Shop.findAll({
                                where: {
                                    $or: [
                                        {
                                            state: SHOP_STATE.APPROVED,
                                        },
                                        {
                                            old_shop: {$ne: null},
                                        },
                                    ],
                                    user_id: item.user_id,
                                },
                                attributes: ['id', 'state', 'user_id', 'expired_date'],
                                transaction,
                            })
                        )
                        console.log('29148 : ShopService -> update -> 2')

                        await this.updateUserRemainingDate(
                            userShopList, // add this new item to the list and apply new list
                            item.user_id,
                            transaction
                        )
                    }
                    ////////////// END
                } else {
                    throw errorService.database.queryFail(
                        `state not supported: ${item.state} -> ${params.state}`
                    )
                }
            } else if (CHANGE_RECOMMENDATION) {
                await this.exec(item.update(params, {transaction}))
                const user_id = params && params.user_id ? params.user_id : item.user_id
                if (user_id) {
                    // increase current_recommendation_post when admin add post to recommendation:
                    await userService.updateCurrentRecommendationPost(
                        user_id,
                        false,
                        transaction
                    )
                }
            } else {
                // flow khác (update default):
                await this.exec(item.update(params, {transaction}))

                if (!isNormalUser && item.user_id) {
                    await notificationService.sendFCMandCreateNoti(
                        item.user_id,
                        FCM_ACTIONS_MESSAGE.YOUR_POST_HAVE_BEEN_EDITED,
                        FCM_ACTIONS.YOUR_POST_HAVE_BEEN_EDITED,
                        item.id,
                        {
                            params_1: item.title,
                        }
                    )
                }
            }
            if (videos) {
                await Video.destroy({
                    where: { shop_id: item.id },
                    transaction,
                })
                const newVideos = videos.map((video: any) => ({
                    shop_id: item.id,
                    url: video.url,
                    duration: video.duration,
                    thumb: video.thumb
                }))
                await Video.bulkCreate(newVideos, { transaction })
            }
            if (tag_ids) {
                await this.exec(
                    ShopTag.destroy({
                        where: {
                            shop_id: item.id,
                        },
                        transaction: transaction,
                    })
                )

                for (let i = 0; i < tag_ids.length; i++) {
                    const bodyST: any = {
                        shop_id: item.id,
                        tag_id: tag_ids[i],
                    }
                    await this.exec(
                        ShopTag.create(bodyST, {
                            transaction,
                        })
                    )
                }
            }
            if (time_ranges) {
                await this.exec(
                    ShopTimeRange.destroy({
                        where: {
                            shop_id: item.id,
                        },
                        transaction: transaction,
                    })
                )
                for (const time of time_ranges) {
                    await this.exec(ShopTimeRange.create(
                        {
                            shop_id: item.id,
                            time,
                        } as any, {transaction}
                    ))
                }
            }

            transaction.commit()
            const dataItem = await this.getItem(option)
            if (params.state === SHOP_STATE.APPROVED) {
                await socketService.emitToUser(dataItem.user_id, SOCKET_EVENTS.APPROVED_SHOP, dataItem)
            }
            if (params.state === SHOP_STATE.REJECTED) {
                await socketService.emitToUser(dataItem.user_id, SOCKET_EVENTS.REJECT_SHOP, dataItem)
            }
            return
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    // async update(params: any, option?: ICrudOption) {
    //   const { tag_ids, account_type, user_role } = params;
    //   delete params.expired_date;

    //   const transaction = await sequelize.transaction();
    //   const isNormalUser = user_role === ROLE.USER;

    //   try {
    //     const item = await this.exec(this.model.findById(option.filter.id), {
    //       allowNull: false,
    //     });
    //     const CHANGE_OWNERSHIP_FLOW =
    //       params.user_id && params.user_id !== item.user_id;
    //     const CHANGE_STATE_FLOW = params.state && params.state !== item.state;
    //     console.log("29148 : ShopService -> update -> CHANGE_STATE_FLOW", CHANGE_STATE_FLOW)
    //     const CHANGE_RECOMMENDATION =
    //       params.is_random_20_shop !== item.is_random_20_shop;

    //     if (params.opening_hours && params.opening_hours !== item.opening_hours) {
    //       const regex2 = /\d\d:\d\d~\d\d:\d\d/g;
    //       if (!regex2.test(params.opening_hours)) {
    //         throw errorService.database.queryFail('Wrong format opening_hours');
    //       }
    //     }
    //     if (CHANGE_OWNERSHIP_FLOW) {
    //       console.log("29148 : ShopService -> update -> CHANGE_OWNERSHIP_FLOW", CHANGE_OWNERSHIP_FLOW)
    //       const haveEvent = await this.exec(
    //         Event.findAll({
    //           where: {
    //             shop_id: item.id,
    //           },
    //         })
    //       );
    //       if (haveEvent && haveEvent.length) {
    //         throw errorService.database.queryError(
    //           ERROR.CANNOT_DELETE_SHOP_ALREADY_HAVE_EVENT
    //         );
    //       }
    //       // đổi owner flow:
    //       // B0: kiểm tra xem người nhận có phải biz account không:
    //       const receiverUserInfo = await this.exec(
    //         User.findOne({
    //           where: {
    //             id: params.user_id,
    //           },
    //           transaction,
    //         })
    //       );
    //       if (
    //         receiverUserInfo &&
    //         receiverUserInfo.account_type !== USER_TYPE.BIZ_USER
    //       ) {
    //         throw errorService.database.queryFail(
    //           'Receiver must be biz account to get ownership'
    //         );
    //       }
    //       // B1: kiểm tra xem người nhận đủ điều kiện (post_limit) không:
    //       await this.checkPostLimit(params.user_id, transaction);
    //       // B2 gắn body:

    //       const newShopInfo = { ...item, ...params };

    //       const receiver_id = params.user_id;
    //       const receivee_id = item.user_id;
    //       // B4: gọi api update:
    //       await this.exec(item.update(newShopInfo, { transaction }));
    //       // B3 update sl active, pending, rejected, expire của người bị chuyển và người nhận:
    //       await this.onUpdatePostOwner(
    //         receivee_id,
    //         receiver_id,
    //         newShopInfo,
    //         transaction
    //       );
    //       // B4 cập nhật lại remaining_date của người bị chuyển và người nhận:
    //       const receiverShopList = await this.exec(
    //         Shop.findAll({
    //           where: {
    //             state: SHOP_STATE.APPROVED,
    //             user_id: receiver_id,
    //           },
    //           transaction,
    //         })
    //       );
    //       const receiveeShopList = await this.exec(
    //         Shop.findAll({
    //           where: {
    //             state: SHOP_STATE.APPROVED,
    //             user_id: receivee_id,
    //           },
    //           transaction,
    //         })
    //       );
    //       await this.updateUserRemainingDate(
    //         receiverShopList,
    //         receiver_id,
    //         transaction
    //       );
    //       await this.updateUserRemainingDate(
    //         receiveeShopList,
    //         receivee_id,
    //         transaction
    //       );

    //       // B5: bắn notification cho 2 user:
    //       // B5.1: notification của receiver:
    //       if (receiver_id) {
    //         const receivee_info = await this.exec(
    //           User.findOne({
    //             where: {
    //               id: receivee_id,
    //             },
    //           })
    //         );

    //         const receiver_noti_data = {
    //           params_1: item.title,
    //           params_2:
    //             receivee_info && receivee_info.nickname
    //               ? receivee_info.nickname
    //               : 'Administrator',
    //         };

    //         const message = getFcmActionMessage(
    //           FCM_ACTIONS_MESSAGE.TRANSFER_SHOP_OWNERSHIP,
    //           receiver_noti_data
    //         );

    //         await firebaseService.sendNotification(
    //           receiver_id,
    //           message,
    //           FCM_ACTIONS.TRANSFER_SHOP_OWNERSHIP,
    //           item.id
    //         );
    //         const entity_id = item.id;
    //         const bodyNoti: any = {
    //           user_id: receiver_id,
    //           title: message,
    //           content: message,
    //           data: {
    //             message: message,
    //             params: receiver_noti_data,
    //           },
    //           action: FCM_ACTIONS.TRANSFER_SHOP_OWNERSHIP,
    //           interacting_type: 'TRANSFER_SHOP_OWNERSHIP',
    //           interacting_content_id: entity_id,
    //         };
    //         await this.exec(Notification.create(bodyNoti));
    //       }
    //       // B5.2: notification của receivee:
    //       if (receivee_id) {
    //         const receivee_noti_data = {
    //           params_1: item.title,
    //         };
    //         const message = getFcmActionMessage(
    //           FCM_ACTIONS_MESSAGE.REMOVE_SHOP_OWNERSHIP,
    //           receivee_noti_data
    //         );

    //         await firebaseService.sendNotification(
    //           receivee_id,
    //           message,
    //           FCM_ACTIONS.REMOVE_SHOP_OWNERSHIP,
    //           item.id
    //         );
    //         const entity_id = item.id;
    //         const bodyNoti: any = {
    //           user_id: receivee_id,
    //           title: message,
    //           content: message,
    //           data: {
    //             message: message,
    //             params: receivee_noti_data,
    //           },
    //           action: FCM_ACTIONS.REMOVE_SHOP_OWNERSHIP,
    //           interacting_type: 'REMOVE_SHOP_OWNERSHIP',
    //           interacting_content_id: entity_id,
    //         };
    //         await this.exec(Notification.create(bodyNoti));
    //       }
    //     } else if (CHANGE_STATE_FLOW) {
    //       console.log("29148 : ShopService -> update -> CHANGE_STATE_FLOW", CHANGE_STATE_FLOW)
    //       // đổi state flow
    //       // B1 kiểm tra trường hợp đổi:
    //       const IS_REJECTED_POST =
    //         params.state === SHOP_STATE.REJECTED &&
    //         item.state !== SHOP_STATE.REJECTED; // reject post
    //         console.log("29148 : ShopService -> update -> IS_REJECTED_POST", IS_REJECTED_POST)

    //       const IS_RESUBMIT_POST =
    //         params.state === SHOP_STATE.PENDING &&
    //         item.state !== SHOP_STATE.PENDING; // resubmit lại post
    //       const IS_APPROVED_POST =
    //         params.state === SHOP_STATE.APPROVED &&
    //         item.state !== SHOP_STATE.APPROVED;

    //       const OLD_STATE = item.state;
    //       const NEW_STATE = params.state;

    //       let newShopInfo: any = {};

    //       if (IS_REJECTED_POST || IS_RESUBMIT_POST) {
    //         // B1: kiểm tra post_limit của user có đủ không nếu là dạng resubmit và có state hiện tại không phải là APPROVED
    //         if (
    //           IS_RESUBMIT_POST &&
    //           item.state !== SHOP_STATE.APPROVED &&
    //           item.user_id
    //         ) {
    //           await this.checkPostLimit(item.user_id, transaction);
    //         }
    //         // B2: gửi noti thông báo reject đến user nếu thuộc dạng reject:
    //         if (IS_REJECTED_POST) {
    //           // gửi notification cho user:
    //           if (item.user_id) {
    //             const message = FCM_ACTIONS_MESSAGE.REJECT_SHOP;
    //             firebaseService.sendNotification(
    //               item.user_id,
    //               message,
    //               FCM_ACTIONS.REJECT_SHOP
    //             );

    //             const bodyNoti: any = {
    //               user_id: item.user_id,
    //               title: message,
    //               content: message,
    //               data: { message: message },
    //               action: FCM_ACTIONS.REJECT_SHOP,
    //             };

    //             await this.exec(
    //               Notification.create(bodyNoti, {
    //                 transaction,
    //               })
    //             );
    //           }
    //         }

    //         console.log("29148  ~ file: shopService.ts ~ line 807 ~ ShopService ~ update ~ newShopInfo", newShopInfo);

    //         // B3: gắn body:
    //         newShopInfo = {
    //           ...params,
    //         };

    //         console.log("29148 : ShopService -> update -> newShopInfo", newShopInfo)

    //         // B4: update:
    //         await this.exec(item.update(newShopInfo, { transaction }));
    //         console.log("29148 : ShopService -> update -> item.user_id", item.user_id)

    //         if (item.user_id) {
    //           // B5: cập nhật lại active, pending, rejected, expire của user:
    //           await this.changeUserPostCounter(
    //             NEW_STATE,
    //             OLD_STATE,
    //             item.user_id,
    //             transaction
    //           );
    //           console.log("29148 : ShopService -> update -> item.user_id 11 ", item.user_id)

    //           // B6: cập nhật lại remaining_date của user:
    //           const userShops = await this.exec(
    //             Shop.findAll({
    //               where: {
    //                 state: SHOP_STATE.APPROVED,
    //                 user_id: item.user_id,
    //               },
    //               transaction,
    //             })
    //           );
    //           console.log("29148 : ShopService -> update -> item.user_id 22 ", item.user_id)

    //           await this.updateUserRemainingDate(
    //             userShops, // add this new item to the list and apply new list
    //             item.user_id,
    //             transaction
    //           );
    //           console.log("29148 : ShopService -> update -> item.user_id 33 ", item.user_id)
    //         }
    //       } else if (IS_APPROVED_POST) {
    //         // B0: cập nhật start_date
    //         const newData: any = item.params_update ? {...item.params_update} : {};
    //         const newShopInfo = { ...params, ...newData, old_shop: null, params_update: null, denied_message: null, denied_shop: null };
    //         newShopInfo.start_date = moment().valueOf();
    //         console.log("29148 : ShopService -> update -> newShopInfo", newShopInfo)

    //         // B1: gửi notification cho user:
    //         if (item.user_id) {
    //           const message = FCM_ACTIONS_MESSAGE.APPROVE_SHOP;
    //           firebaseService.sendNotification(
    //             item.user_id,
    //             message,
    //             FCM_ACTIONS.APPROVE_SHOP
    //           );

    //           const bodyNoti: any = {
    //             user_id: item.user_id,
    //             title: message,
    //             content: message,
    //             data: { message: message },
    //             action: FCM_ACTIONS.APPROVE_SHOP,
    //           };

    //           await this.exec(
    //             Notification.create(bodyNoti, {
    //               transaction,
    //             })
    //           );
    //         }

    //         // B2: update thông tin:
    //         await this.exec(item.update(newShopInfo, { transaction }));
    //         console.log("29148 : ShopService -> update -> newShopInfo", newShopInfo)

    //         // B3: update lại post counter và remaining date:
    //         if (item.user_id) {
    //           // change post counter of user:
    //           await this.changeUserPostCounter(
    //             NEW_STATE,
    //             OLD_STATE,
    //             item.user_id,
    //             transaction
    //           );
    //           console.log("29148 : ShopService -> update -> 1");

    //           // cap nhat remaining date:

    //           const userShopList = await this.exec(
    //             Shop.findAll({
    //               where: {
    //                 state: SHOP_STATE.APPROVED,
    //                 user_id: item.user_id,
    //               },
    //               transaction,
    //             })
    //           );
    //           console.log("29148 : ShopService -> update -> 2");

    //           await this.updateUserRemainingDate(
    //             userShopList, // add this new item to the list and apply new list
    //             item.user_id,
    //             transaction
    //           );
    //           console.log("29148 : ShopService -> update -> 3");

    //         }
    //       } else {
    //         throw errorService.database.queryFail(
    //           `state not supported: ${item.state} -> ${params.state}`
    //         );
    //       }
    //     } else if (CHANGE_RECOMMENDATION) {
    //       console.log("29148 : ShopService -> update -> CHANGE_RECOMMENDATION", CHANGE_RECOMMENDATION)
    //       await this.exec(item.update(params, { transaction }));
    //       const user_id =
    //         params && params.user_id ? params.user_id : item.user_id;
    //       if (user_id) {
    //         // increase current_recommendation_post when admin add post to recommendation:
    //         await userService.updateCurrentRecommendationPost(
    //           user_id,
    //           false,
    //           transaction
    //         );
    //       }
    //     } else {
    //       // flow khác (update default):
    //       await this.exec(item.update(params, { transaction }));

    //       if (!isNormalUser && item.user_id) {
    //         await notificationService.sendFCMandCreateNoti(
    //           item.user_id,
    //           FCM_ACTIONS_MESSAGE.YOUR_POST_HAVE_BEEN_EDITED,
    //           FCM_ACTIONS.YOUR_POST_HAVE_BEEN_EDITED,
    //           item.id,
    //           {
    //             params_1: item.title,
    //           }
    //         );
    //       }
    //     }

    //     if (tag_ids) {
    //       await this.exec(
    //         ShopTag.destroy({
    //           where: {
    //             shop_id: item.id,
    //           },
    //           transaction: transaction,
    //         })
    //       );

    //       for (let i = 0; i < tag_ids.length; i++) {
    //         const bodyST: any = {
    //           shop_id: item.id,
    //           tag_id: tag_ids[i],
    //         };
    //         await this.exec(
    //           ShopTag.create(bodyST, {
    //             transaction,
    //           })
    //         );
    //       }
    //     }

    //     transaction.commit();
    //     return await this.getItem(option);
    //   } catch (e) {
    //     transaction.rollback();
    //     throw e;
    //   }
    // }
    async getList(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {search_value, duplicate} = params
        let subQueryTitle
        let subQueryPhone
        let subQueryAddress
        if (duplicate) {
            subQueryTitle = sequelize.literal(`(SELECT title FROM tbl_shop WHERE deleted_at IS NULL GROUP BY title HAVING COUNT(*) > 1)`);
            subQueryPhone = sequelize.literal(`(SELECT contact_phone FROM tbl_shop WHERE deleted_at IS NULL GROUP BY contact_phone HAVING COUNT(*) > 1)`);
            subQueryAddress = sequelize.literal(`(SELECT address FROM tbl_shop WHERE deleted_at IS NULL GROUP BY address HAVING COUNT(*) > 1)`);
        }
        let result: any
        if (!search_value) {
            result = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll(
                    this.applyFindOptions({
                        ...option,
                        filter: {
                            ...option.filter,
                            ...(duplicate ? {
                                [Op.or]:[
                                    {
                                        title: {
                                            [Op.in]: subQueryTitle
                                        }
                                    },
                                    {
                                        contact_phone: {
                                            [Op.in]: subQueryPhone
                                        }
                                    },
                                    {
                                        address: {
                                            [Op.in]: subQueryAddress
                                        }
                                    }
                                ]
                            } : {}),
                        }
                    })
                )
            )
        }
        else {
            const check = await this.exec(
                this.modelWithScope(option.scope).findAll(
                    this.applyFindOptions({
                        include: [
                            {
                                association: 'user', where: {
                                    $or: [
                                        {
                                            username: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            nickname: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            email: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            company_name: {$iLike: '%' + search_value + '%'},
                                        },
                                        // {
                                        //     phone: {$iLike: '%' + search_value + '%'},
                                        // },
                                    ],
                                }
                            },
                        ]
                    })
                )
            )
            const shopOptions: ICrudOption = {
                ...option,
                filter: {
                    ...option.filter,
                    ...(duplicate ? {
                       [Op.or]:[
                            {
                                 title: {
                                      [Op.in]: subQueryTitle
                                 }
                            },
                            {
                                 contact_phone: {
                                      [Op.in]: subQueryPhone
                                 }
                            },
                           {
                                 address: {
                                      [Op.in]: subQueryAddress
                                 }
                           }
                       ]
                    } : {}),
                    $or: [
                        {
                            title: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            contact_phone: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            address: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            id: {
                                [Op.in]: check.map((e: any) => e.id)
                            }
                        },
                    ]
                },
            }
            result = await this.exec(
                this.modelWithScope(shopOptions.scope).findAndCountAll(
                    this.applyFindOptions(shopOptions)
                )
            )
        }
        const countRecom = await this.exec(
            this.model.count({
                where: {
                    is_random_20_shop: true,
                    state: SHOP_STATE.APPROVED,
                },
            })
        )
        result.count_random_20_shop = countRecom
        return result
    }

    async getSearchTotal(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {search_value} = params
        // const shopData = await this.getListShop(params, option);
        // const postData = await postService.getListPost(params, option);

        const shopOptions: ICrudOption = {
            ...option,
            filter: search_value
                ? {
                    title: {$iLike: '%' + search_value + '%'},
                }
                : {},
            include: [
                //
                {association: 'user', include: [], distinct: true},
                {association: 'category', include: [], distinct: true},
                {
                    association: 'tags',
                    include: [{association: 'tag', include: [], distinct: true}],
                    distinct: true,
                },
            ],
        }
        const postOptions: ICrudOption = {
            ...option,
            filter: search_value
                ? {
                    content: {$iLike: '%' + search_value + '%'},
                }
                : {},
            include: [
                //
                {association: 'user', include: [], distinct: true},
                {association: 'category', include: [], distinct: true},
            ],
        }
        const commentOptions: ICrudOption = {
            ...option,
            filter: search_value
                ? {
                    content: {$iLike: '%' + search_value + '%'},
                }
                : {},
            include: [],
            // include: [
            //   //
            //   { association: 'user', include: [], distinct: true },
            // ],
        }
        // if (distance_order) {
        //   option.order = [
        //     [
        //       Sequelize.fn(
        //         'ST_Distance_Sphere',
        //         Sequelize.col('tbl_shop.position'),
        //         Sequelize.fn(
        //           'ST_SetSRID',
        //           Sequelize.fn('ST_MakePoint', longitude, latitude),
        //           4326
        //         )
        //       ),
        //       distance_order === 'ASC' ? 'asc' : 'desc',
        //     ],
        //   ];
        // }

        const shopData = await this.getListShop(params, shopOptions)
        const postData = await postService.getListPost(params, postOptions)
        const commentData = await commentService.getList(params, commentOptions)

        return {
            shops: shopData,
            posts: postData,
            comments: commentData,
        }
    }

    async getSearchTotalV2(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {search_value} = params
        // const shopData = await this.getListShop(params, option);
        // const postData = await postService.getListPost(params, option);
        const nowTimeStamp = new Date().getTime()
        const tagsOptions: ICrudOption = {
            attributes: ['id', 'name'],
            filter: search_value
                ? {
                    name: {$iLike: '%' + search_value + '%'},
                }
                : {},
            limit: 99999,
        }

        const tagsList = await this.exec(
            Tag.findAll(this.applyFindOptions(tagsOptions))
        )
        const tagIds = tagsList.map((e: any) => e.id)
        const shopOptions: ICrudOption = {
            ...option,
            filter: search_value
                ? {
                    $or: [
                        {
                            title: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            contact_phone: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            short_description: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            short_address: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            address: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            address_2: {$iLike: '%' + search_value + '%'},
                        },
                        {
                            description_content: {$iLike: '%' + search_value + '%'},
                        },
                        tagIds.length > 0
                            ? {
                                tag_ids: {$contains: tagIds},
                            }
                            : {},
                    ],
                    expired_date: {$gt: nowTimeStamp},
                }
                : {expired_date: {$gt: nowTimeStamp}},
            include: [
                //
                {association: 'user', include: [], distinct: true},
                {association: 'category', include: [], distinct: true},
                {
                    association: 'tags',
                    include: [{association: 'tag', include: [], distinct: true}],
                    distinct: true,
                },
            ],
        }
        const postOptions: ICrudOption = {
            ...option,
            filter: search_value
                ? {
                    content: {$iLike: '%' + search_value + '%'},
                }
                : {},
            include: [
                //
                {association: 'user', include: [], distinct: true},
                {association: 'category', include: [], distinct: true},
            ],
        }
        const commentOptions: ICrudOption = {
            ...option,
            filter: search_value
                ? {
                    content: {$iLike: '%' + search_value + '%'},
                }
                : {},
            include: [],
        }
        const shopData = await this.getListShop(params, shopOptions)
        const postData = await postService.getListPost(params, postOptions)
        const commentData = await commentService.getList(params, commentOptions)

        return {
            shops: shopData,
            posts: postData,
            comments: commentData,
        }
    }

    async getListShop(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {user_id, latitude, longitude, order_option, distance_order, radius} =
            params
        // const associations = option.include.map(item => item.association)
        // if (associations.includes('mentors')) {
        //     const mentorInclude = option.include.findIndex(item => item.association === 'mentors')
        //     option.include[mentorInclude].where = {
        //         id: {
        //             [Op.not]: null
        //         }
        //     }
        // }
        if (radius && latitude && longitude) {
            option.filter = {
                $and: [
                    option.filter || {},
                    Sequelize.where(
                        Sequelize.fn(
                            'ST_DWithin',
                            Sequelize.col('tbl_shop.position'),
                            Sequelize.fn(
                                'ST_SetSRID',
                                Sequelize.fn('ST_MakePoint', longitude, latitude),
                                4326
                            ),
                            radius * KILOMETER_TO_MILE * MILE_TO_DEGREE
                        ),
                        true
                    ),
                ],
            }
        }
    
        const distance = [
            Sequelize.fn(
                'ST_DistanceSphere',
                Sequelize.col('tbl_shop.position'),
                Sequelize.fn('ST_MakePoint', longitude, latitude)
            ),
            'distance',
        ]

        if (option.attributes) {
            option.attributes = [...option.attributes, distance]
        } else {
            option.attributes = {
                include: [distance],
            } as any
        }

        const options = this.applyFindOptions(option)

        options.distinct = true

        if (order_option && order_option === SHOP_LIST_BOARD_ORDER.MOST_COMMENT) {
            options.order = [['soft_comment_count', 'desc']]
        } else if (
            order_option &&
            order_option === SHOP_LIST_BOARD_ORDER.MOST_FAVORITE
        ) {
            options.order = [['like', 'desc']]
        } else if (distance_order) {
            options.order = [
                [
                    Sequelize.fn(
                        'ST_DistanceSphere',
                        Sequelize.col('tbl_shop.position'),
                        Sequelize.fn('ST_MakePoint', longitude, latitude)
                    ),
                    distance_order === 'ASC' ? 'asc' : 'desc',
                ],
            ]
        }

        const shopData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(options)
        )

        shopData.total_rows = shopData.count
        if (order_option && order_option === SHOP_LIST_BOARD_ORDER.RANDOM) {
            const newList: any = shuffle(shopData.rows)
            shopData.rows = newList
        }

        const likesList = await favouriteService.getUserFavouriteShop(user_id)

        for (let i = 0; i < shopData.rows.length; i++) {
            const shop = shopData.rows[i].dataValues
            shopData.rows[i].dataValues.is_like = !!likesList.find(
                (e: any) => e.shop_id === shop.id
            )
            shopData.rows[i].dataValues.distance =
                shopData.rows[i].dataValues.distance / 1000
            const videos = await this.exec(
                Video.findAll({
                    where: { shop_id: shop.id },
                    order: [['created_at', 'ASC']],
                })
            )
            shopData.rows[i].dataValues.videos = videos
            if (shopData.rows[i].dataValues.mentors && shopData.rows[i].dataValues.mentors.length) {
                const mentors: any[] = shopData.rows[i].dataValues.mentors
                for (const mentor of mentors) {
                    const is_like = await FavouriteMentor.findOne({where: {user_id, mentor_id: mentor.id}})
                    mentor.dataValues.is_like = !!is_like
                }
            }
        }

        shopData.location = await settingService.getLocationSetting()
        return shopData
    }

    async getNearShop(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {
            latitude,
            longitude,
            distance_order,
            initial_shop_id,
            categories,
            tags,
        } = params

        let shopData: any = {}
        if ((categories && categories.length) || (tags && tags.length)) {
            option.filter = {
                $and: [
                    categories && categories.length
                        ? {
                            $or: categories.map((category_id: any) => ({
                                category_id: category_id,
                            })),
                        }
                        : {},
                    tags && tags.length
                        ? {
                            tag_ids: {
                                $contains: tags.map((tag_id: any) => tag_id),
                            },
                        }
                        : {},
                    {
                        $or: [
                            {
                                state: SHOP_STATE.APPROVED,
                            },
                            {
                                old_shop: {$ne: null},
                            },
                        ],
                    },
                ],
            }

            option.limit = undefined

            option.include = option.include.map((e: any) => ({
                ...e,
                attributes: ['id'],
            }))

            const options: any = this.applyFindOptions(option)

            if (distance_order) {
                options.order = [
                    [
                        Sequelize.fn(
                            'ST_DistanceSphere',
                            Sequelize.col('tbl_shop.position'),
                            Sequelize.fn('ST_MakePoint', longitude, latitude)
                        ),
                        distance_order === 'ASC' ? 'asc' : 'desc',
                    ],
                ]
            }
            shopData = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll({
                    ...options,
                    attributes: ['id'],
                })
            )

            const shopIdIndex = shopData.rows.findIndex(
                (e: any, i: any) => e.dataValues.id === initial_shop_id
            )

            if (shopData.rows.length > 0) {
                const boundaryPos = 50
                const startIndex =
                    shopIdIndex > boundaryPos ? shopIdIndex - boundaryPos : 0
                const endIndex =
                    shopIdIndex > boundaryPos ? shopIdIndex + boundaryPos : boundaryPos
                const finalList = [...shopData.rows.slice(startIndex, endIndex + 1)]
                shopData.rows = finalList.map(
                    (e) => e && e.dataValues && e.dataValues.id
                )
            } else {
                shopData.rows = []
            }
        } else {
            shopData.count = 1
            shopData.rows = [initial_shop_id]
        }

        shopData.location = await settingService.getLocationSetting()
        return shopData
    }

    async getNearJumpUpShop(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {categories, tags, initial_shop_id} = params

        option.order = [['jump_order', 'desc']]

        option.limit = undefined

        let shopData: any = {}
        if ((categories && categories.length) || (tags && tags.length)) {
            option.filter = {
                $and: [
                    categories && categories.length
                        ? {
                            $or: categories.map((category_id: any) => ({
                                category_id: category_id,
                            })),
                        }
                        : {},
                    tags && tags.length
                        ? {
                            tag_ids: {
                                $contains: tags.map((tag_id: any) => tag_id),
                            },
                        }
                        : {},
                    {
                        $or: [
                            {
                                $and: [
                                    {
                                        state: SHOP_STATE.APPROVED,
                                    },
                                ],
                            },
                            {
                                $and: [
                                    {
                                        old_shop: {
                                            $ne: null,
                                        },
                                    },
                                ],
                            },
                        ],
                    },
                ],
            }

            option.include = option.include.map((e: any) => ({
                ...e,
                attributes: ['id'],
            }))
            const options: any = this.applyFindOptions(option)

            shopData = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll({
                    ...options,
                    attributes: ['id'],
                })
            )

            const shopIdIndex = shopData.rows.findIndex(
                (e: any, i: any) => e.dataValues.id === initial_shop_id
            )

            if (shopData.rows.length > 0) {
                const boundaryPos = 50
                const startIndex =
                    shopIdIndex > boundaryPos ? shopIdIndex - boundaryPos : 0
                const endIndex =
                    shopIdIndex > boundaryPos ? shopIdIndex + boundaryPos : boundaryPos
                const finalList = [...shopData.rows.slice(startIndex, endIndex + 1)]
                shopData.rows = finalList.map(
                    (e) => e && e.dataValues && e.dataValues.id
                )
            } else {
                shopData.rows = []
            }
        } else {
            shopData.count = 1
            shopData.rows = [initial_shop_id]
        }

        shopData.location = await settingService.getLocationSetting()
        return shopData
    }

    async getNearShopMap(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        try {
            const {latitude, longitude, radius, user_id} = params

            if (radius) {
                option.filter = {
                    $and: [
                        option.filter,
                        Sequelize.where(
                            Sequelize.fn(
                                'ST_DWithin',
                                Sequelize.col('tbl_shop.position'),
                                Sequelize.fn(
                                    'ST_SetSRID',
                                    Sequelize.fn('ST_MakePoint', longitude, latitude),
                                    4326
                                ),
                                radius * KILOMETER_TO_MILE * MILE_TO_DEGREE
                            ),
                            true
                        ),
                    ],
                }

                option.order = [
                    [
                        Sequelize.fn(
                            'ST_Distance',
                            Sequelize.col('tbl_shop.position'),
                            Sequelize.fn(
                                'ST_SetSRID',
                                Sequelize.fn('ST_MakePoint', longitude, latitude),
                                4326
                            )
                        ),
                        'desc',
                    ],
                ]
            }

            const options: any = this.applyFindOptions(option)

            const shopData = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll(options)
            )

            const likesList = await favouriteService.getUserFavouriteShop(user_id)

            for (let i = 0; i < shopData.rows.length; i++) {
                const shop = shopData.rows[i].dataValues
                shopData.rows[i].dataValues.is_like = !!likesList.find(
                    (e: any) => e.shop_id === shop.id
                )

                shopData.rows[i].dataValues.distance = calcDistance(
                    {
                        latitude: latitude,
                        longitude: longitude,
                    },
                    {
                        latitude: shop.latitude,
                        longitude: shop.longitude,
                    }
                )
            }
            shopData.location = await settingService.getLocationSetting()
            return shopData
        } catch (error) {
            throw error
        }
    }

    async getMasterShop(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        const {thema_id, latitude, longitude, distance_order, user_id} = params

        const cates = await this.exec(
            Category.findAll({
                where: {
                    thema_id: thema_id,
                },
            })
        )

        const cateIds = cates.map((e: any) => e.id)
        const options = this.applyFindOptions(option)

        if (distance_order) {
            options.order = [
                [
                    Sequelize.fn(
                        'ST_Distance_Sphere',
                        Sequelize.col('tbl_shop.position'),
                        Sequelize.fn('ST_MakePoint', longitude, latitude)
                    ),
                    distance_order === 'ASC' ? 'asc' : 'desc',
                ],
            ]
        }

        const shopData = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll({
                ...options,
                where: {
                    is_random_20_shop: true,
                    category_id: {$in: cateIds},
                    state: SHOP_STATE.APPROVED,
                },
                limit: 20,
            })
        )

        const likesList = await favouriteService.getUserFavouriteShop(user_id)

        for (let i = 0; i < shopData.rows.length; i++) {
            const shop = shopData.rows[i].dataValues

            shopData.rows[i].dataValues.is_like = !!likesList.find(
                (e: any) => e.shop_id === shop.id
            )

            if (latitude && longitude) {
                shopData.rows[i].dataValues.distance = calcDistance(
                    {
                        latitude: latitude,
                        longitude: longitude,
                    },
                    {
                        latitude: shop.latitude,
                        longitude: shop.longitude,
                    }
                ) // Distance in km
            }
        }

        shopData.location = await settingService.getLocationSetting()
        return shopData
    }

    async likeShop(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // post id
        return await this.onChangePostLikeStatus(LIKE_STATUS.LIKE_POST, user_id, id)
    }

    async unlikeShop(params: any, option?: ICrudOption) {
        const {user_id} = params
        const {id} = option.filter // post_id
        return await this.onChangePostLikeStatus(
            LIKE_STATUS.UNLIKE_POST,
            user_id,
            id
        )
    }

    onChangePostLikeStatus = async (
        status: string,
        user_id: string,
        shop_id: string
    ) => {
        const transaction = await sequelize.transaction()
        try {
            const bodyFS: any = {user_id: user_id, shop_id: shop_id}

            const tempFind = await this.exec(
                Favourite.findOne({
                    where: bodyFS,
                    attributes: ['id'],
                })
            )
            if (status === LIKE_STATUS.LIKE_POST && tempFind) {
                // call api like when the post is already liked
                throw errorService.database.queryFail(
                    'You have already liked this shop profile!'
                )
            } else if (status === LIKE_STATUS.UNLIKE_POST && !tempFind) {
                // call api unlike when the post not like
                throw errorService.database.queryFail(
                    'You have already unliked this shop profile!'
                )
            }

            if (status === LIKE_STATUS.LIKE_POST) {
                bodyFS.type = REVIEW_TYPE.SHOP
                await this.exec(Favourite.create(bodyFS, {transaction}))
            } else {
                await this.exec(
                    Favourite.destroy({
                        where: {
                            id: tempFind.id,
                        },
                        transaction,
                    })
                )
            }

            const shop_profile = await this.exec(this.model.findById(shop_id), {
                allowNull: false,
            })

            const like_count =
                status === LIKE_STATUS.LIKE_POST
                    ? shop_profile.like + 1
                    : shop_profile.like - 1
            shop_profile.like = like_count

            const body: any = {
                like: like_count,
            }

            const convertedBodyData = this.syncSystemData(shop_profile, body)

            delete convertedBodyData.user_id

            await this.exec(
                this.model.update(convertedBodyData, {
                    where: {
                        id: shop_profile.id,
                    },
                    transaction: transaction,
                })
            )

            transaction.commit()
            return {
                shop: shop_profile,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async cloneShop(params: any) {
        const {user_role, shop_id, employee_id} = params
        const transaction = await sequelize.transaction()
        const isNormalUser = user_role === ROLE.USER
        try {
            if (params && params.user_id && params.is_random_20_shop) {
                // increase current_recommendation_post when admin add post to recommendation:
                await userService.updateCurrentRecommendationPost(
                    params.user_id,
                    true,
                    transaction
                )
            }
            const originShop = await this.exec(
                Shop.findOne({
                    where: {
                        id: shop_id,
                    },
                    include: [
                        {
                            model: Course,
                            as: 'courses',
                            include: [
                                {
                                    model: Price,
                                    as: 'prices',
                                },
                            ],
                        },
                        {
                            model: Mentor,
                            as: 'mentors',
                        },
                    ],
                    order: [
                        [{model: Course, as: 'courses'}, 'order', 'ASC'],
                        [{model: Mentor, as: 'mentors'}, 'order', 'ASC'],
                    ],
                })
            )

            const user = await this.exec(
                User.findOne({
                    where: {
                        id: originShop.user_id,
                    },
                    attributes: [
                        'id',
                        'post_limit',
                        'current_active_post',
                        'current_pending_post',
                    ],
                    transaction: transaction,
                })
            )

            // if (user) {
            //   await this.checkPostLimit(user.id, transaction);
            // }

            const shopInfo = originShop.dataValues
            shopInfo.slug = shopInfo.slug + '-' + uuid.v4()

            delete shopInfo.id
            delete shopInfo.created_at
            delete shopInfo.updated_at

            const shop = await this.exec(
                Shop.create(shopInfo, {
                    transaction,
                })
            )

            const defaultObj: any = {
                comment: 0,
                soft_comment_count: 0,
                view: 0,
                view_records: [0, 0, 0],
                view_daily: 0,
                view_weekly: 0,
                view_monthly: 0,
                view_last_3_months: 0,
                like: 0,
                expired_date: shopInfo.expired_date,
                // jump_interval: null,
                // next_jump_time: null,
                // jump_order: 0,
                // jump_count: 0,
            }

            const convertedBodyData = this.syncSystemData(shop, defaultObj)
            delete convertedBodyData.user_id

            await this.exec(
                Shop.update(convertedBodyData, {
                    where: {
                        id: shop.id,
                    },
                    transaction,
                })
            )

            if (shopInfo.tag_ids && shopInfo.tag_ids.length) {
                for (let i = 0; i < shopInfo.tag_ids.length; i++) {
                    const bodyST: any = {
                        shop_id: shop.id,
                        tag_id: shopInfo.tag_ids[i],
                    }
                    await this.exec(
                        ShopTag.create(bodyST, {
                            transaction,
                        })
                    )
                }
            }
            if (user) {
                // const USED_POST_NUMBER =
                //     user.current_active_post + user.current_pending_post

                // if (user.post_limit <= USED_POST_NUMBER) {
                //     throw errorService.database.queryFail(
                //         '상점가능수를 늘린 후에  상점 추가를 진행하시기 바랍니다'
                //     )
                // }

                if (user.post_limit) {
                    await this.exec(
                        User.update(
                            {
                                post_limit: user.post_limit + 1,
                            },
                            {
                                where: {
                                    id: user.id,
                                },
                                transaction,
                            }
                        )
                    );
                }
                await this.changeUserPostCounter(
                    shop.state,
                    undefined, // create new post so no old state:
                    user.id,
                    transaction
                )
            }

            await recordService.increaseCurrentRecordData(
                {
                    new_post_count: true,
                },
                transaction
            )
            if (shop.created_at_unix_timestamp) {
                console.log('#@$% ', shop.created_at_unix_timestamp)
                await this.exec(
                    Shop.update(
                        {
                            created_at_unix_timestamp: shop.created_at_unix_timestamp,
                        },
                        {
                            where: {
                                id: shop.id,
                            },
                        }
                    )
                )
            }

            for (let index = 0; index < shopInfo.courses.length; index++) {
                const course = shopInfo.courses[index].dataValues
                const prices = course.prices
                const courseBody: any = {
                    title: course.title,
                    running_time: course.running_time,
                    description: course.description,
                    recommended: course.recommended,
                    unit: course.unit,
                    shop_id: shop.id,
                    order: index,
                }
                const courseResult = await this.exec(
                    Course.create(courseBody, {transaction})
                )

                for (const price of prices) {
                    const _price = price.dataValues
                    const priceBody: any = {
                        name: _price.name,
                        price: _price.price,
                        discount: _price.discount,
                        course_id: courseResult.dataValues.id,
                    }
                    await this.exec(Price.create(priceBody, {transaction}))
                }
            }

            for (let index = 0; index < shopInfo.mentors.length; index++) {
                const mentor = shopInfo.mentors[index].dataValues
                const mentorBody: any = {
                    name: mentor.name,
                    description: mentor.description,
                    images: mentor.images,
                    recommended: mentor.recommended,
                    thumbnails: mentor.thumbnails,
                    shop_id: shop.id,
                    order: index,
                }
                await this.exec(Mentor.create(mentorBody, {transaction}))
            }

            transaction.commit()
            return {
                shop,
            }
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async createShop(params: any) {
        const {tag_ids, account_type, user_role, user_id, time_ranges, employee_id} = params
        const transaction = await sequelize.transaction()
        const isNormalUser = user_role === ROLE.USER
        try {            
            if (account_type !== USER_TYPE.BIZ_USER && isNormalUser) {
                throw errorService.database.queryFail(
                    'only biz account can create shop.'
                )
            }
            if (params.slug === '') {
                delete params.slug
            }

            if (params.description === '') {
                throw errorService.database.queryFail(
                    'description cannot be empty',
                    DESCRIPTION_CANNOT_BE_EMPTY
                )
            }
            if (params.slug) {
                let slug = generateSlug(params.slug)
                const checkSlug = await this.model.findOne({where: {slug}})
                if (checkSlug) {
                    slug = slug + '-' + uuid.v4()
                }
                params.slug = slug
            }

            // if (params.desciription === '') {
            //   throw errorService.database.queryFail(
            //     'description cannot be empty',
            //     DESCRIPTION_CANNOT_BE_EMPTY
            //   );
            // }
            if (params.images && params.images.length) {
                const filteredListImgs = params.images.filter(
                    (e: any) => !isBase64EncodeStr(e)
                )
                params.images = filteredListImgs
            }
            if (params.thumbnails && params.thumbnails.length) {
                const filteredListImgs = params.thumbnails.filter(
                    (e: any) => !isBase64EncodeStr(e)
                )
                params.thumbnails = filteredListImgs
            }
            // check and remove base64 image from description:
            params.description = removeBase64EncodeStr(params.description)
            // isBase64EncodeStr
            if (params.opening_hours) {
                const regex2 = /\d\d:\d\d~\d\d:\d\d/g
                if (!regex2.test(params.opening_hours)) {
                    throw errorService.database.queryFail('wrong format opening_hours')
                }
            }

            if (params && params.user_id && params.is_random_20_shop) {
                // increase current_recommendation_post when admin add post to recommendation:
                await userService.updateCurrentRecommendationPost(
                    params.user_id,
                    true,
                    transaction
                )
            }

            if (!employee_id) {
                await this.checkPostLimit(user_id, transaction)
            } else {
                await this.exec(
                    User.update(
                        {
                            post_limit: sequelize.literal(`"post_limit" + 1`),
                        },
                        {
                            where: {
                                id: params.user_id,
                            },
                            transaction,
                        }
                    )
                )
            }
            const { videos, ...paramsWithoutVideos } = params;

            const shopInfo: any = {
                ...paramsWithoutVideos,
                verified: false,
                comment: 0,
                view: 0,
                like: 0,
                short_address: '',
                state:
                    isNormalUser || params.user_id
                        ? SHOP_STATE.PENDING
                        : SHOP_STATE.APPROVED,
                created_by_admin: params.created_by_admin,
                user_id: params.user_id,
                city_id: '38fe9b00-bf45-11ea-96eb-5d9aa703c068',
                district_id: 'a5ddabd0-bf6d-11ea-bbc2-677801023d10',
                ward_id: '7155af10-bf6e-11ea-bbc2-677801023d10',
            }

            if (!isNormalUser && !params.start_date && !params.expired_date) {
                shopInfo.start_date = moment()
                    .utc()
                    .utcOffset(TIMEZONE * 60)
                    .valueOf()
                shopInfo.expired_date =
                    moment()
                        .utc()
                        .utcOffset(TIMEZONE * 60)
                        .valueOf() +
                    86400000 * 30 // set new
            }

            if (params.latitude && params.longitude) {
                shopInfo.latitude = params.latitude
                shopInfo.longitude = params.longitude
            } else {
                const geocoder = NodeGeocoder(options)

                const result = await geocoder.geocode(` ${params.address}`)
                if (result && result.length > 0 && result[0]) {
                    shopInfo.latitude = result[0].latitude
                    shopInfo.longitude = result[0].longitude
                } else {
                    throw errorService.database.queryFail(
                        '검색한 상점주소가 확인되지 않습니다. 정확한 주소정보를 입력해주세요'
                    )
                }
            }

            const shop = await this.exec(
                Shop.create(shopInfo, {
                    transaction,
                })
            )

            await this.exec(rewardService.addReward(user_id, "WRITE_POST", transaction));

            if (params.videos && Array.isArray(params.videos)) {

                for (const video of params.videos) {
                    if (video.url && video.duration) {
                        await this.exec(videoService.create({
                            shop_id: shop.id,
                            url: video.url,
                            duration: video.duration,
                            thumb: video.thumb,
                        }, { transaction }));
                        
                    }
                }
            }

            if (tag_ids) {
                for (let i = 0; i < tag_ids.length; i++) {
                    const bodyST: any = {
                        shop_id: shop.id,
                        tag_id: tag_ids[i],
                    }
                    await this.exec(
                        ShopTag.create(bodyST, {
                            transaction,
                        })
                    )
                }
            }
            if (user_id) {
                await this.changeUserPostCounter(
                    SHOP_STATE.PENDING,
                    undefined, // create new post so no old state:
                    user_id,
                    transaction
                )
            }
            if (time_ranges) {
                for (const time of time_ranges) {
                    await this.exec(ShopTimeRange.create(
                        {
                            shop_id: shop.id,
                            time
                        } as any, {transaction}
                    ))
                }
            }
            await recordService.increaseCurrentRecordData(
                {
                    new_post_count: true,
                },
                transaction
            )
            transaction.commit()
            if (shop.created_at_unix_timestamp) {
                await this.exec(
                    Shop.update(
                        {
                            created_at_unix_timestamp: shop.created_at_unix_timestamp,
                        },
                        {
                            where: {
                                id: shop.id,
                            },
                        }
                    )
                )
            }

            const videosResult = await this.exec(videoService.getList({ shop_id: shop.id }));
            
            return {
                shop: {
                    ...shop.toJSON(),
                    videos: videosResult
                }
            }
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async viewShop(params: any, option?: ICrudOption) {
        const id = option.filter.id
        const where: Record<string, any> = {}
        if (uuid.validate(id)) {
            where['id'] = id
        } else {
            where['slug'] = id
        }
        try {
            const shop = await this.exec(
                this.model.findOne({
                    where,
                    include: [
                        {
                            model: User,
                            as: 'user',
                        },
                        {
                            model: Category,
                            as: 'category',
                            include: [
                                {
                                    model: Thema,
                                    as: 'thema',
                                },
                            ],
                        },
                        {
                            model: ShopTag,
                            as: 'tags',
                            include: [
                                {
                                    model: Tag,
                                    as: 'tag',
                                },
                            ],
                        },
                        {
                            model: Course,
                            as: 'courses',
                            include: [
                                {
                                    model: Price,
                                    as: 'prices',
                                },
                            ],
                        },
                        {
                            model: Mentor,
                            as: 'mentors',
                        },
                        {
                            association: 'events',
                        },
                    ],
                    order: [
                        [{model: Course, as: 'courses'}, 'order', 'ASC'],
                        [{model: Mentor, as: 'mentors'}, 'order', 'ASC'],
                    ],
                })
            )
            if (!shop) {
                throw errorService.database.queryFail('Shop not found!')
            }
            // if (shop.state !== SHOP_STATE.APPROVED) {
            //   throw errorService.database.queryFail('Shop is turned off!');
            // }

            // update view count:
            if (!params.user_id || params.user_id !== shop.user_id) {
                shop.view = shop.view + 1
                shop.view_daily = shop.view_daily + 1
                shop.view_weekly = shop.view_weekly + 1
                shop.view_monthly = shop.view_monthly + 1
                const viewNumber3MonthsObj = this.updateTotalViewLast3MonthsFunc(shop)
                await recordService.increaseCurrentRecordData(
                    {page_view_count: true},
                    undefined
                )

                const body: any = {
                    view: shop.view,
                    view_daily: shop.view_daily,
                    view_weekly: shop.view_weekly,
                    view_monthly: shop.view_monthly,
                    view_last_3_months: viewNumber3MonthsObj.view_last_3_months,
                    view_records: viewNumber3MonthsObj.view_records,
                }

                const convertedBodyData = this.syncSystemData(shop, body)
                delete convertedBodyData.user_id

                // update to database:
                await this.exec(
                    Shop.update(convertedBodyData, {
                        where: {
                            id: shop.id,
                        },
                    })
                )
            }

            if (params.user_id) {
                await this.exec(
                    RecentReading.destroy({
                        where: {
                            shop_id: shop.id,
                            user_id: params.user_id,
                        },
                    })
                )
                const bodyRR: any = {
                    user_id: params.user_id,
                    shop_id: shop.id,
                }
                await this.exec(RecentReading.create(bodyRR))
            }
            const like = await Favourite.findOne({
                where: {
                    user_id: params.user_id,
                    shop_id: shop.id
                }
            })
            shop.dataValues.is_like = !!like

            return {
                shop,
            }
        } catch (e) {
            throw e
        }
    }

    async onDeleteOneShop(
        isNormalUser: any,
        item: any,
        transaction: any,
        isHardDelete = false
    ) {
        if (item) {
            await this.exec(
                Review.destroy({
                    where: {
                        shop_id: item.id,
                    },
                    transaction: transaction,
                })
            )
            await this.exec(
                RecentReading.destroy({
                    where: {
                        shop_id: item.id,
                    },
                    transaction: transaction,
                })
            )
            await this.exec(
                Favourite.destroy({
                    where: {
                        shop_id: item.id,
                    },
                    transaction: transaction,
                })
            )
        }

        if (!isHardDelete) {
            const events = await this.exec(
                Event.findAll({
                    where: {
                        shop_id: item.id,
                    },
                    attributes: ['id'],
                    transaction,
                })
            )

            if (events && events.length > 0) {
                throw errorService.database.queryFail(
                    'There is a EVENT linked to Post you want to delete\nPlease delete EVENT first'
                )
            }
        }

        const userShops = await this.exec(
            Shop.findAll({
                where: {
                    user_id: item.user_id,
                    state: SHOP_STATE.APPROVED,
                },
                transaction,
                attributes: ['expired_date'],
            })
        )

        if (parseInt(item.expired_date) && item.user_id) {
            await this.updateUserRemainingDate(userShops, item.user_id, transaction)
        }

        if (!isNormalUser && item.user_id) {
            await notificationService.sendFCMandCreateNoti(
                item.user_id,
                FCM_ACTIONS_MESSAGE.YOUR_POST_HAVE_BEEN_DELETED,
                FCM_ACTIONS.YOUR_POST_HAVE_BEEN_DELETED,
                item.id,
                {
                    params_1: item.title,
                }
            )
        }

        await item.destroy({transaction})

        if (item && item.user_id) {
            await this.changeUserPostCounter(
                SHOP_STATE.REMOVED,
                item.state,
                item.user_id,
                transaction
            )
            const userShopsLeft = await this.exec(
                Shop.findAll({
                    where: {
                        user_id: item.user_id,
                    },
                    attributes: ['state', 'is_random_20_shop'],
                    transaction,
                })
            )

            const userShopsPendingNumber = userShopsLeft.filter(
                (e: any) => e.state === SHOP_STATE.PENDING
            ).length
            const userShopsActiveNumber = userShopsLeft.filter(
                (e: any) => e.state === SHOP_STATE.APPROVED
            ).length
            const userShopsRejectedNumber = userShopsLeft.filter(
                (e: any) => e.state === SHOP_STATE.REJECTED
            ).length
            const userShopsExpiredNumber = userShopsLeft.filter(
                (e: any) => e.state === SHOP_STATE.EXPIRED
            ).length
            const userCurrentRecommendationPost = userShopsLeft.filter(
                (e: any) => e.is_random_20_shop
            ).length

            const updatedUserShopNumberBody: any = {
                current_active_post: userShopsActiveNumber,
                current_pending_post: userShopsPendingNumber,
                current_rejected_post: userShopsRejectedNumber,
                current_expired_post: userShopsExpiredNumber,
                current_recommendation_post: userCurrentRecommendationPost,
            }

            await this.exec(
                User.update(updatedUserShopNumberBody, {
                    where: {
                        id: item.user_id,
                    },
                    transaction,
                })
            )
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const {user_role} = params

        const transaction = await sequelize.transaction()

        const isNormalUser = user_role === ROLE.USER
        try {
            const item = await this.exec(this.getItem(option), {allowNull: false})

            await this.onDeleteOneShop(isNormalUser, item, transaction)

            transaction.commit()

            return {
                item,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async hardDelete(params: any, option?: ICrudOption) {
        const {user_role} = params

        const transaction = await sequelize.transaction()

        const isNormalUser = user_role === ROLE.USER
        try {
            const item = await this.exec(this.getItem(option), {allowNull: false})

            await this.onDeleteOneShop(isNormalUser, item, transaction, true)

            transaction.commit()

            return {
                item,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async doExpiredPost(shop: any, transaction: any = undefined) {
        // id
        // user_id
        // title,
        await this.changeUserPostCounter(
            SHOP_STATE.EXPIRED,
            SHOP_STATE.APPROVED,
            shop.user_id,
            transaction
        )
        const params = {
            params_1: shop.title,
        }
        const message = getFcmActionMessage(FCM_ACTIONS_MESSAGE.EXPIRE_SHOP, params)
        // firebaseService.sendNotification(
        //     shop.user_id,
        //     message,
        //     FCM_ACTIONS.EXPIRE_SHOP,
        //     shop.id
        // )

        const entity_id = shop.id
        const bodyNoti: any = {
            user_id: shop.user_id,
            title: message,
            content: message,
            data: {
                message: message,
                params,
            },
            action: FCM_ACTIONS.EXPIRE_SHOP,
            interacting_type: 'EXPIRE_SHOP',
            interacting_content_id: entity_id,
        }
        const messageBody = `
광고만료 안내

안녕하세요
■업소명:${shop.title}
${shop.user.username}님 광고기간 0일남았습니다.
제휴 연장입금주시고 입금자명 남겨주시면 자동연장됩니다. 
감사합니다. ${process.env.DOMAIN}
        `
        await smsService.sendLongMessage(messageBody, shop.contact_phone)
        console.log(
            '29148 : ShopService -> scheduleExpiredShop -> bodyNoti',
            bodyNoti
        )

        await this.exec(Notification.create(bodyNoti))
    }

    async scheduleExpiredShop() {
        try {
            const timeNow = moment()
                .utcOffset(TIMEZONE * 60)
                .startOf('day')
                .valueOf()
            const shops = await this.exec(
                Shop.findAll({
                    where: {
                        expired_date: {$lt: timeNow},
                        $or: [
                            {
                                old_shop: null,
                                state: SHOP_STATE.APPROVED,
                            },
                            {
                                old_shop: {$ne: null},
                            },
                        ],
                    },
                    include: [
                        {
                            model: User,
                            as: 'user',
                        },
                    ],
                })
            )

            if (shops && shops.length > 0) {
                const defaultShopIds = shops
                    .filter((e: any) => e && !e.old_shop)
                    .map((e: any) => e.id)

                await this.exec(
                    Shop.update(
                        {
                            state: SHOP_STATE.EXPIRED,
                        },
                        {
                            where: {
                                id: {$in: defaultShopIds},
                                old_shop: null,
                            },
                        }
                    )
                )

                const oldShopShopIds = shops.filter((e: any) => e && e.old_shop)
                for (let i = 0; i < oldShopShopIds.length; i++) {
                    const shopInfo = oldShopShopIds[i]
                    if (shopInfo && shopInfo.old_shop) {
                        const oldShopData = shopInfo.old_shop
                        oldShopData.state = SHOP_STATE.EXPIRED
                        await this.exec(
                            Shop.update(
                                {
                                    old_shop: oldShopData,
                                },
                                {
                                    where: {
                                        id: shopInfo.id,
                                    },
                                }
                            )
                        )
                    }
                }

                for (let i = 0; i < shops.length; i++) {
                    if (shops[i].user_id) {
                        await this.doExpiredPost(shops[i])
                    }
                }
            }

            const ONE_DAY = 60 * 60 * 24 * 1000

            const twoDayMore =
                moment()
                    .utcOffset(TIMEZONE * 60)
                    .startOf('day')
                    .valueOf() +
                ONE_DAY * 2

            const shopsNotification = await this.exec(
                Shop.findAll({
                    where: {
                        expired_date: {$and: [{$gt: timeNow, $lt: twoDayMore}]},
                        $or: [
                            {
                                old_shop: null,
                                state: SHOP_STATE.APPROVED,
                            },
                            {
                                old_shop: {$ne: null},
                            },
                        ],
                    },
                    include: [
                        {
                            model: User,
                            as: 'user',
                        },
                    ],
                })
            )

            if (shopsNotification) {
                for (const shop of shopsNotification) {
                    let params = undefined
                    if (shop.expired_date - timeNow > ONE_DAY) {
                        params = {
                            params_1: '2',
                        }
                    } else {
                        params = {
                            params_1: '1',
                        }
                    }
                    const message = getFcmActionMessage(
                        FCM_ACTIONS_MESSAGE.SHOP_EXPIRED_LEFT,
                        params
                    )
                    // firebaseService.sendNotification(
                    //     shop.user_id,
                    //     message,
                    //     FCM_ACTIONS.SHOP_EXPIRED_LEFT,
                    //     shop.id
                    // )

                    const entity_id = shops.id
                    const bodyNoti: any = {
                        user_id: shop.user_id,
                        title: message,
                        content: message,
                        data: {
                            message: message,
                            params,
                        },
                        action: FCM_ACTIONS.SHOP_EXPIRED_LEFT,
                        interacting_type: 'SHOP_EXPIRED_LEFT',
                        interacting_content_id: entity_id,
                    }

                    await this.exec(Notification.create(bodyNoti))
                    const messageBody = `
광고만료 안내

안녕하세요
■업소명:${shop.title}
${shop.user.username}님 광고기간 ${params.params_1}일남았습니다.
제휴 연장입금주시고 입금자명 남겨주시면 자동연장됩니다. 
감사합니다. ${process.env.DOMAIN}
        `
                    await smsService.sendLongMessage(messageBody, shop.contact_phone)
                }
            }


            return {status: shops.length}
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
            return 0
        }
    }

    async scheduleNotiBeforeExpiredShop() {
        try {
            const shops = await this.exec(
                Shop.findAll({
                    where: {
                        expired_date: {
                            $between: [
                                moment()
                                    .utcOffset(TIMEZONE * 60)
                                    .startOf('day')
                                    .valueOf(),
                                moment()
                                    .utcOffset(TIMEZONE * 60)
                                    .endOf('day')
                                    .valueOf(),
                            ],
                        },
                        $or: [
                            {
                                old_shop: null,
                                state: SHOP_STATE.APPROVED,
                            },
                            {
                                old_shop: {$ne: null},
                            },
                        ],
                    },
                    attributes: ['id', 'user_id', 'title'],
                })
            )

            if (shops && shops.length) {
                for (let i = 0; i < shops.length; i++) {
                    if (shops[i] && shops[i].user_id) {
                        const params = {
                            params_1: shops[i].title,
                        }
                        const message = getFcmActionMessage(
                            FCM_ACTIONS_MESSAGE.PRE_EXPIRE_SHOP,
                            params
                        )
                        // firebaseService.sendNotification(
                        //     shops[i].user_id,
                        //     message,
                        //     FCM_ACTIONS.PRE_EXPIRE_SHOP,
                        //     shops[i].id
                        // )

                        const entity_id = shops[i].id
                        const bodyNoti: any = {
                            user_id: shops[i].user_id,
                            title: message,
                            content: message,
                            data: {
                                message: message,
                                params,
                            },
                            action: FCM_ACTIONS.PRE_EXPIRE_SHOP,
                            interacting_type: 'PRE_EXPIRE_SHOP',
                            interacting_content_id: entity_id,
                        }

                        await this.exec(Notification.create(bodyNoti))
                    }
                }
            }
            return {status: shops.length}
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
            return 0
        }
    }

    updateTotalViewLast3MonthsFunc(shop: any) {
        // lấy ra và update lại index thứ 0 trong view_records (tháng hiện tại) = số lượng view của tháng này
        const viewLast3MonthsArr = shop.view_records

        viewLast3MonthsArr[0] = shop.view_monthly

        // tính tổng lượng view của 3 tháng:
        const totalViewOf3Months =
            viewLast3MonthsArr[0] + viewLast3MonthsArr[1] + viewLast3MonthsArr[2]
        return {
            view_records: viewLast3MonthsArr,
            view_last_3_months: totalViewOf3Months,
        }
    }

    async scheduleUpdateShopLastThreeMonthsView() {
        try {
            const shops = await this.exec(Shop.findAll({}))
            if (shops && shops.length > 0) {
                for (let i = 0; i < shops.length; i++) {
                    const body: any = this.updateTotalViewLast3MonthsFunc(shops[i])
                    // cập nhật vào shop
                    const convertedBodyData = this.syncSystemData(shops[i], {
                        view_records: body.view_records,
                        view_last_3_months: body.view_last_3_months,
                    })

                    await this.exec(
                        Shop.update(convertedBodyData, {
                            where: {
                                id: shops[i].id,
                            },
                        })
                    )
                }
            }
            return {status: shops.length}
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
            return 0
        }
    }

    // update lại index shop, swap trong view_records
    async scheduleSwapShopLastThreeMonthsView() {
        try {
            const shops = await this.exec(Shop.findAll({}))
            if (shops && shops.length > 0) {
                for (let i = 0; i < shops.length; i++) {
                    const newViewRecords = shops[i].view_records
                    let newViewCount = 0
                    console.log('29148 temp temp ne start:', newViewRecords)

                    for (let i = newViewRecords.length - 1; i > 0; i--) {
                        newViewRecords[i] = newViewRecords[i - 1]
                        newViewCount += newViewRecords[i - 1]
                    }
                    newViewRecords[0] = 0
                    newViewCount += 0

                    const convertedBodyData = this.syncSystemData(shops[i], {
                        view_records: newViewRecords,
                        view_last_3_months: newViewCount,
                    })

                    await this.exec(
                        Shop.update(convertedBodyData, {
                            where: {
                                id: shops[i].id,
                            },
                        })
                    )
                }
            }
            return {status: shops.length}
        } catch (error) {
            console.log('29148 scheduleExpiredShop error: ', error)
            return 0
        }
    }

    async scheduleResetShopView() {
        // run this line last
        try {
            const shouldResetDailyView = true // this schedule is run daily so it will alway reset daily view
            const shouldResetWeeklyView =
                moment(moment().valueOf()).date() ===
                moment(moment().valueOf()).weekday(0).date() // only true if today is first day of week (MONDAY)
            const shouldResetMonthlyView = moment(moment().valueOf()).date() === 1 // only true if today is first day of month (1st of each month)

            const body: any = {}

            if (shouldResetDailyView) {
                body.view_daily = 0
            }
            if (shouldResetWeeklyView) {
                body.view_weekly = 0
            }
            if (shouldResetMonthlyView) {
                // swap months in view_records (remove oldest month view count, set 0 view to latest month view count)
                const result2 = await this.scheduleSwapShopLastThreeMonthsView()
                body.view_monthly = 0
            }

            await this.exec(
                Shop.update(body, {
                    where: {
                        status: true,
                    },
                })
            )
            return {status: true}
        } catch (error) {
            console.log('29148 scheduleResetShopView error: ', error)
        }
    }

    async updateUserRemainingDate(
        userShops: any,
        user_id: any,
        transaction: any = undefined
    ) {
        if (user_id) {
            let minRemaingTime = moment('2100/01/01', 'YYYY/MM/DD').valueOf()
            let found = false
            for (let j = 0; j < userShops.length; j++) {
                if (
                    userShops[j].expired_date &&
                    moment(parseInt(userShops[j].expired_date)).valueOf() < minRemaingTime
                ) {
                    found = true
                    minRemaingTime = moment(parseInt(userShops[j].expired_date)).valueOf()
                    console.log(
                        '29148 : ShopService -> userShops[j].expired_date',
                        userShops[j].expired_date
                    )
                }
            }

            if (!found) {
                minRemaingTime = undefined
            }

            console.log('29148 : ShopService -> minRemaingTime', minRemaingTime)

            await this.exec(
                User.update(
                    {
                        post_expired_date: minRemaingTime,
                    },
                    {
                        where: {
                            id: user_id,
                        },
                        transaction,
                    }
                )
            )
        }
    }

    async scheduleSyncCompulsoryShopData() {
        try {
            const shops = await this.exec(
                Shop.findAll({
                    where: {
                        $or: [
                            {
                                old_shop: {$ne: null},
                            },
                            {
                                denied_shop: {$ne: null},
                            },
                        ],
                    },
                    attributes: ['id', ...SYSTEM_DATA_ATTRIBUTES_ARR],
                })
            )
            ///
            for (let i = 0; i < shops.length; i++) {
                const shopInfo = shops[i]
                const convertedBodyData = this.syncSystemData(shopInfo, null)
                await this.exec(
                    Shop.update(convertedBodyData, {
                        where: {
                            id: shopInfo.id,
                        },
                    })
                )
            }
            return {status: shops.length}
        } catch (error) {
            console.log('29148  ~ scheduleSyncCompulsoryShopData ~ error', error)
            return 0
        }
    }

    async downloadExcel(
        params: any,
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        try {
            const {search_value, duplicate} = params
            let subQueryTitle
            let subQueryPhone
            let subQueryAddress
            if (duplicate) {
                subQueryTitle = sequelize.literal(`(SELECT title FROM tbl_shop WHERE deleted_at IS NULL GROUP BY title HAVING COUNT(*) > 1)`);
                subQueryPhone = sequelize.literal(`(SELECT contact_phone FROM tbl_shop WHERE deleted_at IS NULL GROUP BY contact_phone HAVING COUNT(*) > 1)`);
                subQueryAddress = sequelize.literal(`(SELECT address FROM tbl_shop WHERE deleted_at IS NULL GROUP BY address HAVING COUNT(*) > 1)`);
            }
            let result: any
            if (!search_value) {
                result = await this.exec(
                    this.modelWithScope(option.scope).findAndCountAll(
                        this.applyFindOptions({
                            ...option,
                            filter: {
                                ...option.filter,
                                ...(duplicate ? {
                                    [Op.or]:[
                                        {
                                            title: {
                                                [Op.in]: subQueryTitle
                                            }
                                        },
                                        {
                                            contact_phone: {
                                                [Op.in]: subQueryPhone
                                            }
                                        },
                                        {
                                            address: {
                                                [Op.in]: subQueryAddress
                                            }
                                        }
                                    ]
                                } : {}),
                            }
                        })
                    )
                )
            }
            else {
                const check = await this.exec(
                    this.modelWithScope(option.scope).findAll(
                        this.applyFindOptions({
                            include: [
                                {
                                    association: 'user', where: {
                                        $or: [
                                            {
                                                username: {$iLike: '%' + search_value + '%'},
                                            },
                                            {
                                                nickname: {$iLike: '%' + search_value + '%'},
                                            },
                                            {
                                                email: {$iLike: '%' + search_value + '%'},
                                            },
                                            {
                                                company_name: {$iLike: '%' + search_value + '%'},
                                            },
                                            // {
                                            //     phone: {$iLike: '%' + search_value + '%'},
                                            // },
                                        ],
                                    }
                                },
                            ]
                        })
                    )
                )
                const shopOptions: ICrudOption = {
                    ...option,
                    filter: {
                        ...option.filter,
                        ...(duplicate ? {
                            [Op.or]:[
                                {
                                    title: {
                                        [Op.in]: subQueryTitle
                                    }
                                },
                                {
                                    contact_phone: {
                                        [Op.in]: subQueryPhone
                                    }
                                },
                                {
                                    address: {
                                        [Op.in]: subQueryAddress
                                    }
                                }
                            ]
                        } : {}),
                        $or: [
                            {
                                title: {$iLike: '%' + search_value + '%'},
                            },
                            {
                                contact_phone: {$iLike: '%' + search_value + '%'},
                            },
                            {
                                id: {
                                    [Op.in]: check.map((e: any) => e.id)
                                }
                            },
                        ]
                    },
                }
                result = await this.exec(
                    this.modelWithScope(shopOptions.scope).findAndCountAll(
                        this.applyFindOptions(shopOptions)
                    )
                )
            }
            const s3 = new AWS.S3({
                accessKeyId: ID,
                secretAccessKey: SECRET,
            })
            const header = [
                '아이디',
                '테마',
                '제목',
                '전화번호',
                '주소',
                'URL',
                '광역시/도',
                '시/군/구',
                '지하철 지역',
                '노선도',
                '지하철역',
                '메타설명',
                'keywords',
                '삭제여부'
            ]
            const data = [header]
            for (let i = 0; i < result.rows.length; i++) {
                const shop = result.rows[i]
                const rowData = [
                    shop.id ? shop.id : '',
                    shop.category && shop.category.thema && shop.category.thema.name
                        ? shop.category.thema.name
                        : '',
                    shop.title ? shop.title : '',
                    shop.contact_phone ? shop.contact_phone : '',
                    shop.address ? shop.address : '',
                    shop.slug ? shop.slug : '',
                    shop.shop_province ? shop.shop_province : '',
                    shop.shop_district ? shop.shop_district : '',
                    shop.subway_location ? shop.subway_location : '',
                    shop.subway_line ? shop.subway_line : '',
                    shop.subway_station ? shop.subway_station : '',
                    shop.description_content ? shop.description_content : '',
                    shop.keywords ? shop.keywords : '',
                    'no'
                ]
                data.push(rowData)
            }
            const fileName = 'shop' + moment().valueOf() + '.xlsx'
            const buffer = xlsx.build([{name: 'data', data: data}]) // Returns a buffer
            fs.writeFileSync(FILE_IMAGE_PATH + fileName, buffer)
            const fileContent = fs.readFileSync(FILE_IMAGE_PATH + fileName)

            const awsParams = {
                Bucket: BUCKET_NAME,
                Key: fileName, // File name you want to save as in S3
                Body: fileContent,
                ACL: 'public-read',
            }
            // Uploading files to the bucket
            await s3.upload(awsParams, (err: any, data: any) => {
                if (err) {
                    throw err
                }
            })
            const urls3High = `https://${process.env.IMAGE_URL}/${fileName}`

            fs.unlinkSync(FILE_IMAGE_PATH + fileName)
            return {url: urls3High, file_name: fileName}
        } catch (error) {
            console.log(
                '29148  ~ file: shopService.ts ~ line 4200 ~ ShopService ~ error',
                error
            )
            throw error
        }
    }

    async importExcel(params: any) {
        const data = await xlsx.parse(params.url)
        const dataExcel = data[0].data
        const t = await sequelize.transaction()

        try {
            for (let i = 1; i < dataExcel.length; i++) {
                // console.log("@@@ ", supplier)
                const shopId = dataExcel[i][0]
                const body: any = {
                    // id: dataExcel[i][0], // no use
                    // thema: dataExcel[i][1], // no use
                    title: dataExcel[i][2] ? dataExcel[i][2] : undefined,
                    contact_phone: dataExcel[i][3] ? dataExcel[i][3] + '' : undefined,
                    slug: dataExcel[i][5] ? dataExcel[i][5] + '' : undefined,
                    shop_province: dataExcel[i][6] ? dataExcel[i][6] + '' : undefined,
                    shop_district: dataExcel[i][7] ? dataExcel[i][7] + '' : undefined,
                    description_content: dataExcel[i][11] ? dataExcel[i][11] + '' : undefined,
                    keywords: dataExcel[i][12] ? dataExcel[i][12] + '' : undefined,
                    isDelete: dataExcel[i][13] ? dataExcel[i][13] + '' : undefined,
                }
                if (body.isDelete && (body.isDelete.trim() === 'yes' || body.isDelete.trim() === 'Yes')) {
                    await this.exec(
                        this.model.destroy({
                            where: {
                                id: shopId,
                            },
                            transaction: t,
                        })
                    )
                    continue
                }
                const shop = await this.exec(
                    this.model.findOne({
                        where: {
                            id: shopId,
                        },
                        attributes: [
                            'id',
                            'title',
                            'contact_phone',
                            'state',
                            'old_shop',
                            'denied_shop',
                            'denied_message',
                            'slug',
                            'description_content',
                            'keywords',
                            'shop_province',
                            'shop_district',
                        ],
                    })
                )

                if (shop) {
                    let haveChanged = false

                    if (
                        shop.title !== body.title ||
                        shop.contact_phone !== body.contact_phone ||
                        shop.slug !== body.slug ||
                        shop.description_content !== body.description_content ||
                        shop.keywords !== body.keywords ||
                        shop.shop_province !== body.shop_province ||
                        shop.shop_district !== body.shop_district
                    ) {
                        haveChanged = true
                    } else if (
                        shop.old_shop &&
                        (shop.old_shop.title !== body.title ||
                            shop.old_shop.contact_phone !== body.contact_phone)
                    ) {
                        haveChanged = true
                    } else if (
                        shop.denied_shop &&
                        (shop.denied_shop.title !== body.title ||
                            shop.denied_shop.contact_phone !== body.contact_phone)
                    ) {
                        haveChanged = true
                    }

                    if (haveChanged) {
                        if (body.slug === '') {
                            delete body.slug
                        }
                        if (shop.old_shop) {
                            const newOldShop = {
                                ...shop.old_shop,
                                ...body,
                            }
                            body.old_shop = newOldShop
                        }
                        if (shop.denied_shop) {
                            const newDeniedShop = {
                                ...shop.denied_shop,
                                ...body,
                            }
                            body.denied_shop = newDeniedShop
                        }
                        if (body.slug && body.slug !== shop.slug) {
                            let slug = generateSlug(body.slug)
                            const checkSlug = await this.model.findOne({where: {slug}})
                            if (checkSlug && checkSlug.id !== shop.id) {
                                slug = slug + '-' + uuid.v4()
                            }
                            body.slug = slug
                        }
                        if (body.title && body.contact_phone) {
                            await this.exec(
                                this.model.update(body, {
                                    where: {
                                        id: shopId,
                                    },
                                    transaction: t,
                                })
                            )
                        }
                    }
                }
            }
            fs.unlinkSync(params.url)
            t.commit()
            return true
        } catch (error) {
            console.log('29148  ~ importExcel ~ error', error)
            t.rollback()
            throw error
        }
    }

    async reCountShop(user_id: string, transaction: any) {
        const userShopsLeft = await this.exec(
            Shop.findAll({
                where: {
                    user_id: user_id,
                },
                attributes: ['state', 'is_random_20_shop'],
                transaction,
            })
        )

        const userShopsPendingNumber = userShopsLeft.filter(
            (e: any) => e.state === SHOP_STATE.PENDING
        ).length
        const userShopsActiveNumber = userShopsLeft.filter(
            (e: any) => e.state === SHOP_STATE.APPROVED
        ).length
        const userShopsRejectedNumber = userShopsLeft.filter(
            (e: any) => e.state === SHOP_STATE.REJECTED
        ).length
        const userShopsExpiredNumber = userShopsLeft.filter(
            (e: any) => e.state === SHOP_STATE.EXPIRED
        ).length
        const userCurrentRecommendationPost = userShopsLeft.filter(
            (e: any) => e.is_random_20_shop
        ).length

        const updatedUserShopNumberBody: any = {
            current_active_post: userShopsActiveNumber,
            current_pending_post: userShopsPendingNumber,
            current_rejected_post: userShopsRejectedNumber,
            current_expired_post: userShopsExpiredNumber,
            current_recommendation_post: userCurrentRecommendationPost,
        }

        await this.exec(
            User.update(updatedUserShopNumberBody, {
                where: {
                    id: user_id,
                },
                transaction,
            })
        )
    }

    async countShop(params: any) {
        const {search_value, thema_id } = params
        const subQueryTitle = sequelize.literal(`(SELECT title FROM tbl_shop WHERE deleted_at IS NULL GROUP BY title HAVING COUNT(*) > 1)`);
        const subQueryPhone = sequelize.literal(`(SELECT contact_phone FROM tbl_shop WHERE deleted_at IS NULL GROUP BY contact_phone HAVING COUNT(*) > 1)`);
        const subQueryAddress = sequelize.literal(`(SELECT address FROM tbl_shop WHERE deleted_at IS NULL GROUP BY address HAVING COUNT(*) > 1)`);
        let shopFilter = {}
        if (search_value) {
            const check = await this.exec(
                this.model.findAll(
                    this.applyFindOptions({
                        include: [
                            {
                                association: 'user', where: {
                                    $or: [
                                        {
                                            username: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            nickname: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            email: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            company_name: {$iLike: '%' + search_value + '%'},
                                        },
                                        // {
                                        //     phone: {$iLike: '%' + search_value + '%'},
                                        // },
                                    ],
                                }
                            },
                        ]
                    })
                )
            )
            shopFilter = {
                $or: [
                    {
                        title: {$iLike: '%' + search_value + '%'},
                    },
                    {
                        contact_phone: {$iLike: '%' + search_value + '%'},
                    },
                    {
                        address: {$iLike: '%' + search_value + '%'},
                    },
                    {
                        id: {
                            [Op.in]: check.map((e: any) => e.id)
                        }
                    },
                ]
            }
        }
        const countShopPending = await this.model.count({
            where: {
                state: SHOP_STATE.PENDING,
                ...shopFilter
            },
            include: [
                {
                    association: "events",
                },
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', include: [{association: "prices"}]}
            ],
            distinct: true
        })
        const countShopActive = await this.model.count({
            where: {
                state: {
                    '$notIn': [SHOP_STATE.EXPIRED, SHOP_STATE.REJECTED]
                }, ...shopFilter
            },
            include: [
                {
                    association: "events",
                },
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', include: [{association: "prices"}]}
            ],
            distinct: true
        })
        const countShopReject = await this.model.count({
            where: {
                state: SHOP_STATE.REJECTED,
            },
            include: [
                {
                    association: "events",
                    required: false
                },
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', include: [{association: "prices"}]}
            ],
            distinct: true
        })
        const countShopExpired = await this.model.count({
            where: {
                state: {
                    [Op.in]: [SHOP_STATE.EXPIRED]
                }, ...shopFilter
            },
            include: [
                {
                    association: "events",
                },
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', separate: true, include: [{association: "prices"}]}
            ],
            // distinct: true
        })
        const countShopOnEvent = await this.model.count({
            where: {
                state: {
                    [Op.notIn]: [SHOP_STATE.EXPIRED]
                },
                ...shopFilter
            },
            include: [
                {
                    association: "events",
                    where: {}
                },
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', include: [{association: "prices"}]}
            ],
            distinct: true
        })
        const countShopRecommend = await this.model.count({
            where: {
                is_random_20_shop: true, ...shopFilter
            },
            include: [
                {
                    association: "events",
                    where: {}
                },
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', include: [{association: "prices"}]}
            ],
            distinct: true
        })
        const countShopDuplicate = await this.model.count({
            where: {
                ...shopFilter,
                [Op.or]:[
                    {title: {[Op.in]: subQueryTitle}},
                    {contact_phone: {[Op.in]: subQueryPhone}},
                    {address: {[Op.in]: subQueryAddress}}
                ]
            },
            include: [
                {
                    association: "category",
                    include: [
                        {
                            association: "thema",
                        }
                    ],
                    ...(thema_id ? {where: {thema_id}} : {}),
                },
                {association: 'user'},
                {association: 'courses', include: [{association: "prices"}]}
            ],
            distinct: true
        })
        return {
            countShopPending, countShopActive, countShopReject, countShopExpired, countShopOnEvent, countShopRecommend , countShopDuplicate
        }
    }

    async getItem(
        option: ICrudOption = {
            scope: ['defaultScope'],
        },
        is_get?: boolean
    ) {
        const id = option.filter.id
        const where: Record<string, any> = {}
        if (uuid.validate(id)) {
            where['id'] = id
        } else {
            where['slug'] = id
        }
        option.filter = where
        const shop = await this.exec(
            this.modelWithScope(option.scope).findOne(this.applyFindOptions(option)),
            {allowNull: false}
        )

        if (is_get) {
            await ViewShop.create({shop_id: shop.id} as any)
        }
        const videos = await this.exec(
            Video.findAll({
                where: { shop_id: shop.id },
                order: [['created_at', 'ASC']]
            })
        )

        shop.dataValues.videos = videos
        return shop
    }

    async callShop(
        option: ICrudOption = {
            scope: ['defaultScope'],
        }
    ) {
        const item = await this.exec(this.model.findById(option.filter.id), {
            allowNull: false,
        })
        await this.exec(item.update({call: item.call + 1}))
        await CallShop.create({shop_id: option.filter.id} as any)
        return await this.getItem(option)
    }

    async statisticShop(user_id: string, day: string) {
        const startOfDay = moment(day).utc().add(9, 'hour').startOf('days').subtract(9, 'hour').toDate();
        const endOfDay = moment(day).utc().add(9, 'hour').endOf('days').subtract(9, 'hour').toDate();
        const shops = await this.exec(this.model.findAll({where: {user_id}}), {
            allowNull: false,
        })
        const review = await Review.count({
            where: {
                shop_id: {
                    [Op.in]: shops.map((i: any) => i.id)
                },
                created_at: {
                    [Op.between]: [startOfDay, endOfDay]
                }
            }
        })
        const totalReservation = await Reservation.count({
            where: {
                shop_id: {
                    [Op.in]: shops.map((i: any) => i.id)
                },
                created_at: {
                    [Op.between]: [startOfDay, endOfDay]
                }
            }
        })
        const view = await ViewShop.count({
            where: {
                shop_id: {
                    [Op.in]: shops.map((i: any) => i.id)
                },
                created_at: {
                    [Op.between]: [startOfDay, endOfDay]
                }
            }
        })
        const call = await CallShop.count({
            where: {
                shop_id: {
                    [Op.in]: shops.map((i: any) => i.id)
                },
                created_at: {
                    [Op.between]: [startOfDay, endOfDay]
                }
            }
        })
        return {
            review, totalReservation, view, call
        }
    }


    async destroyShopExpired() {
        await Shop.destroy({
            where: {
                state: SHOP_STATE.EXPIRED
            }
        })
        return true
    }

    async rankShop(
        option: {
            limit?: number,
            page?: number,
            sort?: 'ASC' | 'DESC',
            sortBy?: 'callShop' | 'viewShop' | 'bookShop',
            startDate?: string,
            endDate?: string,
            search_value?: string,
        }
    ) {
        const {limit, page, sort, sortBy, endDate, startDate, search_value} = option
        let startOfDay, endOfDay: string
        if (startDate && endDate) {
            startOfDay = moment(startDate).utc().add(9, 'hour').startOf('days').subtract(9, 'hour').format('YYYY-MM-DD HH:mm:ss');
            endOfDay = moment(endDate).utc().add(9, 'hour').endOf('days').subtract(9, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }
        const offset = (Number(page) - 1) * Number(limit)
        if (!search_value) {
            return this.exec(
                this.modelWithScope(['defaultScope']).findAndCountAll(
                    {
                        attributes: [
                            'id',
                            'title',
                            'contact_phone',
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*)::int FROM tbl_call_shop WHERE tbl_call_shop.shop_id = tbl_shop.id ${startDate && endDate ? `AND tbl_call_shop.created_at > '${startOfDay}' AND tbl_call_shop.created_at < '${endOfDay}')` : ')'}`,
                                ),
                                'callShop',
                            ],
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*)::int FROM tbl_view_shop WHERE tbl_view_shop.shop_id = tbl_shop.id ${startDate && endDate ? `AND tbl_view_shop.created_at > '${startOfDay}' AND tbl_view_shop.created_at < '${endOfDay}')` : ')'}`,
                                ),
                                'viewShop',
                            ],
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*)::int FROM tbl_reservation WHERE tbl_reservation.shop_id = tbl_shop.id ${startDate && endDate ? `AND tbl_reservation.created_at > '${startOfDay}' AND tbl_reservation.created_at < '${endOfDay}')` : ')'}`,
                                ),
                                'bookShop',
                            ],
                        ],
                        limit,
                        offset,
                        order: [[sequelize.literal(`"${sortBy}"`), sort], ['updated_at', 'DESC']] as any,
                        include: {
                            association: 'user',
                            attributes: ['email', 'phone', 'username']
                        },
                    }
                )
            )
        } else {
            const check = await this.exec(
                this.modelWithScope(['defaultScope']).findAll(
                    this.applyFindOptions({
                        include: [
                            {
                                association: 'user', where: {
                                    $or: [
                                        {
                                            username: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            nickname: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            email: {$iLike: '%' + search_value + '%'},
                                        },
                                        {
                                            company_name: {$iLike: '%' + search_value + '%'},
                                        },
                                    ],
                                }
                            },
                        ]
                    })
                )
            )
            return this.exec(
                this.modelWithScope(['defaultScope']).findAndCountAll(
                    {
                        attributes: [
                            'id',
                            'title',
                            'contact_phone',
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*)::int FROM tbl_call_shop WHERE tbl_call_shop.shop_id = tbl_shop.id ${startDate && endDate ? `AND tbl_call_shop.created_at > '${startOfDay}' AND tbl_call_shop.created_at < '${endOfDay}')` : ')'}`,
                                ),
                                'callShop',
                            ],
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*)::int FROM tbl_view_shop WHERE tbl_view_shop.shop_id = tbl_shop.id ${startDate && endDate ? `AND tbl_view_shop.created_at > '${startOfDay}' AND tbl_view_shop.created_at < '${endOfDay}')` : ')'}`,
                                ),
                                'viewShop',
                            ],
                            [
                                sequelize.literal(
                                    `(SELECT COUNT(*)::int FROM tbl_reservation WHERE tbl_reservation.shop_id = tbl_shop.id ${startDate && endDate ? `AND tbl_reservation.created_at > '${startOfDay}' AND tbl_reservation.created_at < '${endOfDay}')` : ')'}`,
                                ),
                                'bookShop',
                            ],
                        ],
                        limit,
                        offset,
                        order: [[sequelize.literal(`"${sortBy}"`), sort], ['updated_at', 'DESC']] as any,
                        include: {
                            association: 'user',
                            attributes: ['email', 'phone', 'username']
                        },
                        where: {
                            $or: [
                                {
                                    title: {$iLike: '%' + search_value + '%'},
                                },
                                {
                                    id: {
                                        [Op.in]: check.map((e: any) => e.id)
                                    }
                                },
                            ]
                        }
                    }
                )
            )
        }

    }

    async updateMultiShop(body: any) {
        const {items, ...data} = body
        await this.exec(
            this.model.update(data, {
                where: {
                    id: {
                        [Op.in]: items
                    }
                }
            })
        )
        return true
    }
    async rejectMultiShop(body: any) {
        const { items, denied_message } = body

        const shops = await this.model.findAll({
            where: {
                id: { [Op.in]: items },
                state: SHOP_STATE.PENDING,
            }
        })

        const transaction = await sequelize.transaction()
        try {
            for (const shop of shops) {
                await shop.update(
                    {
                        state: SHOP_STATE.REJECTED,
                        denied_message: denied_message || null,
                    },
                    { transaction }
                )
            }

            await transaction.commit()
            return { rejected: shops.length }
        } catch (e) {
            await transaction.rollback()
            throw e
        }
    } 
}
