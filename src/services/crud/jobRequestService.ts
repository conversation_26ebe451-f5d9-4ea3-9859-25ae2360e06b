// @ts-ignore
import { config } from "@/config";
import { CrudService, ICrudOption } from "../crudService.pg";
import { JobRequest, Quote, sequelize } from "@/models";
import { Op } from "sequelize";

export class JobRequestService extends CrudService<typeof JobRequest> {
  constructor() {
    super(JobRequest);
  }

  async getListV2(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const { user_id: currentUserId } = params;
    const filter: any = {};

    if (option.filter && option.filter.userId) {
      filter.user_id = option.filter.userId;
      delete option.filter.userId;
    }

    if (typeof option.filter.is_favorite !== "undefined") {
      filter.is_favorite =
        option.filter.is_favorite === "true" ||
        option.filter.is_favorite === true;
    }

    if (option.filter.category) {
      filter.category = option.filter.category;
    }

    if (option.filter.region) {
      filter.region = option.filter.region;
    }

    option.filter = {
      ...option.filter,
      ...filter,
    };

    // NEW: handle quote_user_status for current user
    const quoteUserStatus = option.filter.quote_user_status;
    delete option.filter.quote_user_status;
    // not quote of current join and unconfirmed quote
    if (quoteUserStatus === "not_quoted_unconfirmed" && currentUserId) {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: false,
        },
      ];

      const result = await this.modelWithScope(option.scope).findAndCountAll(
        this.applyFindOptions(option)
      );

      result.rows = result.rows.filter((job: any) => {
        if (!job.quotes || job.quotes.length === 0) return true;
        const hasCurrentUserQuote = job.quotes.some(
          (q: any) => q.user_id === currentUserId
        );
        const hasConfirmedQuote = job.quotes.some(
          (q: any) => q.is_confirmed === true
        );
        return !hasCurrentUserQuote && !hasConfirmedQuote;
      });

      result.count = result.rows.length;
      return this.exec(result);
    } else if (quoteUserStatus === "quoted" && currentUserId) {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: true, // A quote from the current user is required.
          where: {
            user_id: currentUserId,
          },
        },
      ];
    } else if (quoteUserStatus === "quoted_confirmed" && currentUserId) {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: true,
          where: {
            user_id: currentUserId,
            is_confirmed: true,
          },
        },
      ];
    }

    // OLD: handle quote_status
    const quoteStatus = option.filter.quote_status;
    delete option.filter.quote_status;

    if (
      quoteStatus !== "no_quote" &&
      quoteStatus !== "unconfirmed" &&
      (!option.include || option.include.length === 0)
    ) {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
        },
      ];
    }

    if (quoteStatus === "has_quote") {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: true,
        },
      ];
    } else if (quoteStatus === "no_quote") {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: false,
        },
      ];

      const result = await this.modelWithScope(option.scope).findAndCountAll(
        this.applyFindOptions(option)
      );

      result.rows = result.rows.filter(
        (job: any) => !job.quotes || job.quotes.length === 0
      );
      result.count = result.rows.length;

      return this.exec(result);
    } else if (quoteStatus === "confirmed") {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: true,
          where: {
            is_confirmed: true,
          },
        },
      ];
    } else if (quoteStatus === "unconfirmed") {
      option.include = [
        {
          association: "quotes",
          attributes: ["id", "user_id", "is_confirmed"],
          required: false,
        },
      ];

      const result = await this.modelWithScope(option.scope).findAndCountAll(
        this.applyFindOptions(option)
      );

      result.rows = result.rows.filter((job: any) => {
        if (!job.quotes || job.quotes.length === 0) return true;
        return job.quotes.every((q: any) => !q.is_confirmed);
      });

      result.count = result.rows.length;
      return this.exec(result);
    }

    const result = await this.modelWithScope(option.scope).findAndCountAll(
      this.applyFindOptions(option)
    );

    return this.exec(result);
  }

  async getListCountCategory(
    params: any,
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ["defaultScope"],
    }
  ) {
    const result = await this.exec(
      this.modelWithScope(option.scope).findAll({
        attributes: [
          "category",
          [sequelize.fn("COUNT", sequelize.col("id")), "count"],
        ],
        group: ["category"],
        raw: true,
      })
    );

    return result;
  }
  async getItem(
    option: ICrudOption = {
      scope: ["defaultScope"],
    }
  ) {
    const {} = option.filter;
    let id: any;
    let currentUserId: any;
    if (option.filter) {
      id = option.filter.id;
      currentUserId = option.filter.user_id;
    }

    const job = await JobRequest.findOne({
      where: { id: id },
      include: [
        {
          model: Quote,
          as: "quote",
          where: { user_id: currentUserId },
          required: false,
        },
      ],
    });
    return job;
  }
}
