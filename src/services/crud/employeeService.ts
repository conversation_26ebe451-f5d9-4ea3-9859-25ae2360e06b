import { CrudService, ICrudOption } from '../crudService.pg'
import { Employee, User } from '@/models/tables'

import {
    sequelize
} from '@/models'

import {
    tokenService,
    firebaseService,
    errorService, recordService, smsService
} from '@/services'
import moment = require('moment')
import { LOGIN_TYPE, USER_TYPE } from '@/const'
export class EmployeeService extends CrudService<typeof Employee> {
    constructor() {
        super(Employee)
    }
    async delete(option?: ICrudOption) {
        const item = await this.exec(this.getItem(option), { allowNull: false });
        await this.exec(User.destroy({
            where: {
                username: item.username
            }
        }));
        return await this.exec(item.destroy());
    }
    async getPassword(params: any, option?: ICrudOption) {
        const {
            old_password,
            new_password,
            employee_id
        } = params
        const findEmployee = await this.exec(this.model.findOne({
            where: {
                id: employee_id,
                password: old_password
            }
        }))
        if (findEmployee) {
            await this.exec(this.model.update({
                password: new_password,
            }, { where: { id: findEmployee.id } }))
            return findEmployee
        } else {
            throw errorService.database.queryFail("Invalid token!!")
        }
    }
    async update(params: any, option?: ICrudOption) {
        if (params.employee_id !== option.filter.id && params.role !== "SUPERADMIN") {
            throw errorService.auth.permissionDeny()
        }
        const item = await this.exec(this.model.findById(option.filter.id), { allowNull: false })
        if (params.username != item.username && params.username != undefined) {
            throw errorService.database.queryFail("Không được thay đổi Username")
        } else {
            if (params.role !== "SUPERADMIN") {
                params.type = item.type
            }
            const updatedItem = await this.exec(item.update(params))
            await this.exec(User.update({
                password: params.password
            }, {
                where: {
                    username: item.username,
                    account_type: USER_TYPE.ADMIN,
                }
            }))
            updatedItem.password = undefined
            return updatedItem;
        }
    }
    async create(params: any, option?: ICrudOption) {
        if (params.role !== "SUPERADMIN") {
            throw errorService.auth.permissionDeny()
        }
        const username = params.username;
        const phone = params.phone;
        const resultUserName: any = await this.model.count({
            where: {
                username
            }
        });
        const resultPhone: any = await this.model.count({
            where: {
                phone
            }
        });
        const lenghtPhone = phone.length
        const lenghtUserName = username.length
        if (lenghtUserName <= 3) {
            throw errorService.database.queryFail("Account must be 4 characters or more");
        } else if (lenghtPhone > 15 || lenghtPhone < 9) {
            throw errorService.database.queryFail("Invalid phone number");
        } else if (resultUserName == 1) {
            throw errorService.database.queryFail(username + " already exists, please choose another username")
        }
        else if (resultPhone == 1) {
            throw errorService.database.queryFail(phone + " already exists, please choose another phone number")
        } else {
            const createdEmployee = await this.exec(this.model.create(params, this.applyCreateOptions(option)))
            const body: any = {
                username: username,
                password: params.password,
                fullname: params.fullname,
                login_type: LOGIN_TYPE.IN_APP,
                account_type: USER_TYPE.ADMIN,
                email: params.email,
                phone: params.phone
            };
            const createdUser = await this.exec(
                User.create(body, this.applyCreateOptions(option))
            );

            createdUser.isNewUser = true;
            await recordService.increaseCurrentRecordData(
                {
                    new_member_count: true,
                },
                undefined
            );
            await this.exec(
                User.update(
                    {
                        sign_in_time_unix_timestamp: moment().valueOf(),
                    },
                    {
                        where: {
                            id: createdUser.id,
                        },
                    }
                )
            );
            return { createdEmployee, createdUser }
        }
    }
    async checkUsername(params: any, option?: ICrudOption) {
        const result: any = await this.model.count({
            where: { username: params.username }
        });
        if (result >= 1) {
            const duplicate = true
            const resultOfCheckUser = params.username + " already exists, please choose another username"
            return { duplicate, resultOfCheckUser };
        } else {
            const resultOfCheckUser = "Can you " + params.username
            return { message: resultOfCheckUser };
        }
    }
    async checkLogin(params: any, option?: ICrudOption) {
        const result = await this.exec(this.model.findOne({
            where: {
                username: params.username,
                password: params.password
            }
        }))
        if (result && result.status) {
            result.password = undefined;
            return result;
        } else {
            throw errorService.database.queryFail("Please check your account and password")
        }
    }

    async findAdminByPhone(phone: string) {
        return await this.exec(
            this.model.findOne({
                attributes: ['id', 'phone', 'username', 'email'],
                where: {
                    phone,
                },
            }),
            { allowNull: false }
        )
    }

    async sendOtp(phone: string){
        return smsService.sendOtp(phone)
    }
    async verifyOtp(phone: string , otp: string){
        const check = await smsService.verifyOtp(phone,otp)
        if (!check){
            throw errorService.auth.codeError()
        }
        return check
    }

    // async createAccount(params: ICreateAccountEmployee) {
    //     const t = await sequelize.transaction();
    //     let {
    //         email,
    //         password,
    //         fullname,
    //         avatar,
    //         phone
    //     } = params;

    //     try {
    //         let employee = await this.exec(Employee.create(params, {
    //             transaction: t
    //         }));

    //         let user = await firebaseService.createUser({
    //             email,
    //             password
    //         })

    //         t.commit();

    //         return {
    //             employee,
    //             user
    //         }
    //     }
    //     catch (err) {
    //         t.rollback();
    //         if (err.code && err.message) throw errorService.firebase.cannotCreateAccount(err)
    //         throw err;
    //     }

    // }

}