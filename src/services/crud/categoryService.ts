import {CrudService, ICrudOption} from '../crudService.pg';
import {Category, Shop, Event, Post, Link, LinkCategory, User, Thema} from '@/models/tables';
import {config} from '@/config';
import {sequelize} from '@/models';

import {tokenService, firebaseService, errorService} from '@/services';
import {SHOP_STATE} from '@/const';
import {Sequelize} from "@/models/base.pg";
import * as moment from "moment";
import {Op} from "sequelize";

export class CategoryService extends CrudService<typeof Category> {
    constructor() {
        super(Category);
    }

    async getListV2(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
    ) {
        try {
            const categories = await this.exec(
                this.modelWithScope(option.scope).findAndCountAll(
                    this.applyFindOptions(option)
                )
            );

            const newCategories = [];
            for (let i = 0; i < categories.rows.length; i++) {
                const category = categories.rows[i].dataValues;

                const pendingShopsNum = await this.exec(Shop.count({
                    where: {
                        category_id: category.id,
                        state: SHOP_STATE.PENDING
                    }
                }));
                const approvedShopsNum = await this.exec(Shop.count({
                    where: {
                        category_id: category.id,
                        state: SHOP_STATE.APPROVED
                    }
                }));
                const rejectedShopsNum = await this.exec(Shop.count({
                    where: {
                        category_id: category.id,
                        state: SHOP_STATE.REJECTED
                    }
                }));
                const expiredShopsNum = await this.exec(Shop.count({
                    where: {
                        category_id: category.id,
                        state: SHOP_STATE.REJECTED
                    }
                }));
                category.pending_shops = pendingShopsNum;
                category.approved_shops = approvedShopsNum;
                category.rejected_shops = rejectedShopsNum;
                category.expired_shops = expiredShopsNum;
                category.active_shops = pendingShopsNum + approvedShopsNum;
                // pending, active, reject, expire
                newCategories.push(category);
            }
            categories.rows = newCategories;
            return categories;
        } catch (error) {
            console.log("29148  ~ file: categoryService.ts ~ line 111 ~ CategoryService ~ error", error)
            throw error;
        }
    }

    async delete(option?: ICrudOption) {
        const transaction = await sequelize.transaction();
        try {
            const item = await this.exec(this.getItem(option), {allowNull: false});
            const shops = await this.exec(
                Shop.findAll({
                    where: {
                        category_id: item.id,
                    },
                    attributes: ["id"]
                })
            );

            if (shops && shops.length) {
                throw errorService.database.queryFail(
                    'You need to delete all shops belongs to this category first.'
                );
            }


            await this.exec(
                LinkCategory.destroy({
                    where: {
                        category_id: item.id,
                    },
                    transaction: transaction,
                })
            );

            // const links = await this.exec(
            //   Link.findAll({
            //     where: {
            //       category_id: item.id,
            //     },
            //     transaction,
            //   })
            // );

            // if (category_ids) {
            //   await this.exec(
            //     LinkCategory.destroy({
            //       where: {
            //         link_id: item.id,
            //       },
            //       transaction: transaction,
            //     })
            //   );

            //   for (let i = 0; i < category_ids.length; i++) {
            //     const bodyLC: any = {
            //       link_id: item.id,
            //       category_id: category_ids[i],
            //     };
            //     await this.exec(
            //       LinkCategory.create(bodyLC, {
            //         transaction,
            //       })
            //     );
            //   }
            // }

            // if (links && links.length) {
            //   throw errorService.database.queryFail(
            //     'You need to delete all links belongs to this category first.'
            //   );
            // }

            await this.exec(
                Post.destroy({
                    where: {
                        category_id: item.id,
                    },
                    transaction,
                })
            );

            await this.exec(item.destroy());

            transaction.commit();

            return {
                item,
            };
        } catch (error) {
            transaction.rollback();
            throw error;
        }
    }

    async create(params: any, option?: ICrudOption) {
        const lastCategory = await this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        })
        params.index = lastCategory ? (Number(lastCategory.dataValues.index) + 1024) : 1024
        const category =  await this.exec(
            this.model.create(params, this.applyCreateOptions(option))
        )
        const linkOfThema = await Link.findAll({
            where : {
                thema_id: params.thema_id
            }
        })
        const lastLinkCategory = await this.exec(LinkCategory.findOne({
            order: [[
                'index', 'DESC'
            ]]
        }))

        for (let i = 0; i < linkOfThema.length; i++) {
            const bodyLC: any = {
                link_id: linkOfThema[i].id,
                category_id: category.id,
                index: lastLinkCategory ? (Number(lastLinkCategory.dataValues.index) + 1024 * (i + 1)) : 1024 * (i + 1)
            }
            await this.exec(
                LinkCategory.create(bodyLC)
            )
        }
        return category
    }

    async dragDropCategory(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const links = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    links.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
        }
        , employee_id ?: string,
        user_id?: string
    ) {
        // const themeOption: Record<string, any> = {
        //     ...(option.filter && option.filter.thema_id ? {id: option.filter.thema_id} : {})
        // }
        // if (!employee_id) {
        //     if (!user_id) {
        //         themeOption['is_for_adults'] = false
        //     } else {
        //         const _19YearAgo = moment().subtract(19, 'years').toDate()
        //         const user = await User.findOne({
        //             where: {
        //                 id: user_id,
        //                 birthday: {
        //                     [Op.lte]: _19YearAgo
        //                 }
        //             },
        //         })
        //         if (!user) {
        //             themeOption['is_for_adults'] = false
        //         }
        //     }
        // }
        // const thema = await Thema.findOne({
        //     where: themeOption
        // })
        // if (!thema) {
        //     throw errorService.router.requestDataInvalid('Underage users')
        // }
        return await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions(option)
            )
        )
    }
}
