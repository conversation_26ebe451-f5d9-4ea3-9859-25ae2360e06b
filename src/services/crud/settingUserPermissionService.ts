import { CrudService, ICrudOption } from '../crudService.pg';
import { SettingUserPermission } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import { tokenService, firebaseService, errorService } from '@/services';
export class SettingUserPermissionService extends CrudService<
  typeof SettingUserPermission
> {
  constructor() {
    super(SettingUserPermission);
  }
}
