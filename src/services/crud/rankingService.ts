import { CrudService, ICrudOption } from "../crudService.pg";
import { Level, Ranking, User, RankingDetail } from "@/models/tables"; // Thêm RankingDetail
import * as Sequelize from 'sequelize';
import { errorService } from "..";

export class RankingService extends CrudService<typeof Ranking> {
  constructor() {
    super(Ranking);
  }

  async getListV2(params: any, option?: any) {
    const { period, metric } = params;

    if (!period) {
      throw errorService.router.badRequest()
    }

    const where: any = Sequelize.where(
      Sequelize.fn('LOWER', Sequelize.col('period')),
      String(period).toLowerCase()
    );

    let order: any[];

    // Include user info
    const includeUser = {
      model: User,
      as: 'user',
      required: false,
      include: [
        {
          model: Level,
          as: 'exp_level',
          required: false,
        },
      ],
    };

    let includeRankingDetail: any = {
      model: RankingDetail,
      as: 'ranking_details',
      required: false,
    };

    if (metric) {
      order = [
        [
          Sequelize.literal(`COALESCE((
            SELECT score FROM tbl_ranking_detail 
            WHERE tbl_ranking_detail.ranking_id = tbl_ranking.id 
              AND tbl_ranking_detail.action_type = '${metric}'
            LIMIT 1
          ), 0)`),
          'DESC'
        ]
      ];
    } else {
      order = [[Sequelize.col('score'), 'DESC']];
    }

    const queryOptions: any = {
      where,
      include: [includeUser, includeRankingDetail],
      limit: 100,
      offset: 0,
      order,
    };


    const list = await this.model.findAll(queryOptions);
    return list;
  }

    async create(params: any, option?: ICrudOption) {

    if (!this.model || typeof this.model.create !== 'function') {
      throw new Error('Ranking model is not initialized correctly in RankingService');
    }

    const createOptions = this.applyCreateOptions(option) || {};

    try {
      const result = await this.model.create(params, createOptions);
      return result;
    } catch (err) {
      console.error('[RankingService][create] Error:', err);
      throw err;
    }
  }
  
}
