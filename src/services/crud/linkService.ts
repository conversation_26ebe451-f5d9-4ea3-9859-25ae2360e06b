import { CrudService, ICrudOption } from '../crudService.pg'
import { Link, LinkCategory, Thema, User } from '@/models/tables'
import { config } from '@/config'
import { sequelize } from '@/models'
import { errorService } from '@/services'
import { Sequelize } from "@/models/base.pg";
import { BOARD, ROUTE, ROUTE_TO_BOARD } from "@/const";
import { Op } from "sequelize";

const LINK_ERR = 600
const CANNOT_CHANGE_INDEX_ERROR = LINK_ERR + 1

export class LinkService extends CrudService<typeof Link> {
    constructor() {
        super(Link)
    }

    async getList(
        option: ICrudOption = {
            limit: config.database.defaultPageSize,
            offset: 0,
            scope: ['defaultScope'],
            order: [['index', 'ASC']],
        }
        , employee_id ?: string,
        user_id?: string
    ) {
        option.order = [['index', 'ASC']]
        option.where = {
            ...option.where,
            [Op.or]: [
                {
                    thema_id: {
                        [Op.ne]: null
                    }
                },
                {
                    themas: {
                        [Op.ne]: null
                    }
                },
            ]
        }
        const themaOption: Record<any, any> = {}
        // if(!employee_id){
        //    if (!user_id){
        //         themeOption['is_for_adults'] = false
        //    }else {
        //        const user = await User.findOne({
        //            where: {
        //                id: user_id,
        //            },
        //        })
        //         if (!user) {
        //             themeOption['is_for_adults'] = false
        //         }
        //    }
        // }
        // if (user_id){
        //     const user = await User.findById(user_id)
        //     themaOption['view_user_permissions'] =
        //         {
        //             [Op.or]: [
        //                 {
        //                     [Op.eq]: null
        //                 },
        //                 {
        //                     [Op.contains]: ['NON_LOGIN_USER']
        //                 },
        //                 {
        //                     [Op.eq]: []
        //                 },
        //                 {
        //                     [Op.contains]: [user.dataValues.account_type]
        //                 }
        //             ]
        //         }
        //
        // }else {
        //     themaOption['view_user_permissions'] =  {
        //         [Op.or]: [
        //             {
        //                 [Op.eq]: null
        //             },
        //             {
        //                 [Op.contains]: ['NON_LOGIN_USER']
        //             },
        //             {
        //                 [Op.eq]: []
        //             }
        //         ]
        //     }
        // }
        option.order.push(['categories', 'index', 'ASC'])
        const data = await this.exec(
            this.modelWithScope(option.scope).findAndCountAll(
                this.applyFindOptions({
                    ...option,
                    include: [
                        {
                            association: 'thema',
                            required: false,
                            where: employee_id ? {} : themaOption
                        },
                        {
                            association: 'categories',
                            required: false,
                            include: [
                                {
                                    association: 'category',
                                }
                            ]
                        },
                    ]
                })
            )
        )
        data.rows = data.rows.filter((i: any) => i.thema !== null || i.themas !== null)
        for (const row of data.rows) {
            // if (row.dataValues.thema.dataValues.is_for_adults){
            //     if (!employee_id){
            //         if (!user_id){
            //             row.dataValues.need_adults = true
            //         }else {
            //             const _19YearAgo = moment().subtract(19,'years').toDate()
            //             const user = await User.findOne({
            //                 where: {
            //                     id: user_id,
            //                     birthday : {
            //                         [Op.lte] : _19YearAgo
            //                     }
            //                 },
            //             })
            //             row.dataValues.need_adults = !user;
            //         }
            //     }
            //
            // }else {
            //     row.dataValues.need_adults = false
            // }
            if (row.dataValues.themas && row.dataValues.themas.length) {
                row.dataValues.themas = await Promise.all(row.dataValues.themas.map(async (thema: any) => {
                    return await Thema.findOne({
                        where: {
                            id: thema
                        }
                    })
                }))
            }
        }
        return data
    }

    async update(params: any, option?: ICrudOption) {
        const {category_ids, thema_id, route} = params
        if (thema_id && route) {
            const routeOfBoard = ROUTE[route]
            if (!routeOfBoard) {
                throw errorService.database.queryFail('Route not found')
            }
            const listBoardOfRoute: string[] = ROUTE_TO_BOARD[routeOfBoard]
            const checkLink = await this.exec(this.model.findOne({
                where: {
                    thema_id,
                    route: {
                        [Op.in]: listBoardOfRoute
                    }
                }
            }))
            if (checkLink && checkLink.id !== option.filter.id) {
                throw errorService.database.queryFail('Thema already existed')
            }

        }
        if (route && route === BOARD.EVENT_BOARD) {
            const checkEvent = await this.exec(this.model.findOne({
                where: {
                    route: BOARD.EVENT_BOARD
                }
            }))
            if (checkEvent && checkEvent.id !== option.filter.id) {
                throw errorService.database.queryFail('Link event already existed')
            }
        }
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.model.findById(option.filter.id), {
                allowNull: false,
            })
            // if (item.index !== params.index) {
            //   const found = await this.exec(
            //     this.model.findOne({
            //       where: {
            //         index: params.index,
            //       },
            //       attributes: ['id', 'name', 'index'],
            //     })
            //   )
            //   if (found) {
            //     throw errorService.database.queryFail(
            //       `order ${params.index} already existed`,
            //       CANNOT_CHANGE_INDEX_ERROR
            //     )
            //   }
            // }

            await this.exec(item.update(params, {transaction}))
            if (category_ids) {
                await this.exec(
                    LinkCategory.destroy({
                        where: {
                            link_id: item.id,
                        },
                        transaction: transaction,
                    })
                )

                const lastLinkCategory = await this.exec(LinkCategory.findOne({
                    order: [[
                        'index', 'DESC'
                    ]]
                }))
                for (let i = 0; i < category_ids.length; i++) {
                    const bodyLC: any = {
                        link_id: item.id,
                        category_id: category_ids[i],
                        index: lastLinkCategory ? (Number(lastLinkCategory.dataValues.index) + 1024 * (i + 1)) : 1024 * (i + 1)
                    }
                    await this.exec(
                        LinkCategory.create(bodyLC, {
                            transaction,
                        })
                    )
                }
            }

            transaction.commit()
            return await this.getItem(option)
        } catch (e) {
            transaction.rollback()
            throw e
        }
    }

    async create(params: any, option?: ICrudOption) {
        const {category_ids, image, thema_id, route, themas} = params
        const routeOfBoard = ROUTE[route]
        if (!routeOfBoard) {
            throw errorService.database.queryFail('Route not found')
        }
        const listBoardOfRoute: string[] = ROUTE_TO_BOARD[routeOfBoard]
        const checkLink = await this.exec(this.model.findOne({
            where: {
                thema_id,
                route: {
                    [Op.in]: listBoardOfRoute
                }
            }
        }))
        if (checkLink) {
            throw errorService.database.queryFail('Thema already existed')
        }
        if (route === BOARD.EVENT_BOARD) {
            const checkEvent = await this.exec(this.model.findOne({
                where: {
                    route: BOARD.EVENT_BOARD
                }
            }))
            if (checkEvent) {
                throw errorService.database.queryFail('Link event already existed')
            }
            if (!themas || (themas && themas.length === 0)) {
                throw errorService.database.queryFail(
                    'Please attach at least one Thema'
                )
            }
        }
        const lastLink = await this.exec(this.model.findOne({
            order: [[
                'index', 'DESC'
            ]]
        }))
        params.index = lastLink ? (Number(lastLink.dataValues.index) + 1024) : 1024
        const transaction = await sequelize.transaction()
        try {
            // if (!category_ids || (category_ids && category_ids.length === 0)) {
            //     throw errorService.database.queryFail(
            //         'Please attach at least one category'
            //     )
            // }
            // image
            if (!image) {
                throw errorService.database.queryFail('Please attach image')
            }
            const count: any = await this.model.count({})
            if (count >= 20) {
                throw errorService.database.queryFail('Link cannot exceed 20 items')
            }

            const link: any = await this.exec(
                this.model.create(params, {transaction})
            )
            console.log('29148 chay cai nay link id:', link.id)
            if (category_ids) {
                const lastLinkCategory = await this.exec(LinkCategory.findOne({
                    order: [[
                        'index', 'DESC'
                    ]]
                }))
                for (let i = 0; i < category_ids.length; i++) {
                    const bodyLC: any = {
                        link_id: link.id,
                        category_id: category_ids[i],
                        index: lastLinkCategory ? (Number(lastLinkCategory.dataValues.index) + 1024 * (i + 1)) : 1024 * (i + 1)
                    }
                    await this.exec(
                        LinkCategory.create(bodyLC, {
                            transaction,
                        })
                    )
                }
            }
            // await this.exec(
            //     this.model.update(
            //         {
            //             index: sequelize.literal(`"index" + 1`),
            //         },
            //         {
            //             where: {
            //                 index: {$gte: params.index},
            //             },
            //             transaction,
            //         }
            //     )
            // )
            transaction.commit()
            return {
                link,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async delete(params: any, option?: ICrudOption) {
        const transaction = await sequelize.transaction()
        try {
            const item = await this.exec(this.getItem(option), {allowNull: false})

            if (item) {
                await this.exec(
                    LinkCategory.destroy({
                        where: {
                            link_id: item.id,
                        },
                    })
                )
            }

            await this.exec(item.destroy())
            transaction.commit()

            return {
                item,
            }
        } catch (error) {
            transaction.rollback()
            throw error
        }
    }

    async dragDropLink(id: string, params: any) {
        let {prev_index_number, next_index_number} = params
        let currElIndexNumber;
        if (prev_index_number === undefined) {
            currElIndexNumber = next_index_number - 512;
        } else if (next_index_number === undefined) {
            currElIndexNumber = prev_index_number + 512;
        } else {
            currElIndexNumber = Math.floor((prev_index_number + next_index_number) / 2);
        }
        try {
            const update = await this.model.update({index: currElIndexNumber}, {
                where: {
                    id
                }
            })
            if (
                Math.abs(currElIndexNumber - prev_index_number) <= 1 ||
                Math.abs(currElIndexNumber - next_index_number) <= 1
            ) {
                const links = await this.model.findAll({
                    attributes: ['id', [Sequelize.literal('ROW_NUMBER() OVER (ORDER BY index_number)'), 'orderedData']],
                })
                await Promise.all(
                    links.map(async (element) => {
                        await this.model.update({index: element.dataValues.orderedData * 1024}, {
                            where: {
                                id: element.id
                            }
                        })
                    })
                );
            }
            return update
        } catch (e) {
            console.log(e)
        }
    }
}
