import { CrudService, ICrudOption } from '../crudService.pg';
import * as moment from 'moment';
import { Comment, Post, User, Notification, Category, Thema } from '@/models/tables';
import { config } from '@/config';
import { sequelize } from '@/models';

import {
  tokenService,
  firebaseService,
  errorService,
  recordService,
  notificationService,
  userService,
  reviewService,
} from '@/services';
import { BOARD, FCM_ACTIONS, FCM_ACTIONS_MESSAGE, HISTORY, REVIEW_TYPE } from '@/const';
import { param } from 'jquery';
import {isBase64EncodeStr} from "@/config/utils";

const TYPE = {
  INCREASE: 'INCREASE',
  DECREASE: 'DECREASE',
};
export class CommentService extends CrudService<typeof Comment> {
  constructor() {
    super(Comment);
  }

  async update(params: any, option?: ICrudOption) {
    try {
      params.type = REVIEW_TYPE.POST;
      return await reviewService.update(params, option);
    } catch (error) {
      throw error;
    }
  }

  async create(params: any, option?: ICrudOption) {
    try {
      params.type = REVIEW_TYPE.POST;
      params.board_type = BOARD.BULLETIN_BOARD;

      return await reviewService.create(params, option);
    } catch (error) {
      throw error;
    }
  }
  async deleteAll(option?: ICrudOption) {
    try {
      return await reviewService.deleteAll(option)
    } catch (error) {
      throw error;
    }
  }

  async delete(option?: ICrudOption) {
    try {
      return await reviewService.delete(option);
    } catch (error) {
      throw error;
    }
  }

  async getList(
      params: any,
      option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
      }
  ) {
    try {
      option.filter.type = REVIEW_TYPE.POST;
      return await reviewService.getListFilter(params, option);
    } catch (error) {
      throw error;
    }
  }

  async getListAdmin(
      option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
      }
  ) {
    return await reviewService.getListByAdmin(option);
  }

  async getListByAdmin(
      params: any,
      option: ICrudOption = {
        limit: config.database.defaultPageSize,
        offset: 0,
        scope: ['defaultScope'],
      }
  ) {
    const options = this.applyFindOptions(option);
    return this.exec(
        this.modelWithScope(option.scope).findAndCountAll(options)
    );
  }

  async createByAdmin(params: any, option?: ICrudOption) {
    try {
      params.type = REVIEW_TYPE.POST;
      params.board_type = BOARD.BULLETIN_BOARD;

      return await reviewService.createByAdmin(params, option);
    } catch (error) {
      throw error;
    }
  }

  async likeReview(params: any, option?: ICrudOption) {
    return reviewService.likeReview(params,option)
  }
  async unlikeReview(params: any, option?: ICrudOption) {
    return reviewService.unlikeReview(params,option)
  }
  async dislikeReview(params: any, option?: ICrudOption) {
    return reviewService.dislikeReview(params,option)
  }
  async undislikeReview(params: any, option?: ICrudOption) {
    return reviewService.undislikeReview(params,option)
  }
}
