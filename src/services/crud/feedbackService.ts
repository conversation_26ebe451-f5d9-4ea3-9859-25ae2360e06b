import { CrudService, ICrudOption } from '../crudService.pg'
import { sequelize, Feedback } from '@/models'

export class FeedbackService extends CrudService<typeof Feedback> {
  constructor() {
    super(Feedback)
  }

  async getListForShop(shop_id: string) {
    const [feedbacks, count] = await Promise.all([
      this.exec(
        this.model.findAll({
          attributes: [
            [sequelize.fn('count', sequelize.col('feedback_item_id')), 'count'],
          ],
          include: [
            {
              association: 'feedback_item',
              paranoid: true,
            },
            {
              association: 'review',
              attributes: [],
              where: {
                shop_id,
              },
            },
          ],
          group: ['feedback_item_id', 'feedback_item.id'],
          order: [
            [sequelize.fn('count', sequelize.col('feedback_item_id')), 'DESC'],
          ],
        })
      ),
      this.model.count({
        include: [
          {
            association: 'review',
            attributes: [],
            where: {
              shop_id,
            },
          },
        ],
      }),
    ])

    return { feedbacks, count }
  }
}
