import * as swagger<PERSON><PERSON><PERSON> from 'swagger-jsdoc';
import { Options } from 'swagger-jsdoc';
import { config } from './index';

const options: Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Kormsg API Documentation',
      version: '1.0.0',
      description: 'API documentation for Kormsg using Swagger/OpenAPI 3.0',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
        url: 'https://kormsg.com',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production'
          ? `${process.env.DOMAIN ||' https://api.kormsg.com'}/api/v1`
          : `http://${config.server.host}:${config.server.port}/api/v1`,
        description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Authorization header using the Bearer scheme. Example: "Authorization: Bearer {token}"',
        },
        firebaseAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'Firebase',
          description: 'Firebase authentication token',
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Access token is missing or invalid',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Unauthorized'
                  }
                }
              }
            }
          }
        },
        BadRequestError: {
          description: 'Invalid request parameters',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Bad Request'
                  },
                  errors: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        field: {
                          type: 'string'
                        },
                        message: {
                          type: 'string'
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Resource not found'
                  }
                }
              }
            }
          }
        },
        ServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Internal Server Error'
                  }
                }
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Forbidden access',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Forbidden'
                  }
                }
              }
            }
          }
        }
      },
      parameters: {
        PageParam: {
          in: 'query',
          name: 'page',
          schema: {
            type: 'integer',
            default: 1,
            minimum: 1
          },
          description: 'Page number'
        },
        PageSizeParam: {
          in: 'query',
          name: 'pageSize',
          schema: {
            type: 'integer',
            default: 10,
            minimum: 1,
            maximum: 100
          },
          description: 'Number of items per page'
        },
        IdParam: {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
            format: 'uuid'
          },
          description: 'Resource ID'
        },
        PaginationLimit: {
          in: 'query',
          name: 'limit',
          schema: {
            type: 'integer',
            default: 10,
            minimum: 1,
            maximum: 100
          },
          description: 'Maximum number of items to return'
        },
        PaginationOffset: {
          in: 'query',
          name: 'offset',
          schema: {
            type: 'integer',
            default: 0,
            minimum: 0
          },
          description: 'Number of items to skip'
        },
        FilterParam: {
          in: 'query',
          name: 'filter',
          schema: {
            type: 'string'
          },
          description: 'Filter criteria in JSON format. Example: {"status": true, "name": "example"}'
        },
        SortParam: {
          in: 'query',
          name: 'sort',
          schema: {
            type: 'string'
          },
          description: 'Sort criteria in JSON format. Example: ["created_at", "DESC"]'
        },
        SearchParam: {
          in: 'query',
          name: 'search',
          schema: {
            type: 'string'
          },
          description: 'Search text to filter results'
        },
        IncludeParam: {
          in: 'query',
          name: 'include',
          schema: {
            type: 'string'
          },
          description: 'Related entities to include in response. Example: "user,category"'
        },
        LanguageParam: {
          in: 'query',
          name: 'lang',
          schema: {
            type: 'string',
            default: 'ko',
            enum: ['ko', 'en']
          },
          description: 'Language preference for the response'
        }
      }
    },
    security: [{
      bearerAuth: [] as string[],
    }],
    tags: [
      { name: 'Auth', description: 'Authentication and authorization' },
      { name: 'Users', description: 'User operations' },
      { name: 'Categories', description: 'Category operations' },
      { name: 'Posts', description: 'Post operations' },
      { name: 'Comments', description: 'Comment operations' },
      { name: 'Shops', description: 'Shop operations' },
      { name: 'Events', description: 'Event operations' },
      { name: 'Admin', description: 'Admin operations' },
      { name: 'Files', description: 'File upload/download operations' },
      { name: 'Notifications', description: 'Notification operations' },
      { name: 'Reports', description: 'Report operations' },
      { name: 'Statistics', description: 'Statistics operations' },
      { name: 'AnswerQuestions', description: 'Answer Question operations' }
    ],
    externalDocs: {
      description: 'Find out more about Kormsg',
      url: 'https://kormsg.com'
    }
  },
  apis: [
    './src/routers/**/*.ts',
    './src/controllers/**/*.ts',
    './src/models/**/*.ts',
    './src/models/tables/*.ts',
    './src/models/collections/*.ts',
  ],
};

const swaggerSpec = swaggerJSDoc(options);

export default swaggerSpec;