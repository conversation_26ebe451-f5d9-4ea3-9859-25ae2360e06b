const sql = require('./database')

export default {
	server: {
		host: 'localhost',
		protocol: 'http',
		debug: true,
		logging: true,
		name: 'LOCAL NAME',
		port: process.env.PORT_DEV || 8765,
		secret: process.env.SERVER_SECRET,
	},
	database: {
		mongo: process.env.MONGODB_URI,
		sessionSecret: process.env.SESSION_SECRET,
		defaultPageSize: 20,
		sql: sql.development,
	},
	firebase: {
		type: process.env.FIREBASE_TYPE,
		project_id: process.env.FIREBASE_PROJECT_ID,
		private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
		private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
		client_email: process.env.FIREBASE_CLIENT_EMAIL,
		client_id: process.env.FIREBASE_CLIENT_ID,
		auth_uri: process.env.FIREBASE_AUTH_URI,
		token_uri: process.env.FIREBASE_TOKEN_URI,
		auth_provider_x509_cert_url:
			process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
		client_x509_cert_url: process.env.FIREBASE_CLIENT_X509_CERT_URL,
	},
	socket: {
		port: 9879,
	},
	firebaseDbURL: process.env.FIREBASE_DATABASE_URL,
}
