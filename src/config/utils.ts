import * as moment from 'moment'

const rateLimit = require('express-rate-limit');

export const checkExpired = (currentDate: any, endDate: any) => {
    const currentTimeFormated = moment(currentDate)
    const expiredDateFormatted = moment(endDate)

    if (expiredDateFormatted.isAfter(currentTimeFormated)) {
        return false
    }
    return true
}

export const shortenShopListData = (item: any, option: any) => {
    if (
        option &&
        option.include &&
        option.include.find((e: any) => e.as === 'shop')
    ) {
        const arr = item.map((e1: any) => {
            if (e1.shop_id) {
                e1.shop.dataValues && delete e1.shop.dataValues.description
                e1.shop.dataValues &&
                e1.shop.dataValues.old_shop &&
                delete e1.shop.dataValues.old_shop.description
                e1.shop.dataValues &&
                e1.shop.dataValues.denied_shop &&
                delete e1.shop.dataValues.denied_shop.description
                e1.shop.dataValues &&
                e1.shop.dataValues.params_update &&
                delete e1.shop.dataValues.params_update.description
            }
            return e1
        })
        return arr
    }
    return item
}

export const removeBase64EncodeStr = (str: string) => {
    // check and remove base64 image:
    const regex =
        /(data:image\/(?:gif|png|jpeg|bmp|webp)(?:;charset=utf-8)?;base64,(?:[A-Za-z0-9]|[+/])+={0,2})/g
    if (str && regex.test(str)) {
        const finalStr = str.replace(regex, '')
        str = finalStr
    }
    return str
}

export const isBase64EncodeStr = (str: string) => {
    // check and remove base64 image:
    const regex =
        /(data:image\/(?:gif|png|jpeg|bmp|webp)(?:;charset=utf-8)?;base64,(?:[A-Za-z0-9]|[+/])+={0,2})/g
    if (str && regex.test(str)) {
        return true
    }
    return false
}

export function makeRandomCode(length: number) {
    var result = ''
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
    var charactersLength = characters.length
    for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
}

export const generateSlug = (title: string) => {
    return title
        .toLowerCase()
        .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F700}-\u{1F77F}]|[\u{1F780}-\u{1F7FF}]|[\u{1F800}-\u{1F8FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA00}-\u{1FA6F}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F1E6}-\u{1F1FF}]|[\u{2B50}-\u{2B55}]|[\u{1F004}]|[\u{1F0CF}]|[\u{1F18E}]|[\u{3030}]|[\u{00A9}\u{00AE}\u{2122}\u{2139}]|[\u{25A0}-\u{25FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '')
        .replace(/[^a-zA-Z0-9가-힣\-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/^-+|-+$/g, '')
}

export const limitRequest = (time: number, max: number, message ?: string) => {
    return rateLimit({
        windowMs: time,
        max: max,
        handler: (req: any, res: any) => {
            res.status(429).json({
                code: 429,
                message : message || `당신은 ' + ${max} + ' 요청을 ' + ${time / 1000 }+ '초' 동안 보낼 수 있습니다!`
            });
        }
    })
}
