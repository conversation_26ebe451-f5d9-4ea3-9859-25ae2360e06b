import * as fs from "fs";
import * as path from "path";
import { v4 as uuidv4 } from "uuid";
import * as ffmpeg from "fluent-ffmpeg";
import ffmpegPath from "ffmpeg-static";
const AWS = require("aws-sdk");

const BUCKET_NAME = process.env.AWS_BUCKET_NAME || "";
const ACCESS_KEY_ID = process.env.AWS_ID || "";
const SECRET_ACCESS_KEY = process.env.AWS_SECRET_KEY || "";
const IMAGE_URL_DOMAIN = process.env.IMAGE_URL || "";

console.log("Bucket Name:", BUCKET_NAME);

AWS.config.update({
  accessKeyId: ACCESS_KEY_ID,
  secretAccessKey: SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || "ap-southeast-1",
});

ffmpeg.setFfmpegPath(ffmpegPath!);

const s3 = new AWS.S3();

export const uploadToS3 = async (
  file: Express.Multer.File
): Promise<{ url: string; duration?: number; thumb?: string }> => {
  const ext = path.extname(file.originalname);
  const fileName = `${uuidv4()}${ext}`;

  const params = {
    Bucket: BUCKET_NAME,
    Key: fileName,
    Body: file.buffer, // <- Sử dụng buffer từ memory
    ACL: "public-read",
    ContentType: file.mimetype,
  };

  await s3.upload(params).promise();
  const url = `https://${IMAGE_URL_DOMAIN}/${fileName}`;

  let duration: number | undefined = undefined;
  let thumb: string | undefined = undefined;

  if (file.mimetype.startsWith("video/")) {
    // Lưu file tạm ra local để ffmpeg xử lý
    const tempPath = path.join(__dirname, `${uuidv4()}${ext}`);
    fs.writeFileSync(tempPath, file.buffer);

    duration = await new Promise<number>((resolve, reject) => {
      ffmpeg.ffprobe(tempPath, (err, metadata) => {
        if (err) return reject(err);
        resolve(metadata.format.duration || 0);
      });
    });

    const thumbPath = path.join(__dirname, `thumb-${uuidv4()}.jpg`);
    await new Promise<void>((resolve, reject) => {
      ffmpeg(tempPath)
        .on("end", () => resolve())
        .on("error", (err: any) => reject(err))
        .screenshots({
          timestamps: ["50%"],
          filename: path.basename(thumbPath),
          folder: path.dirname(thumbPath),
          size: "320x240",
        });
    });

    const thumbBuffer = fs.readFileSync(thumbPath);
    const thumbKey = `thumb-${uuidv4()}.jpg`;
    await s3
      .upload({
        Bucket: BUCKET_NAME,
        Key: thumbKey,
        Body: thumbBuffer,
        ACL: "public-read",
        ContentType: "image/jpeg",
      })
      .promise();

    thumb = `https://${IMAGE_URL_DOMAIN}/${thumbKey}`;

    // Clean up
    fs.unlinkSync(thumbPath);
    fs.unlinkSync(tempPath);
  }

  return { url, duration, thumb };
};
