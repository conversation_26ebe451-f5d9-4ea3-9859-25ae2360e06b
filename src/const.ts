export const EARTH_RADIUS = 6371
export const TESTING_EXPIRATION_FLOW = false

export const TIMEZONE = 9

export const FCM_ACTIONS = {
    SEND_NOTIFICATION: '1',
    INCREASE_POST_LIMIT: '2',
    DECREASE_POST_LIMIT: '3',
    EXPOSE_CATEGORY_TAG: '4',
    HIDE_CATEGORY_TAG: '5',
    COMMENT_POST: '6', // Someone has added a comment to your Post
    REPLY_COMMENT_POST: '7', // Someone has replied to your comment
    COMMENT_SHOP: '8', //
    REPLY_COMMENT_SHOP: '9',
    APPROVE_SHOP: '10',
    REJECT_SHOP: '11',
    PRE_EXPIRE_SHOP: '12',
    PRE_EXPIRE_EVENT: '13',
    EXPIRE_SHOP: '14',
    EXPIRE_EVENT: '15',
    EXTEND_SHOP_EXPIRE_DATE: '16',
    DECREASE_SHOP_EXPIRE_DATE: '17',
    TRANSFER_SHOP_OWNERSHIP: '18',
    REMOVE_SHOP_OWNERSHIP: '19',
    DISABLED_ACCOUNT: '20',
    YOUR_EVENT_HAVE_BEEN_DELETED: '21',
    YOUR_POST_HAVE_BEEN_DELETED: '22',
    YOUR_POST_HAVE_BEEN_EDITED: '23',
    YOUR_EVENT_HAVE_BEEN_EDITED: '24',
    YOUR_ACCOUNT_HAVE_BEEN_UPGRADED: '25',
    YOUR_ACCOUNT_HAVE_BEEN_EDITED: '26',
    YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED: '27',
    YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED: '28',
    YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED: '29',
    YOUR_ACCOUNT_PRE_DOWNGRADED_TO_FREE_USER: '30',
    YOUR_POSTING_WAS_BANNED: '31',
    YOUR_POSTING_WAS_RESTORED: '32',
    YOUR_COMMENT_WAS_BANNED: '33',
    YOUR_COMMENT_WAS_RESTORED: '34',
    SHOP_EXPIRED_LEFT: '35',
    NEW_RESERVATION: '36',
    REPEAT_RESERVATION: '37',
    CANCEL_RESERVATION: '38',
    UPDATE_RESERVATION: '39',
    RESERVATION_NOT_AVAILABLE: '40',
    ACCEPTED_RESERVATION: '41',
    REPEAT_RESERVATION_USER: '42',
    NEW_MESSAGE: '43',
    NEW_MESSAGE_NOTIFICATION: '44',
    NEW_MESSAGE_10_MINUTES: '45',
    NEW_MESSAGE_1_HOUR: '46',
    NEW_MESSAGE_1_DAY: '47',
    COMPLETE_RESERVATION: '48',
    REMIND_RESERVATION: '49',
    FIVE_HUNDRED_VIEWS: '50',
    COMMENT_POST_DONE: '51',
    POST_DONE: '52',
    CONGRATULATION_POST : '53',
    COMMENT_SHOP_DONE : '54',
    CONGRATULATION_COMMENT : '55',
    POINTS_ARE_ADDED: '56',
    BUY_POINT_PRODUCT: '57',
    SENT_PRODUCT: '58',
    POINT_FOR_REVIEW_SHOP: '59',
}

export const FCM_ACTIONS_MESSAGE = {
    INCREASE_POST_LIMIT: '상점등록 제한갯수가 상향되었습니다.',
    DECREASE_POST_LIMIT: '상점등록 제한갯수가 하향되었습니다.',
    EXPOSE_CATEGORY_TAG: '관리자가 상점 카테고리 태그 노출처리 하였습니다.',
    HIDE_CATEGORY_TAG: '관리자가 상점 카테고리 태그 비노출처리 하였습니다.',
    COMMENT_POST: '📬 내 게시글에 댓글이 달렸어요! 어떤 글인지 궁금하시죠? 지금 바로 확인해보세요!',
    COMMENT_POST_DONE: '✏️ 커뮤니티글에 댓글 남기셨군요, 대단해요! 계속해서 멋진 활동을 기대할게요! 🎉👏',
    REPLY_COMMENT_POST: '댓글에 답변이 달렸습니다.',
    COMMENT_SHOP: '💬새 리뷰 알림! 고객님이 남긴 소중한 의견을 확인하세요! 👀',
    REPLY_COMMENT_SHOP: '댓글에 답변이 달렸습니다.',
    APPROVE_SHOP: '광고심사가 승인되었습니다.',
    REJECT_SHOP: '광고심사가 거절되었습니다.',
    PRE_EXPIRE_SHOP: '[params_1] 상점이 1일 후 만료예정입니다.',
    PRE_EXPIRE_EVENT: '[params_1] 이벤트가 내일 만료될 예정입니다.',
    EXPIRE_SHOP: '[params_1] 상점이 만료되었습니다.',
    EXPIRE_EVENT: '[params_1] 이벤트가 만료되었습니다.',
    EXTEND_SHOP_EXPIRE_DATE:
        '[params_1] 상점 등록기간이 [params_2] 까지 연장되었습니다.',
    DECREASE_SHOP_EXPIRE_DATE:
        '[params_1] 상점 등록기간이 [params_2] 까지 축소되었습니다.',
    TRANSFER_SHOP_OWNERSHIP:
        '[params_1]상점의 소유권이 [params_2] 으로부터 회원님에게 이전되었습니다.',
    REMOVE_SHOP_OWNERSHIP: '회원님의 [params_1] 상점소유권이 상실되었습니다.',
    DISABLED_ACCOUNT:
        '회원님의 계정은 삭제되었습니다. 문의사항이 있으시면 관리자에게 연락 부탁드립니다.', // <== send to user if Administrator delete an event of that user.
    YOUR_EVENT_HAVE_BEEN_DELETED:
        '[params_1] 이벤트가 관리자에 의해 삭제되었습니다.', //  <== send to user if Administrator delete an post of that user.
    YOUR_POST_HAVE_BEEN_DELETED:
        '[params_1] 상점이 관리자에 의해 삭제되었습니다.',
    YOUR_POST_HAVE_BEEN_EDITED: '[params_1] 상점이 관리자에 의해 편집되었습니다.',
    YOUR_EVENT_HAVE_BEEN_EDITED:
        '[params_1] 이벤트가 관리자에 의해 편집되었습니다.',
    YOUR_ACCOUNT_HAVE_BEEN_UPGRADED:
        '회원님은 유료회원으로 업그레이드되었습니다.',
    YOUR_ACCOUNT_HAVE_BEEN_EDITED:
        '회원님의 계정정보가 관리자에 의해 편집되었습니다.',
    YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED: '회원님은 무료회원으로 전환되었습니다.',
    YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED:
        '[params_1] 상점 내 회원님의 코멘트는 삭제처리되었습니다',
    YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED:
        '[params_1] > [params_2] 게시판 내 회원님의 코멘트는 삭제처리되었습니다',
    YOUR_ACCOUNT_PRE_DOWNGRADED_TO_FREE_USER: '유료계정이 1일 후 만료예정입니다',
    YOUR_POSTING_WAS_BANNED: '귀하의 게시물이 금지되었습니다',
    YOUR_POSTING_WAS_RESTORED: '귀하의 게시물이 복원되었습니다',
    YOUR_COMMENT_WAS_BANNED: '귀하의 댓글이 금지되었습니다',
    YOUR_COMMENT_WAS_RESTORED: '귀하의 댓글이 복원되었습니다',
    SHOP_EXPIRED_LEFT: '상점 광고기간이 [params_1]일 남았습니다.',
    NEW_RESERVATION: '📅 [params_1]님이 신규 예약을 접수했습니다! 예약 내역을 확인하고 승락해 주세요! 🔍',
    NEW_RESERVATION_FOR_SHOP: '📅 [params_1]매장 예약이 진행 중이에요! 매장에서 확인 중이니 조금만 기다려 주세요! ⏳',
    REPEAT_RESERVATION: '[params_1]님이 예약시간이 15분 남았습니다.',
    CANCEL_RESERVATION: '🚫예약한 [params_1]님에 사정으로 예약이 취소되었습니다.',
    CANCEL_RESERVATION_FOR_USER: '🚫 [params_1]매장 예약이 취소되었습니다. 계획에 변동이 생겼나요? 걱정 마세요, 언제든 다시 예약할 수 있어요! 🔄',
    UPDATE_RESERVATION: '✏️ [params_1]님이 예약 내용이 수정되었습니다! 새로운 일정을 확인하고 준비하세요! 📅',
    RESERVATION_NOT_AVAILABLE: '🚫예약한 [params_1]매장 사정으로 예약이 취소되었습니다. 이번에는 이용이 어렵지만, 다음에 또 만나요! 🙏',
    ACCEPTED_RESERVATION: '🎉 [params_1]매장 예약이 완료되었습니다! 지정된 시간에 맞춰 즐거운 시간을 보내세요! 🌟',
    COMPLETE_RESERVATION: '🌟 [params_1]매장 이용을 완료하셨군요! 경험에 대해 어떠셨나요? 우리 모두에게 도움이 될 수 있는 리뷰를 작성해주세요! ✍️ 여러분의 소중한 의견을 기다리고 있어요! 💬',
    REPEAT_RESERVATION_USER: '예약시간 15분이 남았습니다.',
    NEW_MESSAGE_FOR_USER: '💌 [params_1] 매장에서 새로운 메시지가 도착했어요! 확인해 보아요! 🌟',
    NEW_MESSAGE_FOR_SHOP: '💌 [params_1]님에게서 새로운 메시지가 도착했어요! 지금 바로 확인해보세요! 🌟',
    NEW_MESSAGE_FOR_USER_10_MINUTES: '🕒 [params_1]매장에서 온 메시지가 벌써 10분째 기다리고 있어요! 빨리 확인해보세요! 🌈',
    NEW_MESSAGE_FOR_SHOP_10_MINUTES: '🕒 [params_1]님에게서 온 메시지가 이미 10분째 기다리고 있어요!  지금 바로 확인해보세요! 🌈',
    NEW_MESSAGE_FOR_USER_1_HOUR: '⏰ [params_1]매장에서 보낸 메시지가 벌써 한 시간째 당신을 기다리고 있어요! 어서 확인해보세요! 💌',
    NEW_MESSAGE_FOR_SHOP_1_HOUR: '⏳ [params_1]님에게서 온 메시지가 벌써 한 시간째 당신의 답변을 기다리고 있어요!',
    NEW_MESSAGE_FOR_USER_1_DAY: '📅 [params_1]매장에서 온 메시지가 벌써 하루째 당신의 답장을 기다리고 있어요! 무슨 중요한 소식이 기다리고 있을지 궁금하지 않으세요?',
    NEW_MESSAGE_FOR_SHOP_1_DAY: '🗓️ [params_1]님에게서 온 메시지가 벌써 하루째 당신의 확인을 기다리고 있어요! 놓치면 후회할지도 몰라요! 🌟',
    TEN_MINUTES_BEFORE_BOOKING_TIME_USER: "⏳ [params_1]매장 이용시간 10분 전입니다. 준비됐나요? 🌟",
    THIRTY_MINUTES_BEFORE_BOOKING_TIME_USER: "⏳ [params_1]매장 이용시간 30분 전입니다. 고객님을 맞이할 준비 중입니다! 🙏",
    TEN_MINUTES_BEFORE_BOOKING_TIME_SHOP: "⏳ [params_1]님에 이용시간 10분 전입니다. 준비됐나요? 🌟",
    THIRTY_MINUTES_BEFORE_BOOKING_TIME_SHOP: "⏳ [params_1]님에 이용시간 30분 전입니다.  고객님은 준비 중입니다! 🙏",
    FIVE_HUNDRED_VIEWS : "🎉 내 게시글 조회수가 500회를 넘었습니다! 짱 멋져요!  🌈",
    POST_DONE: "✏️방금 커뮤니티에 글을 쓰셨군요~ 대단해요! 계속해서 멋진 활동 기대할게요! 🌟🚀",
    COMMENT_SHOP_DONE : "📝 [params_1]매장에 작성한 내 리뷰에 새 댓글이 달렸어요! 확인해보세요!",
    CONGRATULATION_POST: "🌟 축하합니다! 내 게시물이 이번 주 베스트 글로 선정되었습니다! 🏆",
    CONGRATULATION_COMMENT: "👏 와우! 당신의 댓글이 커뮤니티 스타가 되었어요! 베스트글로 등극했습니다! 이 화려한 순간을 축하해요🎉✨",
    POINTS_ARE_ADDED: '🎁 축하합니다! [params_1]을 통해 [params_2]포인트를 획득하셨습니다! 포인트로 더 많은 혜택을 즐겨보세요! 🌟',
    BUY_POINT_PRODUCT: '🛍️ [params_1]상품을 포인트로 구매 신청하셨습니다! 신청 확인 후 상품을 전달드릴 예정이니 기대해주세요! 🌟',
    SENT_PRODUCT : '📲 상품 도착 알림! 구매하신 상품 [params_1]이 전송됐습니다. 문자 메시지 확인하시고, 메시지가 안 왔다면 내 정보에서 전화번호를 확인해 주세요! 🎁✨',
    POINT_FOR_REVIEW_SHOP:'🎉 매장 리뷰 작성해주셔서 감사합니다! [params_1]포인트가 지급되었습니다. 축하드려요! 🌟'
} as const

export const getFcmActionMessage = (
    actionMessage: string,
    params: any = undefined
) => {
    let temp: string = actionMessage
    if (params) {
        temp = temp.replace('[params_1]', params.params_1)
        temp = temp.replace('[params_2]', params.params_2)
    }
    return temp
}

export const PAYMENT_METHODS = {
    MEET_AND_CASH: 'MEET_AND_CASH',
    MEET_AND_TRANSFER: 'MEET_AND_TRANSFER',
    MEET_AND_CARD: 'MEET_AND_CARD',
    IN_APP_PAYMENT: 'IN_APP_PAYMENT',
}

export const RESERVATION_STATUS = {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED',
    CANCELLED: 'CANCELLED',
    COMPLETED: 'COMPLETED',
    ALL: 'ALL',
}

export const CONSERVATION = {
    USER: 'USER',
    SHOP: 'SHOP',
    GROUP: 'GROUP',
}

export const LOGIN_TYPE = {
    IN_APP: 'INAPP',
    GOOGLE: 'GOOGLE',
    FACEBOOK: 'FACEBOOK',
    KAKAO: 'KAKAO',
    NAVER: 'NAVER',
    APPLE: 'APPLE',
}

export const USER_TYPE = {
    BIZ_USER: 'BIZ_USER',
    FREE_USER: 'FREE_USER',
    PAID_USER: 'PAID_USER',
    ADMIN: 'ADMIN',
}

export const SHOP_STATE = {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED',
    REMOVED: 'REMOVED',
    EXPIRED: 'EXPIRED',
}

export const ROLE = {
    OPERATOR: 'OPERATOR',
    ADMIN: 'ADMIN',
    SUPERADMIN: 'SUPERADMIN',
    USER: 'USER',
}

export const REVIEW_TYPE = {
    SHOP: 'SHOP',
    RECRUIT: 'RECRUIT',
    POST: 'POST',
}

export const BOARD = {
    DISTANCE_ORDER_BOARD: 'DISTANCE_ORDER_BOARD',
    DISTANCE_ORDER_BOARD_2: 'DISTANCE_ORDER_BOARD_2',
    DISTANCE_ORDER_BOARD_3: 'DISTANCE_ORDER_BOARD_3',
    STORE_PROFILE: 'STORE_PROFILE',
    PROFILE_DESIGN1: 'PROFILE_DESIGN1',
    RECRUIT_BOARD: 'RECRUIT_BOARD',
    RECRUIT_BOARD_2: 'RECRUIT_BOARD_2',
    BULLETIN_BOARD: 'BULLETIN_BOARD',
    JUMP_UP_SHOP_LIST_BOARD: 'JUMP_UP_SHOP_LIST_BOARD',
    EVENT_BOARD: 'EVENT_BOARD',
    SHOP_SALES_BOARD: 'SHOP_SALES_BOARD',
    BLOG: 'BLOG',
    BLOG_2: 'BLOG_2',
    SITE: "SITE",
    REAL_ESTATE: "REAL_ESTATE",
    SECOND_HAND_MARKET: "SECOND_HAND_MARKET",
    BULLETIN_BOARD_2: 'BULLETIN_BOARD_2',
    FIND_TECH_BOARD: "FIND_TECH_BOARD",
}
export const ROUTE = {
    [BOARD.DISTANCE_ORDER_BOARD]: 'SHOP',
    [BOARD.DISTANCE_ORDER_BOARD_2]: 'SHOP',
    [BOARD.DISTANCE_ORDER_BOARD_3]: 'SHOP',
    [BOARD.SHOP_SALES_BOARD]: 'SHOP',
    [BOARD.RECRUIT_BOARD]: 'SHOP',
    [BOARD.RECRUIT_BOARD_2]: 'SHOP',
    [BOARD.STORE_PROFILE]: 'STORE_PROFILE',
    [BOARD.PROFILE_DESIGN1]: 'PROFILE_DESIGN1',
    [BOARD.BULLETIN_BOARD]: 'POST',
    [BOARD.BULLETIN_BOARD_2]: 'POST',
    [BOARD.JUMP_UP_SHOP_LIST_BOARD]: 'SHOP_SECOND',
    [BOARD.EVENT_BOARD]: 'EVENT',
    [BOARD.BLOG]: 'BLOG',
    [BOARD.BLOG_2]: 'BLOG_2',
    [BOARD.SITE]: 'SITE',
    [BOARD.REAL_ESTATE]: 'REAL_ESTATE',
    [BOARD.SECOND_HAND_MARKET]: 'SECOND_HAND_MARKET',
    [BOARD.FIND_TECH_BOARD]: 'FIND_TECH',
}

export const ROUTE_TO_BOARD: Record<string, string[]> = {
    SHOP_SECOND: [BOARD.JUMP_UP_SHOP_LIST_BOARD],
    STORE_PROFILE: [BOARD.STORE_PROFILE],
    PROFILE_DESIGN1: [BOARD.PROFILE_DESIGN1],
    POST: [BOARD.BULLETIN_BOARD, BOARD.BULLETIN_BOARD_2],
    EVENT: [BOARD.EVENT_BOARD],
    BLOG: [BOARD.BLOG],
    BLOG_2: [BOARD.BLOG_2],
    SHOP: [
        BOARD.DISTANCE_ORDER_BOARD,
        BOARD.DISTANCE_ORDER_BOARD_2,
        BOARD.SHOP_SALES_BOARD,
        BOARD.RECRUIT_BOARD,
        BOARD.RECRUIT_BOARD_2,
        BOARD.DISTANCE_ORDER_BOARD_3,
    ],
    SITE: [BOARD.SITE],
    REAL_ESTATE: [BOARD.REAL_ESTATE],
    SECOND_HAND_MARKET: [BOARD.SECOND_HAND_MARKET],
    FIND_TECH: [BOARD.FIND_TECH_BOARD]
}


export const SHOP_LIST_BOARD_ORDER = {
    LATEST: 'LATEST',
    RANDOM: 'RANDOM',
    MOST_COMMENT: 'MOST_COMMENT',
    MOST_FAVORITE: 'MOST_FAVORITE',
}

export const CONTACT_TYPE = {
    IN_APP_ISSUES: 'IN_APP_ISSUES',
    PARTNERSHIP_ISSUES: 'PARTNERSHIP_ISSUES',
}

export const ERROR = {
    EXCEED_EVENT_LIMIT: {
        code: 951,
        message: 'You have reached event limit',
    },
    ONE_EVENT_ONE_SHOP_ONLY: {
        code: 952,
        message: 'One shop can only have one event',
    },
    CANNOT_DELETE_SHOP_ALREADY_HAVE_EVENT: {
        code: 953,
        message: 'Please remove relevant Event before transferring the ownership',
    },
    SHOP_CATEGORY_WAS_DELETED: {
        code: 954,
        message: "Cannot found shop's category, please check it again",
    },
}

export const LOCATION = {
    location_1: 'Seoul',
    location_2: 'Kyeong-gi',
    location_3: 'Incheon',
    location_4: 'Gangwon',
    location_5: 'Chungbuk',
    location_6: 'Chungnam',
    location_7: 'Kyeongnam',
    location_8: 'Kyeongbuk',
    location_9: 'Jeonnam',
    location_10: 'Jeonbuk',
    location_11: 'Jeju',
    location_12: 'Busan',
    location_13: 'Daejeon',
    location_14: 'Daegu',
    location_15: 'Gwangju',
}
export const RECRUIT_TYPE = {
    RECRUIT: 'RECRUIT',
    JOB_SEEKER: 'JOB_SEEKER',
    SHOP_SALES: 'SHOP_SALES',
}

export const HISTORY = {
    SHOP: {
        NAME: 'SHOP',
        TYPES: {
            JUMP_UP: 'JUMP_UP',
            REPORT: {
                NAME: 'REPORT',
                TYPES: {},
            },
        },
    },
    POST: {
        NAME: 'POST',
        TYPES: {
            REPORT: {
                NAME: 'REPORT',
                TYPES: {},
            },
        },
    },
    REVIEW: {
        NAME: 'REVIEW',
        TYPES: {
            REPORT: {
                NAME: 'REPORT',
                TYPES: {},
            },
        },
    },
    USER: {
        NAME: 'USER',
        TYPES: {
            GIFT_ROLL: {
                NAME: 'GIFT_ROLL',
            },
            JUMP_UP: {
                NAME: 'JUMP_UP',
            },
            LEVEL: {
                NAME: 'LEVEL',
                TYPES: {
                    ACTIVITY_1: {
                        NAME: 'activity_1',
                    },
                    ACTIVITY_2: {
                        NAME: 'activity_2',
                    },
                    ACTIVITY_3: {
                        NAME: 'activity_3',
                    },
                    ACTIVITY_4: {
                        NAME: 'activity_4',
                    },
                    ACTIVITY_5: {
                        NAME: 'activity_5',
                    },
                    ACTIVITY_6: {
                        NAME: 'activity_6',
                    },
                    ACTIVITY_7: {
                        NAME: 'activity_7',
                    },
                },
            },
        },
    },
}

export const SYSTEM_DATA_ATTRIBUTES_ARR = [
    'old_shop',
    'denied_shop',
    'comment',
    'soft_comment_count',
    'view',
    'view_records',
    'view_daily',
    'view_weekly',
    'view_monthly',
    'view_last_3_months',
    'like',
    'expired_date',
    'jump_interval',
    'next_jump_time',
    'jump_order',
    'jump_count',
]
export const LEVEL = {
    1: 50,
    2: 150,
    3: 300,
    4: 500,
    5: 1000,
    6: 1300,
    7: 1600,
    8: 1900,
    9: 2300,
    10: 2700,
    11: 3100,
    12: 3600,
    13: 4100,
    14: 4600,
    15: 5200,
    16: 5800,
    17: 8400,
    18: 9400,
    19: 10400,
    20: 11400,
    21: 12600,
    22: 13800,
    23: 15000,
    24: 16400,
    25: 17800,
    26: 23200,
    27: 25200,
    28: 27200,
    29: 29200,
    30: 32200,
    31: 35200,
    32: 38200,
    33: 42200,
    34: 46200,
    35: 60200,
    36: 75200,
    37: 115200,
    38: 195200,
    39: 345200,
    40: 10000000000000,
}

export const VIDEO_FORMAT = {
    HORIZONTAL: 'HORIZONTAL',
    VERTICAL: 'VERTICAL',
}
export const QUESTION_TYPE = {
    CHANGE_DESIGN: 'CHANGE_DESIGN',
    PAYMENT_ADVERTISEMENT: 'PAYMENT_ADVERTISEMENT',
    LIMIT_FUNCTION: 'LIMIT_FUNCTION',
    NORMAL_QUESTION: 'NORMAL_QUESTION',
    ERROR: 'ERROR',
    OTHER: 'OTHER',
}

export const QUESTION_STATUS = {
    COMPLETED: 'COMPLETED',
    PENDING: 'PENDING',
    MORE: 'MORE',
}

export const REVIEW_SUB_TYPE = {
    DEFAULT: 'DEFAULT',
    FEEDBACK: 'FEEDBACK',
}

export const POINT_PRODUCT_TYPE = {
    CAFE: 'CAFE',
    SHOPPING_VOUCHER: 'SHOPPING_VOUCHER',
    'BURGER/PIZZA': 'BURGER/PIZZA',
    'KOREAN/CHINESE/JAPANESE_FOOD/SNACK_BAR':
        'KOREAN/CHINESE/JAPANESE_FOOD/SNACK_BAR',
    'RESTAURANT/BUFFET': 'RESTAURANT/BUFFET',
    'MOVIE/MUSIC/BOOK': 'MOVIE/MUSIC/BOOK',
    'LIFE/HOME_APPLICANCE/ENTERTAINMENT': 'HOME/HOME_APPLICANCE/ENTERTAIN',
    'BAKERY/DONUT/TOKK': 'BAKERY/DONUT/TOKK',
    'ICE-CREAM/BINGSU': 'ICE-CREAM/BINGSU',
    CHICKEN: 'CHICKEN',
    'GRILL/PORK_FEED': 'GRILL/PORK_FEED',
    'FOREIGN/FUSION_FOOD/OTHER': 'FOREIGN/FUSION_FOOD/OTHER',
    'HEALTH/LIVING/FOOT_HALL': 'HEALTH/LIVING/FOOT_HALL',
}

export const POINT_ACTION = {
    ATTENDANCE: 'ATTENDANCE',
    LOTTERY: 'LOTTERY',
    BUY: 'BUY',
    RESERVATION: 'RESERVATION',
    REVIEW: 'REVIEW',
    INVITE: 'INVITE',
}

export const PUSH_NOTIFICATION_TYPE = {
    ONE_TIME: 'ONE_TIME',
    '7_DAYS': 'REGULAR_EVERY_7_DAYS',
    '30_DAYS': 'REGULAR_EVERY_30_DAYS',
}

export const PLATFORM = {
    APPLE: 'APPLE',
    ANDROID: 'ANDROID',
    BROWSER: 'BROWSER',
    BROWSER_MOBILE: 'BROWSER_MOBILE',
}

export const NAV = {
    ROAD_SHOP: 'ROAD_SHOP',
    HOME_MASSAGE: 'HOME_MASSAGE',
    RESTAURANT: 'RESTAURANT',
    EVENT: 'EVENT',
}
export const NAVBAR = {
    HOME: 'Home',
    EVENT: 'Event',
    POST: 'Post',
    CONVERSATION: 'Conversation',
    RESERVATION: 'Reservation',
    SHORT_VIDEO: 'Short video',
    PROFILE: 'Profile',
}
export const USER_PERMISSION = {
    LOGIN_USER: 'LOGIN_USER',
    NON_LOGIN_USER: 'NON_LOGIN_USER',
    BIZ_USER: 'BIZ_USER',
    PAID_USER: 'PAID_USER',
    ADMIN: 'ADMIN'
}

export const SALARY_UNIT = {
    MONTHLY: 'MONTHLY',
    WEEKLY: 'WEEKLY',
}

export const WORKING_DAY = {
    MONDAY: 'MONDAY',
    TUESDAY: 'TUESDAY',
    WEDNESDAY: 'WEDNESDAY',
    THURSDAY: 'THURSDAY',
    FRIDAY: 'FRIDAY',
    SATURDAY: 'SATURDAY',
    SUNDAY: 'SUNDAY',
}

export const AREA_SETTING = {
    GLOBAL : 'GLOBAL',
    KOREA : 'KOREA',
}

export const BLOG_TYPE = {
    BLOG_1: 'BLOG_1',
    BLOG_2: 'BLOG_2',
}

export const PRODUCT_STATE = {
    SOLD_OUT : 'SOLD_OUT',
    ON_SALE : 'ON_SALE',
}

export const WEB_ROUTE = {
    SHOP: '/shop',
    SHOP_SECOND: '/jump-up-shop',
    EVENT: '/event',
    POST: '/post',
    BLOG: '/blog',
    STORE_PROFILE: '/store_profile',
    PROFILE_DESIGN1: '/profile_design1',
    RECRUIT_BOARD: '/recruit',
    SHOP_SALES_BOARD: '/shop-sale',
    SITE: '/site',
    SHOP3: '/shop3',
    SECOND_HAND_MARKET: '/second-hand-market',
    CONVERSATION: '/conversation',
    RESERVATION: '/reservation',
    FIND_TECH: '/find-tech'
}

export const BOARD_ROUTE = {
    [BOARD.DISTANCE_ORDER_BOARD]: WEB_ROUTE.SHOP,
    [BOARD.DISTANCE_ORDER_BOARD_2]: WEB_ROUTE.SHOP,
    [BOARD.DISTANCE_ORDER_BOARD_3]: WEB_ROUTE.SHOP3,
    [BOARD.JUMP_UP_SHOP_LIST_BOARD]: WEB_ROUTE.SHOP_SECOND,
    [BOARD.EVENT_BOARD]: WEB_ROUTE.EVENT,
    [BOARD.BULLETIN_BOARD]: WEB_ROUTE.POST,
    [BOARD.BULLETIN_BOARD_2]: WEB_ROUTE.POST,
    [BOARD.BLOG]: WEB_ROUTE.BLOG,
    [BOARD.SHOP_SALES_BOARD]: WEB_ROUTE.SHOP_SALES_BOARD,
    [BOARD.RECRUIT_BOARD]: WEB_ROUTE.RECRUIT_BOARD,
    [BOARD.RECRUIT_BOARD_2]: WEB_ROUTE.SHOP,
    [BOARD.STORE_PROFILE]: WEB_ROUTE.STORE_PROFILE,
    [BOARD.PROFILE_DESIGN1]: WEB_ROUTE.PROFILE_DESIGN1,
    [BOARD.SITE]: WEB_ROUTE.SITE,
    [BOARD.SECOND_HAND_MARKET]: WEB_ROUTE.SECOND_HAND_MARKET,
    [BOARD.FIND_TECH_BOARD]: WEB_ROUTE.FIND_TECH
}