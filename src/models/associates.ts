import {
  Employee,
  PushNotification,
  User,
  //   Event,
  City,
  District,
  Category,
  Thema,
  Comment,
  Post,
  Shop,
  Event,
  Tag,
  Favourite,
  Review,
  Ward,
  FavouritePost,
  ShopTag,
  Link,
  LinkCategory,
  DislikePost,
  Notification,
  Contact,
  Seo,
  Meta,
  KLocation,
  History,
  Report,
  Faq,
  FaqCategory,
  Course,
  Price,
  Reservation,
  ReservationItem,
  Mentor,
  //   Post,
  //   Comment,
  FavouriteMentor,
  Loyalty,
  Block,
  ShortVideo,
  FavouriteShortVideo,
  Question,
  AnswerQuestion,
  EditUserHistory,
  TicketUsed,
  Feedback,
  Conversation,
  Message,
  PointHistory,
  FeedbackItem,
  ThemaFeedbackItem,
  Blog,
  Group,
  FavouriteReview,
  DislikeReview,
  ViewPost,
  ThemaGroup,
  ShopOffRange,
  SettingProvince,
  SettingDistrict,
  SettingStation,
  SettingStationLine,
  SettingStationSubway,
  ShopTimeRange,
  ViewMentor,
  Site,
  SiteCategory,
  RealEstate,
  RealEstateTag, FavouriteSecondHand, SecondHandMarket, Keyword, KeywordCategory, KeywordType, JobRequest,
  Quote,
  FavoriteJob,
  ExpertInfo,
  ActionLog,
  Board,
  BoardPermission,
  ExpRule,
  Level,
  Ranking,
  Video,
  RankingDetail,
  CarManufacturer,
  CarModel,
  CarOptionGroup,
  CarOption,
  UsedCarPost,
  UsedCarPostTag,
  UsedCarPostOption,
  FavouriteUsedCarPost,
  UsedCarSavedSearch,
  UsedCarInquiry
} from '@/models/tables'
import { RecentReading } from './tables/recent_reading'
import { Recruit } from './tables/recruit'
import {ViewBlog} from "@/models/tables/view_blog";

// Link
Link.belongsTo(Thema, {
  foreignKey: 'thema_id',
  as: 'thema',
})
Thema.hasMany(Link, {
  foreignKey: 'thema_id',
  as: 'links',
})

// Link - Category
LinkCategory.belongsTo(Link, {
  foreignKey: 'link_id',
  as: 'link',
})

Link.hasMany(LinkCategory, {
  foreignKey: 'link_id',
  as: 'categories',
})


LinkCategory.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(LinkCategory, {
  foreignKey: 'category_id',
  as: 'links',
})
// District
District.belongsTo(City, {
  foreignKey: 'city_id',
  as: 'city',
})
City.hasMany(District, {
  foreignKey: 'city_id',
  as: 'districts',
})
// Category
Category.belongsTo(Thema, {
  foreignKey: 'thema_id',
  as: 'thema',
})
Thema.hasMany(Category, {
  foreignKey: 'thema_id',
  as: 'categories',
})
// Comment
Comment.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})
Post.hasMany(Comment, {
  foreignKey: 'post_id',
  as: 'comments',
})
// Comment-reply
// Review.belongsTo(Review, {
//   foreignKey: 'parent_id',
//   as: 'parent_comment',
// });
Review.hasMany(Review, {
  foreignKey: 'parent_id',
  as: 'comment_childs',
})
// review-reply
Review.belongsTo(Review, {
  foreignKey: 'parent_id',
  as: 'parent',
})
Review.hasMany(Review, {
  foreignKey: 'parent_id',
  as: 'review_chilrds',
})

// Post
Post.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})
User.hasMany(Post, {
  foreignKey: 'user_id',
  as: 'posts',
})

Post.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee',
})
Employee.hasMany(Post, {
  foreignKey: 'employee_id',
  as: 'posts',
})

Post.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})
Category.hasMany(Post, {
  foreignKey: 'category_id',
  as: 'posts',
})
// User
Comment.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})
User.hasMany(Comment, {
  foreignKey: 'user_id',
  as: 'comments',
})
// Shop
Shop.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})
User.hasMany(Shop, {
  foreignKey: 'user_id',
  as: 'shops',
})

Shop.belongsTo(City, {
  foreignKey: 'city_id',
  as: 'city',
})

City.hasMany(Shop, {
  foreignKey: 'city_id',
  as: 'shops',
})

Shop.belongsTo(District, {
  foreignKey: 'district_id',
  as: 'district',
})

District.hasMany(Shop, {
  foreignKey: 'district_id',
  as: 'shops',
})

Shop.belongsTo(Ward, {
  foreignKey: 'ward_id',
  as: 'ward',
})

Ward.hasMany(Shop, {
  foreignKey: 'ward_id',
  as: 'shops',
})

Shop.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(Shop, {
  foreignKey: 'category_id',
  as: 'shops',
})

// Shop - Tag - Recruit
Shop.belongsToMany(Tag, {
  through: ShopTag,
  foreignKey: 'shop_id',
  otherKey: 'tag_id',
  as: 'shop_tags',
})

ShopTag.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(ShopTag, {
  foreignKey: 'shop_id',
  as: 'tags',
})

ShopTag.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag',
})

Tag.hasMany(ShopTag, {
  foreignKey: 'tag_id',
  as: 'shops',
})

ShopTag.belongsTo(Recruit, {
  foreignKey: 'recruit_id',
  as: 'recruit',
})

Recruit.hasMany(ShopTag, {
  foreignKey: 'recruit_id',
  as: 'tags',
})

// Favourite
Favourite.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(Favourite, {
  foreignKey: 'shop_id',
  as: 'favored_users',
})

Favourite.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Favourite, {
  foreignKey: 'user_id',
  as: 'favourite_shops',
})

Favourite.belongsTo(Recruit, {
  foreignKey: 'recruit_id',
  as: 'recruit',
})

Recruit.hasMany(Favourite, {
  foreignKey: 'recruit_id',
  as: 'favourite_shops',
})

// Favourite Post
FavouritePost.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})

Post.hasMany(FavouritePost, {
  foreignKey: 'post_id',
  as: 'favored_users',
})

FavouritePost.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

// Favourite Review
FavouriteReview.belongsTo(Review, {
  foreignKey: 'review_id',
  as: 'review',
})

Review.hasMany(FavouriteReview, {
  foreignKey: 'review_id',
  as: 'favored_users',
})

FavouriteReview.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(FavouriteReview, {
  foreignKey: 'user_id',
  as: 'favourite_reviews',
})

// Favourite Mentor
Mentor.hasMany(FavouriteMentor, {
  foreignKey: 'mentor_id',
  as: 'favourite_mentor',
})

FavouriteMentor.belongsTo(Mentor, {
  foreignKey: 'mentor_id',
  as: 'mentor',
})

User.hasMany(FavouriteMentor, {
  foreignKey: 'user_id',
  as: 'favourite_mentor',
})

FavouriteMentor.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

// Dislike Post
DislikePost.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})

Post.hasMany(DislikePost, {
  foreignKey: 'post_id',
  as: 'dislike_users',
})

ViewPost.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})

Post.hasMany(ViewPost, {
  foreignKey: 'post_id',
  as: 'view_users',
})

DislikePost.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(DislikePost, {
  foreignKey: 'user_id',
  as: 'dislike_posts',
})

// Dislike Review
DislikeReview.belongsTo(Review, {
  foreignKey: 'review_id',
  as: 'review',
})

Review.hasMany(DislikeReview, {
  foreignKey: 'review_id',
  as: 'dislike_users',
})

DislikeReview.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(DislikeReview, {
  foreignKey: 'user_id',
  as: 'dislike_reviews',
})

// Review
Review.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(Review, {
  foreignKey: 'shop_id',
  as: 'reviews',
})

Review.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Review, {
  foreignKey: 'employee_id',
  as: 'reviews',
})

Review.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee',
})

Employee.hasMany(Review, {
  foreignKey: 'user_id',
  as: 'reviews',
})

Review.belongsTo(Recruit, {
  foreignKey: 'recruit_id',
  as: 'recruit',
})

Recruit.hasMany(Review, {
  foreignKey: 'recruit_id',
  as: 'reviews',
})

Review.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})

Post.hasMany(Review, {
  foreignKey: 'post_id',
  as: 'reviews',
})

// Event
Event.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})
User.hasMany(Event, {
  foreignKey: 'user_id',
  as: 'events',
})

Event.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(Event, {
  foreignKey: 'shop_id',
  as: 'events',
})

// notification
Notification.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Notification, {
  foreignKey: 'user_id',
  as: 'notifications',
})

Notification.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee',
})

Employee.hasMany(Notification, {
  foreignKey: 'employee_id',
  as: 'notifications',
})

// Recent Reading
RecentReading.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(RecentReading, {
  foreignKey: 'shop_id',
  as: 'recent_readings',
})

RecentReading.belongsTo(Recruit, {
  foreignKey: 'recruit_id',
  as: 'recruit',
})

Recruit.hasMany(RecentReading, {
  foreignKey: 'recruit_id',
  as: 'recent_readings',
})

RecentReading.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Favourite, {
  foreignKey: 'user_id',
  as: 'recent_readings',
})

Contact.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Contact, {
  foreignKey: 'user_id',
  as: 'contacts',
})

Meta.belongsTo(Seo, {
  foreignKey: 'seo_id',
  as: 'seo',
})

Seo.hasMany(Meta, {
  foreignKey: 'seo_id',
  as: 'metas',
})

// Tag:
Tag.belongsTo(Thema, {
  foreignKey: 'thema_id',
  as: 'thema',
})

Thema.hasMany(Tag, {
  foreignKey: 'thema_id',
  as: 'tags',
})

// Recruit
Recruit.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Recruit, {
  foreignKey: 'user_id',
  as: 'recruits',
})

Recruit.belongsTo(Thema, {
  foreignKey: 'thema_id',
  as: 'thema',
})

Thema.hasMany(Recruit, {
  foreignKey: 'thema_id',
  as: 'recruits',
})

Recruit.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(Recruit, {
  foreignKey: 'category_id',
  as: 'recruits',
})

Recruit.belongsTo(KLocation, {
  foreignKey: 'klocation_id',
  as: 'klocation',
})
KLocation.hasMany(Recruit, {
  foreignKey: 'klocation_id',
  as: 'recruits',
})

// History:
History.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(History, {
  foreignKey: 'shop_id',
  as: 'histories',
})

History.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(History, {
  foreignKey: 'user_id',
  as: 'histories',
})

History.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})
Post.hasMany(History, {
  foreignKey: 'post_id',
  as: 'histories',
})

History.belongsTo(Review, {
  foreignKey: 'review_id',
  as: 'review',
})
Review.hasMany(History, {
  foreignKey: 'review_id',
  as: 'histories',
})

// REPORT:
Report.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})
Shop.hasMany(Report, {
  foreignKey: 'shop_id',
  as: 'reports',
})

Report.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})
User.hasMany(Report, {
  foreignKey: 'user_id',
  as: 'reports',
})

Report.belongsTo(Post, {
  foreignKey: 'post_id',
  as: 'post',
})
Post.hasMany(Report, {
  foreignKey: 'post_id',
  as: 'reports',
})

Report.belongsTo(Review, {
  foreignKey: 'review_id',
  as: 'review',
})
Review.hasMany(Report, {
  foreignKey: 'review_id',
  as: 'reports',
})

Faq.belongsTo(FaqCategory, {
  foreignKey: 'faq_category_id',
  as: 'faq_category',
})
FaqCategory.hasMany(Faq, {
  foreignKey: 'faq_category_id',
  as: 'faqs',
})

Course.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})
Shop.hasMany(Course, {
  foreignKey: 'shop_id',
  as: 'courses',
})

Price.belongsTo(Course, {
  foreignKey: 'course_id',
  as: 'course',
})
Course.hasMany(Price, {
  foreignKey: 'course_id',
  as: 'prices',
})

Shop.hasMany(Mentor, {
  foreignKey: 'shop_id',
  as: 'mentors',
})

Mentor.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(Loyalty, {
  foreignKey: 'shop_id',
  as: 'loyalties',
})

Loyalty.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

User.hasMany(Loyalty, {
  foreignKey: 'user_id',
  as: 'loyalties',
})

Loyalty.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

Reservation.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Shop.hasMany(Reservation, {
  foreignKey: 'shop_id',
  as: 'reservations',
})

Reservation.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Reservation, {
  foreignKey: 'user_id',
  as: 'reservations',
})

Reservation.belongsTo(User, {
  foreignKey: 'seller_id',
  as: 'seller',
})

User.hasMany(Reservation, {
  foreignKey: 'seller_id',
  as: 'seller_reservations',
})

ReservationItem.belongsTo(Reservation, {
  foreignKey: 'reservation_id',
  as: 'reservation',
})

Reservation.hasMany(ReservationItem, {
  foreignKey: 'reservation_id',
  as: 'items',
})

ReservationItem.belongsTo(Price, {
  foreignKey: 'price_id',
  as: 'price',
})

Price.hasMany(ReservationItem, {
  foreignKey: 'price_id',
  as: 'reservation_items',
})

FavouriteShortVideo.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(FavouriteShortVideo, {
  foreignKey: 'user_id',
  as: 'favourite_short_video',
})

FavouriteShortVideo.belongsTo(ShortVideo, {
  foreignKey: 'short_video_id',
  as: 'short_video',
})

ShortVideo.hasMany(FavouriteShortVideo, {
  foreignKey: 'short_video_id',
  as: 'favourite_short_video',
})

// ShopBlock
Block.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Block, {
  foreignKey: 'user_id',
  as: 'blocks',
})

Block.belongsTo(User, {
  foreignKey: 'user_id_blocked',
  as: 'user_blocked',
})

User.hasMany(Block, {
  foreignKey: 'user_id_blocked',
  as: 'blocked',
})

Question.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(Question, {
  foreignKey: 'user_id',
  as: 'questions',
})

AnswerQuestion.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

User.hasMany(AnswerQuestion, {
  foreignKey: 'user_id',
  as: 'answer_question',
})

AnswerQuestion.belongsTo(Question, {
  foreignKey: 'question_id',
  as: 'question',
})

Question.hasMany(AnswerQuestion, {
  foreignKey: 'question_id',
  as: 'answer_question',
})

AnswerQuestion.hasMany(AnswerQuestion, {
  foreignKey: 'parent_id',
  as: 'answer_child',
})

AnswerQuestion.belongsTo(AnswerQuestion, {
  foreignKey: 'parent_id',
  as: 'parent',
})

User.hasMany(EditUserHistory, {
  foreignKey: 'user_id',
  as: 'user_edit_history',
  onDelete: 'cascade',
})

EditUserHistory.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

// TicketUsed
User.hasMany(TicketUsed, {
  foreignKey: 'user_id',
  as: 'user_ticket',
  onDelete: 'cascade',
})

TicketUsed.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

Shop.hasMany(TicketUsed, {
  foreignKey: 'shop_id',
  as: 'shop_ticket',
  onDelete: 'cascade',
})

TicketUsed.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

// Feedback
Review.hasMany(Feedback, {
  foreignKey: 'review_id',
  as: 'feedbacks',
  onDelete: 'cascade',
})

Feedback.belongsTo(Review, {
  foreignKey: 'review_id',
  as: 'review',
})

Feedback.belongsTo(FeedbackItem, {
  foreignKey: 'feedback_item_id',
  as: 'feedback_item',
})

// Conversation
Conversation.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

Conversation.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Conversation.belongsTo(Message, {
  foreignKey: 'last_message_id',
  as: 'last_message',
})

Message.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

Message.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
})

Conversation.hasMany(Message, {
  foreignKey: 'conversation_id',
  as: 'messages',
})

Message.belongsTo(Conversation, {
  foreignKey: 'conversation_id',
  as: 'conversation',
})

//Point
PointHistory.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

//Thema-Feedback-Item
Thema.belongsToMany(FeedbackItem, {
  through: ThemaFeedbackItem,
  foreignKey: 'thema_id',
  as: 'feedback_items',
})

FeedbackItem.belongsToMany(Thema, {
  through: ThemaFeedbackItem,
  foreignKey: 'feedback_item_id',
  as: 'themas',
})

Review.belongsToMany(FeedbackItem, {
  through: Feedback,
  foreignKey: 'review_id',
  otherKey: 'feedback_item_id',
  as: 'feedback_items',
})

Blog.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(Blog, {
  foreignKey: 'category_id',
  as: 'blogs',
})
User.belongsTo(Group, {
  foreignKey: 'group_id',
  as: 'new_group',
})
Group.hasMany(User, {
  foreignKey: 'group_id',
  as: 'users',
})

ViewBlog.belongsTo(Blog, {
  foreignKey: 'blog_id',
  as: 'blog',
})

Blog.hasMany(ViewBlog, {
  foreignKey: 'blog_id',
  as: 'view_users',
})
//thema group
ThemaGroup.belongsTo(Group, {
  foreignKey: 'view_group_id',
  as: 'group_view',
})

Group.hasMany(ThemaGroup, {
  foreignKey: 'view_group_id',
  as: 'group_views',
})
ThemaGroup.belongsTo(Group, {
  foreignKey: 'post_group_id',
  as: 'group_post',
})

Group.hasMany(ThemaGroup, {
  foreignKey: 'post_group_id',
  as: 'group_posts',
})

ThemaGroup.belongsTo(Group, {
  foreignKey: 'comment_group_id',
  as: 'group_comment',
})

Group.hasMany(ThemaGroup, {
  foreignKey: 'comment_group_id',
  as: 'group_comments',
})

ThemaGroup.belongsTo(Thema, {
  foreignKey: 'thema_id',
  as: 'thema',
})

Thema.hasMany(ThemaGroup, {
  foreignKey: 'thema_id',
  as: 'groups',
})

// ShopOffRange.belongsTo(Shop, {
//   foreignKey: 'shop_id',
//   as: 'shop',
//   onDelete : "cascade"
// })
//
// Shop.hasMany(ShopOffRange, {
//   foreignKey: 'shop_id',
//   as: 'time_ranges',
// })

SettingDistrict.belongsTo(SettingProvince, {
  foreignKey: 'setting_province_id',
  as: 'province',
  onDelete : "cascade"
})

SettingProvince.hasMany(SettingDistrict, {
  foreignKey: 'setting_province_id',
  as: 'districts',
})

SettingStationLine.belongsTo(SettingStation, {
  foreignKey: 'setting_station_id',
  as: 'station',
  onDelete : "cascade"
})

SettingStation.hasMany(SettingStationLine, {
  foreignKey: 'setting_station_id',
  as: 'lines',
})

SettingStationSubway.belongsTo(SettingStationLine, {
  foreignKey: 'setting_station_line_id',
  as: 'line',
  onDelete : "cascade"
})

SettingStationLine.hasMany(SettingStationSubway, {
  foreignKey: 'setting_station_line_id',
  as: 'subways',
})

ShopTimeRange.belongsTo(Shop, {
  foreignKey: 'shop_id',
  as: 'shop',
  onDelete : "cascade"
})

Shop.hasMany(ShopTimeRange, {
  foreignKey: 'shop_id',
  as: 'time_ranges',
})

Report.belongsTo(Conversation, {
  foreignKey: 'chat_id',
  as: 'conversation',
})
Conversation.hasMany(Report, {
  foreignKey: 'chat_id',
  as: 'reports',
})

// View Mentor
Mentor.hasMany(ViewMentor, {
  foreignKey: 'mentor_id',
  as: 'view_mentor',
})

ViewMentor.belongsTo(Mentor, {
  foreignKey: 'mentor_id',
  as: 'mentor',
})

Site.belongsTo(SiteCategory, {
  foreignKey: 'site_category_id',
  as: 'site_category',
})
SiteCategory.hasMany(Site, {
  foreignKey: 'site_category_id',
  as: 'sites',
})

Conversation.belongsTo(User, {
  foreignKey: 'user_id_2',
  as: 'user2',
})

Conversation.belongsTo(Shop, {
  foreignKey: 'shop_id_2',
  as: 'shop2',
})

//real estate

RealEstate.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(RealEstate, {
  foreignKey: 'category_id',
  as: 'real_estates',
})

RealEstate.belongsToMany(Tag, {
  through: RealEstateTag,
  foreignKey: 'real_estate_id',
  otherKey: 'tag_id',
  as: 'real_estate_tags',
})

RealEstateTag.belongsTo(RealEstate, {
  foreignKey: 'real_estate_id',
  as: 'real_estate',
})

RealEstate.hasMany(RealEstateTag, {
  foreignKey: 'real_estate_id',
  as: 'tags',
})

RealEstateTag.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag',
})

Tag.hasMany(RealEstateTag, {
  foreignKey: 'tag_id',
  as: 'real_estates',
})

FavouriteSecondHand.belongsTo(SecondHandMarket, {
  foreignKey: 'second_hand_market_id',
  as: 'product',
})

SecondHandMarket.hasMany(FavouriteSecondHand, {
  foreignKey: 'second_hand_market_id',
  as: 'favored_users',
})

FavouriteSecondHand.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})

SecondHandMarket.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(SecondHandMarket, {
  foreignKey: 'category_id',
  as: 'products',
})
SecondHandMarket.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
})
User.hasMany(SecondHandMarket, {
  foreignKey: 'user_id',
  as: 'products',
})

Message.belongsTo(Recruit, {
  foreignKey: 'recruit_id',
  as: 'recruit',
})

Recruit.hasMany(Message, {
  foreignKey: 'recruit_id',
  as: 'messages',
})

Keyword.belongsTo(KeywordCategory, {
  foreignKey: 'keyword_category_id',
  as: 'keyword_category',
})
KeywordCategory.hasMany(Keyword, {
  foreignKey: 'keyword_category_id',
  as: 'keywords',
})

KeywordCategory.belongsTo(KeywordType, {
  foreignKey: 'keyword_type_id',
  as: 'keyword_type',
})
KeywordType.hasMany(KeywordCategory, {
  foreignKey: 'keyword_type_id',
  as: 'keyword_categories',
})

KeywordType.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
})

Category.hasMany(KeywordType, {
  foreignKey: 'category_id',
  as: 'types',
})

KeywordCategory.belongsTo(SettingProvince, {
  foreignKey: 'province_id',
  as: 'province',
})

SettingProvince.hasMany(KeywordCategory, {
  foreignKey: 'province_id',
  as: 'keywordCategories',
})

KeywordCategory.belongsTo(SettingDistrict, {
  foreignKey: 'district_id',
  as: 'district',
})

SettingDistrict.hasMany(KeywordCategory, {
  foreignKey: 'district_id',
  as: 'keywordCategories',
})

JobRequest.belongsTo(User, {
  foreignKey: "user_id",
  as: "user",
});

User.hasMany(JobRequest, {
  foreignKey: "user_id",
  as: "job_requests",
});

Quote.belongsTo(User, {
  foreignKey: "user_id",
  as: "user",
});
Quote.belongsTo(JobRequest, {
  foreignKey: "job_id",
  as: "job",
});
JobRequest.hasMany(Quote, { foreignKey: 'job_id', as: 'quote' });
JobRequest.hasOne(Quote, {
  foreignKey: 'job_id',
  as: 'confirmed_quotes',
  scope: { is_confirmed: true },
});
User.hasMany(Quote, {
  foreignKey: "user_id",
  as: "user",
});
JobRequest.hasMany(Quote, { as: 'quotes', foreignKey: 'job_id' })
Quote.belongsTo(JobRequest, { foreignKey: 'job_id' })
FavoriteJob.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

FavoriteJob.belongsTo(JobRequest, {
  foreignKey: 'job_id',
  as: 'job',
});
User.hasMany(FavoriteJob, {
  foreignKey: 'user_id',
  as: 'favorite_jobs',
});
JobRequest.hasMany(FavoriteJob, {
  foreignKey: 'job_id',
  as: 'favorites',
});

// ExpertInfo.belongsTo(User, {
//   foreignKey: 'user_id',
//   as: 'user',
//   onDelete: 'CASCADE',
// });
// User.hasOne(ExpertInfo, {
//   foreignKey: 'user_id',
//   as: 'expertInfo',
// });
User.belongsTo(ExpertInfo, {
  foreignKey: 'expert_info_id',
  as: 'expertInfo',
});
ExpertInfo.hasOne(User, {
  foreignKey: 'expert_info_id',
  as: 'user',
});

User.belongsTo(Level, {
  foreignKey: 'level_id',
  as: 'exp_level',
});

User.hasMany(ActionLog, {
  foreignKey: 'user_id',
  as: 'action_logs',
});

User.hasMany(Ranking, {
  foreignKey: 'user_id',
  as: 'rankings',
});
Level.hasMany(User, {
  foreignKey: 'level_id',
  as: 'users',
});

Level.hasMany(BoardPermission, {
  foreignKey: 'level_id',
  as: 'permissions',
});

BoardPermission.belongsTo(Board, {
  foreignKey: 'board_id',
  as: 'board',
});

BoardPermission.belongsTo(Level, {
  foreignKey: 'level_id',
  as: 'level',
});

Board.hasMany(BoardPermission, {
  foreignKey: 'board_id',
  as: 'permissions',
});

ActionLog.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

Ranking.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

Ranking.hasMany(RankingDetail, {
  foreignKey: 'ranking_id',
  as: 'ranking_details',
});
RankingDetail.belongsTo(Ranking, {
  foreignKey: 'ranking_id',
  as: 'ranking',
});

Shop.hasMany(Video, { foreignKey: 'shop_id' });
Video.belongsTo(Shop, { foreignKey: 'shop_id' });
// //Post
// Post.belongsTo(City, {
//     foreignKey: 'city_id',
//     as: 'city'
// })
// City.hasMany(Post, {
//     foreignKey: 'city_id',
//     as: 'posts'
// })
// Post.belongsTo(Ward, {
//     foreignKey: 'ward_id',
//     as: 'ward'
// })
// Ward.hasMany(Post, {
//     foreignKey: 'ward_id',
//     as: 'posts'
// })
// Post.belongsTo(District, {
//     foreignKey: 'district_id',
//     as: 'district'
// })
// District.hasMany(Post, {
//     foreignKey: 'district_id',
//     as: 'posts'
// })
// Post.belongsTo(User, {
//     foreignKey: 'user_id',
//     as: 'user'
// })
// User.hasMany(Post, {
//     foreignKey: 'user_id',
//     as: 'posts'
// })
// //Favorite_post
// FavoritePost.belongsTo(User, {
//     foreignKey: 'user_id',
//     as: 'user'
// })
// User.hasMany(FavoritePost, {
//     foreignKey: 'user_id',
//     as: 'favorite_posts'
// })
// FavoritePost.belongsTo(Post, {
//     foreignKey: 'post_id',
//     as: 'post'
// })
// Post.hasMany(FavoritePost, {
//     foreignKey: 'post_id',
//     as: 'favorite_users'
// })
// //Post_event
// PostEvent.belongsTo(Event, {
//     foreignKey: 'event_id',
//     as: 'event'
// })
// Event.hasMany(PostEvent, {
//     foreignKey: 'event_id',
//     as: 'event_posts'
// })
// PostEvent.belongsTo(Post, {
//     foreignKey: 'post_id',
//     as: 'post'

// Used Car Bulletin Board Associations

// CarManufacturer relationships
CarManufacturer.hasMany(CarModel, {
  foreignKey: 'manufacturer_id',
  as: 'models',
});

CarManufacturer.hasMany(UsedCarPost, {
  foreignKey: 'manufacturer_id',
  as: 'used_car_posts',
});

// CarModel relationships
CarModel.belongsTo(CarManufacturer, {
  foreignKey: 'manufacturer_id',
  as: 'manufacturer',
});

CarModel.hasMany(UsedCarPost, {
  foreignKey: 'model_id',
  as: 'used_car_posts',
});

// CarOptionGroup relationships
CarOptionGroup.hasMany(CarOption, {
  foreignKey: 'option_group_id',
  as: 'options',
});

// CarOption relationships
CarOption.belongsTo(CarOptionGroup, {
  foreignKey: 'option_group_id',
  as: 'option_group',
});

CarOption.hasMany(UsedCarPostOption, {
  foreignKey: 'car_option_id',
  as: 'used_car_post_options',
});

// UsedCarPost relationships
UsedCarPost.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'seller',
});

User.hasMany(UsedCarPost, {
  foreignKey: 'user_id',
  as: 'used_car_posts',
});

UsedCarPost.belongsTo(CarManufacturer, {
  foreignKey: 'manufacturer_id',
  as: 'manufacturer',
});

UsedCarPost.belongsTo(CarModel, {
  foreignKey: 'model_id',
  as: 'model',
});

UsedCarPost.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
});

Category.hasMany(UsedCarPost, {
  foreignKey: 'category_id',
  as: 'used_car_posts',
});

// Many-to-many relationship: UsedCarPost <-> Tag through UsedCarPostTag
UsedCarPost.belongsToMany(Tag, {
  through: UsedCarPostTag,
  foreignKey: 'used_car_post_id',
  otherKey: 'tag_id',
  as: 'tags',
});

Tag.belongsToMany(UsedCarPost, {
  through: UsedCarPostTag,
  foreignKey: 'tag_id',
  otherKey: 'used_car_post_id',
  as: 'used_car_posts',
});

// UsedCarPostTag join table relationships
UsedCarPostTag.belongsTo(UsedCarPost, {
  foreignKey: 'used_car_post_id',
  as: 'used_car_post',
});

UsedCarPost.hasMany(UsedCarPostTag, {
  foreignKey: 'used_car_post_id',
  as: 'post_tags',
});

UsedCarPostTag.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag',
});

Tag.hasMany(UsedCarPostTag, {
  foreignKey: 'tag_id',
  as: 'used_car_post_tags',
});

// UsedCarPostOption relationships (many-to-many UsedCarPost <-> CarOption)
UsedCarPostOption.belongsTo(UsedCarPost, {
  foreignKey: 'used_car_post_id',
  as: 'used_car_post',
});

UsedCarPost.hasMany(UsedCarPostOption, {
  foreignKey: 'used_car_post_id',
  as: 'car_options',
});

UsedCarPostOption.belongsTo(CarOption, {
  foreignKey: 'car_option_id',
  as: 'car_option',
});

// FavouriteUsedCarPost relationships
FavouriteUsedCarPost.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

User.hasMany(FavouriteUsedCarPost, {
  foreignKey: 'user_id',
  as: 'favourite_used_car_posts',
});

FavouriteUsedCarPost.belongsTo(UsedCarPost, {
  foreignKey: 'used_car_post_id',
  as: 'used_car_post',
});

UsedCarPost.hasMany(FavouriteUsedCarPost, {
  foreignKey: 'used_car_post_id',
  as: 'favourited_by_users',
});

// UsedCarSavedSearch relationships
UsedCarSavedSearch.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

User.hasMany(UsedCarSavedSearch, {
  foreignKey: 'user_id',
  as: 'saved_searches',
});

UsedCarSavedSearch.belongsTo(CarManufacturer, {
  foreignKey: 'manufacturer_id',
  as: 'manufacturer',
});

CarManufacturer.hasMany(UsedCarSavedSearch, {
  foreignKey: 'manufacturer_id',
  as: 'saved_searches',
});

UsedCarSavedSearch.belongsTo(CarModel, {
  foreignKey: 'model_id',
  as: 'model',
});

CarModel.hasMany(UsedCarSavedSearch, {
  foreignKey: 'model_id',
  as: 'saved_searches',
});

UsedCarSavedSearch.belongsTo(Category, {
  foreignKey: 'category_id',
  as: 'category',
});

Category.hasMany(UsedCarSavedSearch, {
  foreignKey: 'category_id',
  as: 'saved_searches',
});

// UsedCarInquiry relationships
UsedCarInquiry.belongsTo(UsedCarPost, {
  foreignKey: 'used_car_post_id',
  as: 'used_car_post',
});

UsedCarPost.hasMany(UsedCarInquiry, {
  foreignKey: 'used_car_post_id',
  as: 'inquiries',
});

UsedCarInquiry.belongsTo(User, {
  foreignKey: 'buyer_id',
  as: 'buyer',
});

UsedCarInquiry.belongsTo(User, {
  foreignKey: 'seller_id',
  as: 'seller',
});

User.hasMany(UsedCarInquiry, {
  foreignKey: 'buyer_id',
  as: 'sent_inquiries',
});

User.hasMany(UsedCarInquiry, {
  foreignKey: 'seller_id',
  as: 'received_inquiries',
});
