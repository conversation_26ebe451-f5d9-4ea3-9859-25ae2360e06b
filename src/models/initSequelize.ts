import * as models from './tables';
import { Sequelize } from 'sequelize';

export const sequelizeInstance = new Sequelize({
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  host: process.env.HOST,
  port: parseInt(process.env.DB_PORT || '5432'),
  dialect: 'postgresql',
  dialectOptions: {
    ssl: false
  },
});

export const initSequelize = async () => {
  Object.values(models).forEach((model: any) => {
    if (typeof model.associate === 'function') {
      model.associate(models);
    }
  });

  console.log('[initSequelize] Sequelize models initialized');
};
