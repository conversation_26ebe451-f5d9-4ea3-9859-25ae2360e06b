import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import {SHOP_STATE} from '@/const'
import {Conversation, Message, Reservation, Event} from '.'

let _ = require('lodash')

export const Shop = sequelize.define(
    'tbl_shop',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        is_random_20_shop: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        title: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
        },
        description_content: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        min_price: {
            type: Sequelize.TEXT,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        denied_message: {
            type: Sequelize.TEXT,
        },
        opening_hours: {
            type: Sequelize.STRING,
        },
        alias: {
            type: Sequelize.STRING,
        },
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        thumbnails: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        tag_ids: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        position: {
            type: Sequelize.GEOMETRY('POINT', 4326),
            defaultValue: {
                type: 'Point',
                coordinates: [0, 0],
                crs: {type: 'name', properties: {name: 'EPSG:4326'}},
            },
        },
        comment: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        soft_comment_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_records: {
            type: Sequelize.ARRAY({type: Sequelize.INTEGER}),
            defaultValue: [0, 0, 0],
        },
        view: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_daily: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_weekly: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_monthly: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_last_3_months: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        like: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        contact_phone: {
            type: Sequelize.STRING,
            defaultValue: false,
        },
        badge_color: {
            type: Sequelize.STRING,
        },
        badge_text: {
            type: Sequelize.STRING,
        },
        latitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        longitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        state: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        SHOP_STATE.PENDING,
                        SHOP_STATE.APPROVED,
                        SHOP_STATE.REJECTED,
                        SHOP_STATE.REMOVED,
                        SHOP_STATE.EXPIRED,
                    ],
                ],
            },
            defaultValue: SHOP_STATE.PENDING,
        },
        expired_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        short_address: {
            type: Sequelize.STRING,
        },
        kakaolink_url: {
            type: Sequelize.STRING,
        },
        address: {
            type: Sequelize.STRING,
        },
        address_2: {
            type: Sequelize.STRING,
        },
        created_by_admin: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        params_update: {
            type: Sequelize.JSONB,
        },
        old_shop: {
            type: Sequelize.JSONB,
        },
        denied_shop: {
            type: Sequelize.JSONB,
        },
        theme_color: {
            type: Sequelize.STRING,
        },
        badge_image: {
            type: Sequelize.STRING,
        },
        short_description: {
            type: Sequelize.STRING,
        },
        start_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        geolocation_api_type: {
            type: Sequelize.STRING,
            defaultValue: 'NAVER',
            allowNull: false,
        },
        jump_interval: {
            type: Sequelize.INTEGER,
        },
        next_jump_time: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        jump_order: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        jump_count: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        payment_methods: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
        },
        reservation_times: {
            type: Sequelize.ARRAY({type: Sequelize.JSONB}),
            defaultValue: [],
        },
        shop_province: {
            type: Sequelize.INTEGER,
        },
        shop_district: {
            type: Sequelize.INTEGER,
        },
        // foreign key:
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        city_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_city',
                key: 'id',
            },
        },
        district_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_district',
                key: 'id',
            },
        },
        ward_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_ward',
                key: 'id',
            },
        },
        category_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_category',
                key: 'id',
            },
        },
        mentor_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        course_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        course_state: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        loyalty_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        number_loyalty: {
            type: Sequelize.INTEGER,
        },
        loyalty_service: {
            type: Sequelize.STRING,
        },
        reservation_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        messenger_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        is_home: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        return_policy: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        bank: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        bank_account: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        account_number: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        subway_line: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        subway_station: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        subway_location: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        call: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        opening_hours_weekend: {
            type: Sequelize.STRING,
            allowNull : true
        },
        card_information: {
            type: Sequelize.JSONB,
            allowNull: true,
        },
        leave_day: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
        },
        has_holiday: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
        },
        working_day: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        holiday_day: {
            type: Sequelize.ARRAY({type: Sequelize.JSONB}),
            allowNull: true,
        },
        bank_information: {
            type: Sequelize.JSONB,
            allowNull: true,
        },
        payment_method: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        is_open: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
        },
        break_time: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        break_time_weekend: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        holiday_setting: {
            type: Sequelize.STRING,
            allowNull: true,
            validate: {
                isIn: [
                    [
                        'ONLY_LUNAR_NEW_YEAR_AND_CHUSEOK',
                        'ALL',
                        'OTHER',
                    ],
                ],
            },
        },
        slug: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        keywords: {
            type: Sequelize.TEXT,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    }
                }
                item.alias = item.title.trim().split('/').join('-').split(' ').join('-')
                if (item.description && !item.description_content) {
                    function replaceHtml(htmlStr: String) {
                        let regexTag = /(<([^>]+)>)/gi
                        let regexLink = /(^\w+:|^)\/\//
                        const htmlString = htmlStr
                        const stripedHtml = htmlString.replace(regexTag, '')
                        const result = stripedHtml.replace(regexLink, '')
                        return result
                    }

                    item.description_content = replaceHtml(item.description)
                    if (item.old_shop && item.old_shop.description) {
                        item.old_shop.description_content = replaceHtml(
                            item.old_shop.description
                        )
                    }
                    if (item.denied_shop && item.denied_shop.description) {
                        item.denied_shop.description_content = replaceHtml(
                            item.denied_shop.description
                        )
                    }
                }
            },
            beforeUpdate: (item: any) => {
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    }
                }
                if (item.title) {
                    item.alias = item.title
                        .trim()
                        .split('/')
                        .join('-')
                        .split(' ')
                        .join('-')
                }
                // if (item.description) {
                //     function replaceHtml(htmlStr: String) {
                //         let regexTag = /(<([^>]+)>)/gi
                //         let regexLink = /(^\w+:|^)\/\//
                //         const htmlString = htmlStr
                //         const stripedHtml = htmlString.replace(regexTag, '')
                //         const result = stripedHtml.replace(regexLink, '')
                //         return result
                //     }
                //
                //     item.description_content = replaceHtml(item.description)
                //     if (item.old_shop && item.old_shop.description) {
                //         item.old_shop.description_content = replaceHtml(
                //             item.old_shop.description
                //         )
                //     }
                //     if (item.denied_shop && item.denied_shop.description) {
                //         item.denied_shop.description_content = replaceHtml(
                //             item.denied_shop.description
                //         )
                //     }
                // }
            },
            beforeDestroy: async (item: any) => {
              await Message.destroy({where: {shop_id: item.id}})
              await Conversation.destroy({where: {shop_id: item.id}})
              await Reservation.destroy({where: {shop_id: item.id}})
              await Event.destroy({where: {shop_id: item.id}})
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {
                exclude: ['deleted_at'], // CODE remove description for API GET LIST OF SHOP in queryMiddleware-postgres.ts
            },
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
