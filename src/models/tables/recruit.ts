import {sequelize, Sequelize} from '../base';
import * as moment from 'moment';
import {RECRUIT_TYPE, SALARY_UNIT, SHOP_STATE, WORKING_DAY} from '@/const';
import {Favourite, RecentReading, Review} from '.';

export const Recruit = sequelize.define(
    'tbl_recruit',
    {
        // default field:
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        gender_acceptable: {
            type: Sequelize.STRING,
        },
        age_acceptable: {
            type: Sequelize.STRING,
        },
        title: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        location: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        work_experience: {
            type: Sequelize.STRING,
        },
        nationality_acceptable: {
            type: Sequelize.STRING,
        },
        working_hours: {
            type: Sequelize.STRING,
        },
        telephone_number: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
        },
        verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        denied_message: {
            type: Sequelize.TEXT,
        },
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        thumbnails: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        videos: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
        },
        position: {
            type: Sequelize.GEOMETRY('POINT', 4326),
            defaultValue: {
                type: 'Point',
                coordinates: [0, 0],
                crs: {type: 'name', properties: {name: 'EPSG:4326'}},
            },
        },
        comment: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        soft_comment_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_records: {
            type: Sequelize.ARRAY({type: Sequelize.INTEGER}),
            defaultValue: [0, 0, 0],
        },
        view: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_daily: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_weekly: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_monthly: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view_last_3_months: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        like: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        latitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        longitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        state: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        SHOP_STATE.PENDING,
                        SHOP_STATE.APPROVED,
                        SHOP_STATE.REJECTED,
                        SHOP_STATE.REMOVED,
                        SHOP_STATE.EXPIRED,
                    ],
                ],
            },
            defaultValue: SHOP_STATE.PENDING,
        },
        type: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        RECRUIT_TYPE.RECRUIT,
                        RECRUIT_TYPE.JOB_SEEKER,
                        RECRUIT_TYPE.SHOP_SALES
                    ],
                ],
            },
            defaultValue: RECRUIT_TYPE.RECRUIT,
        },
        geolocation_api_type: {
            type: Sequelize.STRING,
            defaultValue: 'NAVER'
        },
        expired_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        kakaolink_url: {
            type: Sequelize.STRING,
        },
        address: {
            type: Sequelize.STRING,
        },
        address_2: {
            type: Sequelize.STRING,
        },
        size: {
            type: Sequelize.STRING,
        },
        deposit: {
            type: Sequelize.STRING,
        },
        montly_rental_fee: {
            type: Sequelize.STRING,
        },
        premium: {
            type: Sequelize.STRING,
        },
        alias: {
            type: Sequelize.STRING,
        },
        created_by_admin: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        tag_ids: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        old_post: {
            type: Sequelize.JSONB,
        },
        denied_post: {
            type: Sequelize.JSONB,
        },
        start_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        // foreign key:
        klocation_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_klocation',
                key: 'id',
            },
        },
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        thema_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_thema',
                key: 'id',
            },
        },
        category_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_category',
                key: 'id',
            },
        },
        salary: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            allowNull: true,
        },
        salary_unit: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        SALARY_UNIT.MONTHLY,
                        SALARY_UNIT.WEEKLY,
                    ],
                ],
            },
            allowNull: true,
        },
        leave_day: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
        },
        working_day: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            // validate: {
            //     isIn: [
            //         [
            //             WORKING_DAY.MONDAY,
            //             WORKING_DAY.TUESDAY,
            //             WORKING_DAY.WEDNESDAY,
            //             WORKING_DAY.THURSDAY,
            //             WORKING_DAY.FRIDAY,
            //             WORKING_DAY.SATURDAY,
            //             WORKING_DAY.SUNDAY,
            //         ],
            //     ],
            // },
            allowNull: true,
        },
        start_time: {
            type: Sequelize.TIME,
            allowNull: true
        },
        end_time: {
            type: Sequelize.TIME,
            allowNull: true
        },
        working_hours_weekend: {
            type: Sequelize.STRING,
            allowNull: true
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf();
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    };
                }
                item.alias = item.title.trim().split('/').join('-').split(' ').join('-')
            },
            beforeUpdate: (item: any) => {
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    };
                }
                item.alias = item.title.trim().split('/').join('-').split(' ').join('-')
            },
            beforeDestroy: (item: any) => {
                RecentReading.destroy({
                    where: {
                        recruit_id: item.id,
                    },
                })
                Review.destroy({
                    where: {
                        recruit_id: item.id,
                    },
                })
                Favourite.destroy({
                    where: {
                        recruit_id: item.id,
                    },
                })
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
);
