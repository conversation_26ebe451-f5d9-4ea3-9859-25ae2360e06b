import {sequelize, Sequelize} from '../base';
import * as moment from 'moment';
import {AREA_SETTING} from "@/const";
import {SettingDistrict} from "@/models";

export const SettingProvince = sequelize.define(
    'tbl_setting_province',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        name: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        en_name: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        region: {
            type: Sequelize.STRING,
            defaultValue : AREA_SETTING.GLOBAL
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf();
            },
            beforeDestroy:async (item: any) => {
                await SettingDistrict.destroy({
                    where : {
                        setting_province_id : item.id
                    }
                })
            }
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
);
