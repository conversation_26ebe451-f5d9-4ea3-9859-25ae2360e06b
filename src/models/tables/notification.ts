import {sequelize, Sequelize} from '../base'
import moment = require('moment')
import {FCM_ACTIONS} from '@/const'

export const Notification = sequelize.define(
    'tbl_notification',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: true,
        },
        title: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        content: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        data: {
            type: Sequelize.JSON,
            allowNull: false,
        },
        interacting_user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: true,
        },
        interacting_type: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        'SEND_NOTIFICATION',
                        'INCREASE_POST_LIMIT',
                        'DECREASE_POST_LIMIT',
                        'EXPOSE_CATEGORY_TAG',
                        'HIDE_CATEGORY_TAG',
                        'COMMENT_POST', // Someone has added a comment to your Post
                        'REPLY_COMMENT_POST', // Someone has replied to your comment
                        'COMMENT_SHOP', //
                        'REPLY_COMMENT_SHOP',
                        'APPROVE_SHOP',
                        'REJECT_SHOP',
                        'PRE_EXPIRE_SHOP',
                        'PRE_EXPIRE_EVENT',
                        'EXPIRE_SHOP',
                        'EXPIRE_EVENT',
                        'EXTEND_SHOP_EXPIRE_DATE',
                        'DECREASE_SHOP_EXPIRE_DATE',
                        'TRANSFER_SHOP_OWNERSHIP',
                        'REMOVE_SHOP_OWNERSHIP',
                        'DISABLED_ACCOUNT', // 20,
                        'YOUR_EVENT_HAVE_BEEN_DELETED', // 21
                        'YOUR_POST_HAVE_BEEN_DELETED', // 22
                        'YOUR_POST_HAVE_BEEN_EDITED', // 23
                        'YOUR_EVENT_HAVE_BEEN_EDITED', // 24
                        'YOUR_ACCOUNT_HAVE_BEEN_UPGRADED', // 25
                        'YOUR_ACCOUNT_HAVE_BEEN_EDITED', // 26
                        'YOUR_ACCOUNT_HAVE_BEEN_DOWNGRADED', // 27
                        'YOUR_COMMENT_IN_SHOP_HAVE_BEEN_DELETED', // 28
                        'YOUR_COMMENT_IN_SOCIAL_HAVE_BEEN_DELETED', // 29
                        'NEW_RESERVATION', // 30
                        'REPEAT_RESERVATION',
                        'REPEAT_RESERVATION_USER',
                        'UPDATE_RESERVATION',
                        'ACCEPTED_RESERVATION',
                        'RESERVATION_NOT_AVAILABLE',
                        'CANCEL_RESERVATION',
                        'NEW_MESSAGE_NOTIFICATION',
                        'NEW_MESSAGE_10_MINUTES',
                        'NEW_MESSAGE_1_HOUR',
                        'NEW_MESSAGE_1_DAY',
                        'COMPLETE_RESERVATION',
                        'REMIND_RESERVATION',
                        'FIVE_HUNDRED_VIEWS',
                        'COMMENT_POST_DONE',
                        'CONGRATULATION_POST',
                        'CONGRATULATION_COMMENT',
                        'COMMENT_SHOP_DONE',
                        'POINTS_ARE_ADDED',
                        'BUY_POINT_PRODUCT',
                        'SENT_PRODUCT',
                        'POINT_FOR_REVIEW_SHOP',
                        'POST_DONE'
                    ],
                ],
            },
            allowNull: true,
        },
        interacting_content_id: {
            type: Sequelize.UUID,
        },
        action: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
        },
        is_view: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        employee_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_employee',
                key: 'id',
            },
        },
        image: {
            type: Sequelize.STRING,
        },
    },
    {
        hooks: {
            beforeCreate: (n: any) => {
                n.created_at_unix_timestamp = moment().valueOf()
                if (n.interacting_type === 'POINTS_ARE_ADDED' && n.interacting_type === 'POINT_FOR_REVIEW_SHOP') {
                    n.image = 'https://kormassage.s3.ap-northeast-2.amazonaws.com/point.png'
                }
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
