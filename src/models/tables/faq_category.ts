import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';
import { Faq } from './faq';

export const FaqCategory = sequelize.define(
  'tbl_faq_category', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true
    },
    name: {
      type: Sequelize.STRING,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0
      }
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false
    },
    deleted_at: { type: 'TIMESTAMP' }
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
      beforeDestroy: (item: any) => {
        Faq.destroy({
          where: {
            faq_category_id: item.id,
          }
        });
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
