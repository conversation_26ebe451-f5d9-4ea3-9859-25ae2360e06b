import { sequelize, Sequelize } from '../base'
import { POINT_PRODUCT_TYPE } from '@/const'

export const PointProduct = sequelize.define(
  'tbl_point_product',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    title: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    point: {
      type: Sequelize.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    type: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    images: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
      allowNull: false,
    },
    thumbnails: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
      allowNull: false,
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
  },
  {
    freezeTableName: true,
    underscored: true,
  }
)
