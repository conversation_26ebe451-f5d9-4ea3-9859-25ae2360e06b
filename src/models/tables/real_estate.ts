import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'


export const RealEstate = sequelize.define(
    'tbl_real_estate',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        title: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        deposit_price: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        management_fee: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        thumbnails: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        position: {
            type: Sequelize.GEOMETRY('POINT', 4326),
            defaultValue: {
                type: 'Point',
                coordinates: [0, 0],
                crs: {type: 'name', properties: {name: 'EPSG:4326'}},
            },
        },
        comment: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        soft_comment_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        land_area: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        like: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        contact_phone: {
            type: Sequelize.STRING,
            defaultValue: false,
        },
        latitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        longitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        short_address: {
            type: Sequelize.STRING,
        },
        address: {
            type: Sequelize.STRING,
        },
        address_2: {
            type: Sequelize.STRING,
        },
        short_description: {
            type: Sequelize.STRING,
        },
        geolocation_api_type: {
            type: Sequelize.STRING,
            defaultValue: 'NAVER',
            allowNull: false,
        },
        // foreign key:
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        district_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_district',
                key: 'id',
            },
            allowNull : true
        },
        province_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_province',
                key: 'id',
            },
            allowNull : true
        },
        category_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_category',
                key: 'id',
            },
        },
        station_line_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_station_line',
                key: 'id',
            },
            allowNull : true
        },
        station_subway_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_station_subway',
                key: 'id',
            },
            allowNull : true
        },
        station_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_station',
                key: 'id',
            },
            allowNull : true
        },
        call: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        alias: {
            type: Sequelize.STRING,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    }
                }
                item.alias = item.title.trim().split('/').join('-').split(' ').join('-')
            },
            beforeUpdate: (item: any) => {
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    }
                }
                if (item.title) {
                    item.alias = item.title
                        .trim()
                        .split('/')
                        .join('-')
                        .split(' ')
                        .join('-')
                }
            },
            beforeDestroy: async (item: any) => {
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {
                exclude: ['deleted_at'], // CODE remove description for API GET LIST OF SHOP in queryMiddleware-postgres.ts
            },
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
