import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'

/**
 * @swagger
 * components:
 *   schemas:
 *     CarOptionGroup:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique option group identifier
 *         name:
 *           type: string
 *           description: Option group name (e.g., Safety, Comfort, Technology)
 *         description:
 *           type: string
 *           description: Description of the option group
 *         icon:
 *           type: string
 *           description: Icon identifier or URL for the group
 *         color:
 *           type: string
 *           description: Color code for UI representation
 *         index:
 *           type: integer
 *           description: Display order
 *         status:
 *           type: boolean
 *           description: Whether the option group is active
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

export const CarOptionGroup = sequelize.define(
    'tbl_car_option_group',
    {
        // Standard fields following project patterns
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        
        // Option group specific fields
        name: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: true,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        icon: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
            beforeUpdate: (item: any) => {
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
) 