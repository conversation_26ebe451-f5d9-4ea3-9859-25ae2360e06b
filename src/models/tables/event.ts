import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'
import { SHOP_STATE } from '@/const'
import { utilService } from '@/services'
import { shortenShopListData } from '@/config/utils'

export const Event = sequelize.define(
  'tbl_event',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    // new field:
    title: {
      type: Sequelize.STRING,
    },
    images: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
    },
    start_time: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    end_time: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    state: {
      type: Sequelize.STRING,
      validate: {
        isIn: [
          [
            SHOP_STATE.PENDING,
            SHOP_STATE.APPROVED,
            SHOP_STATE.REJECTED,
            SHOP_STATE.REMOVED,
            SHOP_STATE.EXPIRED,
          ],
        ],
      },
      defaultValue: SHOP_STATE.PENDING,
    },
    description: {
      type: Sequelize.TEXT,
    },
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: true,
    },
    shop_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_shop',
        key: 'id',
      },
      allowNull: false,
    },
    created_by_admin: {
      type: Sequelize.BOOLEAN,
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf()
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
)
