import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

/**
 * @swagger
 * components:
 *   schemas:
 *     UsedCarPostTag:
 *       type: object
 *       description: Join table connecting used car posts with tags for categorization and filtering
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the used car post tag relationship
 *           example: "123e4567-e89b-12d3-a456-************"
 *         status:
 *           type: boolean
 *           description: Whether this tag relationship is active
 *           default: true
 *           example: true
 *         created_at_unix_timestamp:
 *           type: integer
 *           format: int64
 *           description: Unix timestamp when the relationship was created
 *           example: 1640995200000
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the relationship was created
 *           example: "2023-12-31T00:00:00.000Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the relationship was last updated
 *           example: "2023-12-31T00:00:00.000Z"
 *         used_car_post_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the used car post
 *           example: "123e4567-e89b-12d3-a456-************"
 *         tag_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the tag
 *           example: "123e4567-e89b-12d3-a456-************"
 *       required:
 *         - id
 *         - status
 *         - created_at
 *         - updated_at
 *         - used_car_post_id
 *         - tag_id
 */

export const UsedCarPostTag = sequelize.define(
  'tbl_used_car_post_tag',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
      comment: 'Primary key - UUID for the used car post tag relationship',
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
      comment: 'Whether this tag relationship is active',
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
      comment: 'Unix timestamp when the relationship was created',
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the relationship was created',
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the relationship was last updated',
    },
    deleted_at: { 
      type: 'TIMESTAMP',
      comment: 'Timestamp when the relationship was soft deleted',
    },
    
    // foreign key:
    used_car_post_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_used_car_post',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the used car post',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    tag_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_tag',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the tag',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
      beforeUpdate: (item: any) => {
        item.updated_at = moment().toDate();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
    indexes: [
      {
        unique: true,
        fields: ['used_car_post_id', 'tag_id'],
        name: 'unique_used_car_post_tag',
        where: {
          deleted_at: null,
        },
      },
      {
        fields: ['used_car_post_id'],
        name: 'idx_used_car_post_tag_post_id',
      },
      {
        fields: ['tag_id'],
        name: 'idx_used_car_post_tag_tag_id',
      },
    ],
    tableName: 'tbl_used_car_post_tag',
    comment: 'Join table connecting used car posts with tags for categorization and filtering',
  }
); 