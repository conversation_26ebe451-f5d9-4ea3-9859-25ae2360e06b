import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

/**
 * @swagger
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique category identifier
 *         name:
 *           type: string
 *           description: Category name
 *         alias:
 *           type: string
 *           description: URL-friendly version of the category name
 *         theme_color:
 *           type: string
 *           description: Color code for the category theme
 *         thema_id:
 *           type: string
 *           format: uuid
 *           description: ID of the parent theme
 *         index:
 *           type: integer
 *           description: Sorting position of the category
 *         status:
 *           type: boolean
 *           description: Category status (active/inactive)
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the category was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the category was last updated
 */

export const Category = sequelize.define(
  'tbl_category',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    // new field:
    name: {
      type: Sequelize.STRING,
    },
    alias: {
      type: Sequelize.STRING,
    },
    theme_color: {
      type: Sequelize.STRING,
    },
    // foreign key:
    thema_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_thema',
        key: 'id',
      },
    },
      index: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
      },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
        item.alias = item.name.trim().split('/').join('-').split(' ').join('-')
      },
      beforeUpdate: (item: any) => {
        item.alias = item.name.trim().split('/').join('-').split(' ').join('-')
      }
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
