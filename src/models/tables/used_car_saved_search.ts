import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

/**
 * @swagger
 * components:
 *   schemas:
 *     UsedCarSavedSearch:
 *       type: object
 *       description: User saved search criteria for used car posts
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the saved search
 *           example: "123e4567-e89b-12d3-a456-************"
 *         status:
 *           type: boolean
 *           description: Whether this saved search is active
 *           default: true
 *           example: true
 *         created_at_unix_timestamp:
 *           type: integer
 *           format: int64
 *           description: Unix timestamp when the search was saved
 *           example: 1640995200000
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the search was saved
 *           example: "2023-12-31T00:00:00.000Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the search was last updated
 *           example: "2023-12-31T00:00:00.000Z"
 *         name:
 *           type: string
 *           description: User-defined name for this saved search
 *           example: "Budget Family Car"
 *         user_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the user who saved this search
 *           example: "123e4567-e89b-12d3-a456-************"
 *         manufacturer_id:
 *           type: string
 *           format: uuid
 *           description: Filter by specific car manufacturer
 *           example: "123e4567-e89b-12d3-a456-************"
 *         model_id:
 *           type: string
 *           format: uuid
 *           description: Filter by specific car model
 *           example: "123e4567-e89b-12d3-a456-************"
 *         category_id:
 *           type: string
 *           format: uuid
 *           description: Filter by car category
 *           example: "123e4567-e89b-12d3-a456-************"
 *         price_min:
 *           type: number
 *           description: Minimum price filter
 *           example: 10000
 *         price_max:
 *           type: number
 *           description: Maximum price filter
 *           example: 50000
 *         year_min:
 *           type: integer
 *           description: Minimum year filter
 *           example: 2015
 *         year_max:
 *           type: integer
 *           description: Maximum year filter
 *           example: 2023
 *         mileage_min:
 *           type: integer
 *           description: Minimum mileage filter
 *           example: 0
 *         mileage_max:
 *           type: integer
 *           description: Maximum mileage filter
 *           example: 100000
 *         fuel_type:
 *           type: string
 *           enum: [gasoline, diesel, hybrid, electric, lpg]
 *           description: Filter by fuel type
 *           example: "gasoline"
 *         transmission:
 *           type: string
 *           enum: [manual, automatic, cvt]
 *           description: Filter by transmission type
 *           example: "automatic"
 *         body_type:
 *           type: string
 *           enum: [sedan, hatchback, suv, coupe, convertible, wagon, pickup, van, minivan]
 *           description: Filter by body type
 *           example: "sedan"
 *         condition:
 *           type: string
 *           enum: [excellent, good, fair, poor]
 *           description: Filter by car condition
 *           example: "good"
 *         location_radius:
 *           type: number
 *           description: Search radius in kilometers from user location
 *           example: 50
 *         location_latitude:
 *           type: number
 *           description: Center latitude for location search
 *           example: 37.7749
 *         location_longitude:
 *           type: number
 *           description: Center longitude for location search
 *           example: -122.4194
 *         keywords:
 *           type: string
 *           description: Text search keywords
 *           example: "leather seats, sunroof"
 *         is_negotiable:
 *           type: boolean
 *           description: Filter for negotiable prices only
 *           example: true
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of tag names to filter by
 *           example: ["family-car", "fuel-efficient"]
 *         notification_enabled:
 *           type: boolean
 *           description: Whether to notify user of new matches
 *           default: true
 *           example: true
 *         last_executed_at:
 *           type: string
 *           format: date-time
 *           description: When this search was last executed
 *           example: "2023-12-31T00:00:00.000Z"
 *       required:
 *         - id
 *         - status
 *         - created_at
 *         - updated_at
 *         - name
 *         - user_id
 */

export const UsedCarSavedSearch = sequelize.define(
  'tbl_used_car_saved_search',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
      comment: 'Primary key - UUID for the saved search',
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
      comment: 'Whether this saved search is active',
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
      comment: 'Unix timestamp when the search was saved',
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the search was saved',
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the search was last updated',
    },
    deleted_at: { 
      type: 'TIMESTAMP',
      comment: 'Timestamp when the search was soft deleted',
    },
    
    // search metadata:
    name: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'User-defined name for this saved search',
      validate: {
        len: [1, 100],
      },
    },
    notification_enabled: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      comment: 'Whether to notify user of new matches',
    },
    last_executed_at: {
      type: 'TIMESTAMP',
      comment: 'When this search was last executed',
    },
    
    // search criteria:
    price_min: {
      type: Sequelize.DECIMAL(15, 2),
      validate: {
        min: 0,
      },
      comment: 'Minimum price filter',
    },
    price_max: {
      type: Sequelize.DECIMAL(15, 2),
      validate: {
        min: 0,
      },
      comment: 'Maximum price filter',
    },
    year_min: {
      type: Sequelize.INTEGER,
      validate: {
        min: 1900,
        max: new Date().getFullYear() + 2,
      },
      comment: 'Minimum year filter',
    },
    year_max: {
      type: Sequelize.INTEGER,
      validate: {
        min: 1900,
        max: new Date().getFullYear() + 2,
      },
      comment: 'Maximum year filter',
    },
    mileage_min: {
      type: Sequelize.INTEGER,
      validate: {
        min: 0,
      },
      comment: 'Minimum mileage filter',
    },
    mileage_max: {
      type: Sequelize.INTEGER,
      validate: {
        min: 0,
      },
      comment: 'Maximum mileage filter',
    },
    fuel_type: {
      type: Sequelize.ENUM('gasoline', 'diesel', 'hybrid', 'electric', 'lpg'),
      comment: 'Filter by fuel type',
    },
    transmission: {
      type: Sequelize.ENUM('manual', 'automatic', 'cvt'),
      comment: 'Filter by transmission type',
    },
    body_type: {
      type: Sequelize.ENUM('sedan', 'hatchback', 'suv', 'coupe', 'convertible', 'wagon', 'pickup', 'van', 'minivan'),
      comment: 'Filter by body type',
    },
    condition: {
      type: Sequelize.ENUM('excellent', 'good', 'fair', 'poor'),
      comment: 'Filter by car condition',
    },
    is_negotiable: {
      type: Sequelize.BOOLEAN,
      comment: 'Filter for negotiable prices only',
    },
    keywords: {
      type: Sequelize.TEXT,
      comment: 'Text search keywords',
    },
    tags: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
      comment: 'Array of tag names to filter by',
    },
    
    // location criteria:
    location_radius: {
      type: Sequelize.DECIMAL(8, 2),
      validate: {
        min: 0,
      },
      comment: 'Search radius in kilometers',
    },
    location_latitude: {
      type: Sequelize.DECIMAL(10, 8),
      validate: {
        min: -90,
        max: 90,
      },
      comment: 'Center latitude for location search',
    },
    location_longitude: {
      type: Sequelize.DECIMAL(11, 8),
      validate: {
        min: -180,
        max: 180,
      },
      comment: 'Center longitude for location search',
    },
    
    // foreign key:
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the user who saved this search',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    manufacturer_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_car_manufacturer',
        key: 'id',
      },
      allowNull: true,
      comment: 'Filter by specific car manufacturer',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    model_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_car_model',
        key: 'id',
      },
      allowNull: true,
      comment: 'Filter by specific car model',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    category_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_category',
        key: 'id',
      },
      allowNull: true,
      comment: 'Filter by car category',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
      beforeUpdate: (item: any) => {
        item.updated_at = moment().toDate();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
    indexes: [
      {
        fields: ['user_id'],
        name: 'idx_used_car_saved_search_user_id',
      },
      {
        fields: ['manufacturer_id'],
        name: 'idx_used_car_saved_search_manufacturer_id',
      },
      {
        fields: ['model_id'],
        name: 'idx_used_car_saved_search_model_id',
      },
      {
        fields: ['category_id'],
        name: 'idx_used_car_saved_search_category_id',
      },
      {
        fields: ['notification_enabled'],
        name: 'idx_used_car_saved_search_notification_enabled',
      },
      {
        fields: ['last_executed_at'],
        name: 'idx_used_car_saved_search_last_executed_at',
      },
    ],
    tableName: 'tbl_used_car_saved_search',
    comment: 'User saved search criteria for used car posts',
  }
); 