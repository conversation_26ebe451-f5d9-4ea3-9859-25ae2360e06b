import { sequelize, Sequelize } from "../base";

export const ExpRule = sequelize.define(
  "tbl_exp_rule",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    action_type: {
      type: Sequelize.STRING,
      allowNull: false,
      unique: true,
      comment: 'Action type, e.g., write_comment, receive_like, etc.',
    },
    exp: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Amount of experience points rewarded for the action',
    },
    point: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Amount of reward points granted for the action',
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
      comment: 'Creation timestamp',
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
      comment: 'Last update timestamp',
    },
    deleted_at: { type: Sequelize.DATE, },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
