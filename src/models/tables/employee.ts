import {
    sequelize,
    Sequelize
  } from '../base'
  
  export const Employee = sequelize.define(
    'tbl_employee',
    {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV1,
        primaryKey: true
      },
      fullname: {
        type: Sequelize.STRING,
        allowNull: false
      },
      avatar: {
        type: Sequelize.STRING,
        allowNull: true
      },
      phone: {
        type: Sequelize.STRING,
        allowNull: false
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false
      },
      username: {
        type: Sequelize.STRING,
        allowNull: false
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM,
        values: ['OPERATOR', 'ADMIN', 'SUPERADMIN'],
        defaultValue: 'OPERATOR',
        allowNull: false
      },
      status: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      created_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false
      },
      updated_at: {
        type: 'TIMESTAMP',
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false
      },
      deleted_at: { type: 'TIMESTAMP' }
    },
    {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      paranoid: true,
      defaultScope: {
        attributes: { exclude: ['deleted_at'] }
      },
      scopes: {
        deleted: {
          where: { deleted_at: { $ne: null } },
          paranoid: false
        }
      }
    }
  )
  