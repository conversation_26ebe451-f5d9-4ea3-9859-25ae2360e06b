import { sequelize, Sequelize } from "../base";
export const Ranking = sequelize.define(
  "tbl_ranking",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    period: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'Ranking period (e.g., daily, weekly, monthly)',
    },
    period_start: {
      type: Sequelize.DATE,
      allowNull: false,
      comment: 'Start date of the period (e.g., 2025-05-01)',
    },
    metric: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'Ranking metric (e.g., exp, point, posts)',
    },
    user_id: {
      type: Sequelize.UUID,
      allowNull: false,
      comment: 'User who is ranked',
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    score: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Score used for ranking',
    },
    rank: {
      type: Sequelize.INTEGER,
      allowNull: false,
      comment: 'Position in the ranking (1 = top)',
    },
    // WRITE_POST_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // WRITE_COMMENT_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // WRITE_REVIEW_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // REGISTER_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // DAILY_ATTENDANCE_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // POST_LIKED_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // INVITE_USER_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    // REPORT_SPAM_score: { type: Sequelize.INTEGER, defaultValue: 0 },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    deleted_at: { type: Sequelize.DATE },
  } ,
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        name: 'uq_ranking_period_metric_user',
        fields: ['period', 'metric', 'user_id'],
      },
    ],
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
