import { sequelize, Sequelize } from '../base'

export const PointProductHistory = sequelize.define(
  'tbl_point_product_history',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    user_name: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    user_point: {
      type: Sequelize.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    user_phone: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    product: {
      type: Sequelize.JSONB,
      allowNull: false,
    },
    pending: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    sent: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
  },
  {
    freezeTableName: true,
    underscored: true,
  }
)
