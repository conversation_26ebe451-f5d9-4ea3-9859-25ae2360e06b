import { sequelize, Sequelize } from '../base'

export const JobRequest = sequelize.define(
  'tbl_job_request',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    title: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    category: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    budget: {
      type: Sequelize.INTEGER,
    },
    region: { type: Sequelize.STRING },
    note: { type: Sequelize.STRING },
    address: { type: Sequelize.STRING },
    address_detail: { type: Sequelize.STRING },
    service_name: { type: Sequelize.STRING },
    space_type: { type: Sequelize.STRING },
    area_size: { type: Sequelize.STRING },
    window_work_type: { type: Sequelize.STRING },
    window_quantity: { type: Sequelize.STRING },
    required_work: { type: Sequelize.STRING },
    installation_space: { type: Sequelize.STRING },
    light_quantity: { type: Sequelize.STRING },
    light_type: { type: Sequelize.STRING },
    electrical_wiring_needed: { type: Sequelize.STRING },
    residential_type: { type: Sequelize.STRING },
    room_count: { type: Sequelize.STRING },
    bathroom_count: { type: Sequelize.STRING },
    balcony_count: { type: Sequelize.STRING },
    additional_services: { type: Sequelize.STRING },
    pickup_address: { type: Sequelize.STRING },
    pickup_address_detail: { type: Sequelize.STRING },
    delivery_address: { type: Sequelize.STRING },
    delivery_address_detail: { type: Sequelize.STRING },
    preferred_start_date: { type: Sequelize.DATEONLY },
    preferred_end_date: { type: Sequelize.DATEONLY },
    image_url: { type: Sequelize.ARRAY(Sequelize.STRING) },
    response_type: {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: false,
      defaultValue: ['quote'],
    },
    is_favorite: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    created_at: {
      type: 'TIMESTAMP',
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
      type: 'TIMESTAMP',
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    deleted_at: {
      type: 'TIMESTAMP',
    },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: {
        exclude: ['deleted_at'],
      },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
)
