import {sequelize, Sequelize} from '../base';
import * as moment from 'moment';
import {Faq} from './faq';
import {Keyword} from "@/models";

export const KeywordCategory = sequelize.define(
    'tbl_keyword_category', {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true
        },
        name: {
            type: Sequelize.STRING,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0
            }
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false
        },
        deleted_at: {type: 'TIMESTAMP'},
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        keyword_type_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_keyword_type',
                key: 'id'
            },
        },
        province_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_province',
                key: 'id',
            },
            allowNull : true
        },
        district_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_setting_district',
                key: 'id',
            },
            allowNull : true
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf();
            },
            beforeDestroy: (item: any) => {
                Keyword.destroy({
                    where: {
                        keyword_category_id: item.id,
                    }
                });
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
);
