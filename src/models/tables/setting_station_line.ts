import {sequelize, Sequelize} from '../base';
import * as moment from 'moment';
import {SettingStationSubway} from "@/models";

export const SettingStationLine = sequelize.define(
    'tbl_setting_station_line',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        name: {
            type: Sequelize.STRING,
        },
        en_name: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
        },
        color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        setting_station_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_setting_station',
                key: 'id',
            },
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf();
            },
            beforeDestroy : async (item: any) => {
                await SettingStationSubway.destroy({
                    where : {
                        setting_station_line_id : item.id
                    }
                })
            }
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
);
