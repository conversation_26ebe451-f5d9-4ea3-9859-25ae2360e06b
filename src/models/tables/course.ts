import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'

export const Course = sequelize.define(
  'tbl_course',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    images: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
      defaultValue: [],
    },
    thumbnails: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
      defaultValue: [],
    },
    title: {
      type: Sequelize.STRING,
      defaultValue: '',
    },
    running_time: {
      type: Sequelize.STRING,
      defaultValue: '',
    },
    description: {
      type: Sequelize.TEXT,
      defaultValue: '',
    },
    recommended: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    unit: {
      type: Sequelize.STRING,
      defaultValue: '',
    },
    shop_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_shop',
        key: 'id',
      },
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },
    order: {
      type: Sequelize.INTEGER,
    },
    limit_in_one_day: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf()
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
)
