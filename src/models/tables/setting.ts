import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

export const Setting = sequelize.define(
  'tbl_setting',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    content: {
          type: Sequelize.TEXT,
          allowNull: true,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    value_array: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
      defaultValue: [],
    },
    value_array_obj: {
      type: Sequelize.ARRAY({ type: Sequelize.JSONB }),
      defaultValue: [],
    },
    value_obj: {
          type: Sequelize.JSONB,
          allowNull: true,
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    // new field:
    field: {
      type: Sequelize.STRING,
    },
    value: {
      type: Sequelize.TEXT,
    },
    // foreign key:
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
