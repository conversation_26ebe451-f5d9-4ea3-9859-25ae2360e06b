import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'

/**
 * @swagger
 * components:
 *   schemas:
 *     UsedCarPostOption:
 *       type: object
 *       required:
 *         - used_car_post_id
 *         - car_option_id
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the post-option relationship
 *         used_car_post_id:
 *           type: string
 *           format: uuid
 *           description: ID of the used car post
 *         car_option_id:
 *           type: string
 *           format: uuid
 *           description: ID of the car option
 *         is_verified:
 *           type: boolean
 *           description: Whether this option has been verified for the car
 *         condition:
 *           type: string
 *           enum: [excellent, good, fair, poor, not_working]
 *           description: Condition of this specific option
 *         notes:
 *           type: string
 *           description: Additional notes about this option
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

export const UsedCarPostOption = sequelize.define(
    'tbl_used_car_post_option',
    {
        // Standard fields following project patterns
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        
        // Join table specific fields
        is_verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        condition: {
            type: Sequelize.ENUM('excellent', 'good', 'fair', 'poor', 'not_working'),
            allowNull: true,
        },
        notes: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        
        // Foreign key relationships
        used_car_post_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_used_car_post',
                key: 'id',
            },
            onDelete: 'CASCADE',
        },
        car_option_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_option',
                key: 'id',
            },
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
        indexes: [
            {
                unique: true,
                fields: ['used_car_post_id', 'car_option_id'],
                name: 'unique_post_option'
            }
        ]
    }
) 