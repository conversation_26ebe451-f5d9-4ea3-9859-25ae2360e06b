import {Sequelize, sequelize} from '../base'

export const PointHistory = sequelize.define(
    'tbl_point_history',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: false,
        },
        action: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        di_object: {
            type: Sequelize.JSONB,
            allowNull: true,
        },
        point: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        current_user_point: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
    },
    {
        underscored: true,
        freezeTableName: true,
    }
)
