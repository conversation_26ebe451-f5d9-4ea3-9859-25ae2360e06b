import { sequelize, Sequelize } from "../base";

export const Level = sequelize.define(
  "tbl_level",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      allowNull: false,
      primaryKey: true,
    },
    name: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    icon_url: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    exp_min: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    exp_max: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    how_to_obtain: {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
    },
    exp_payment: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    point_award: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    bbs_access_right:  {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("NOW()"),
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("NOW()"),
    },
    deleted_at: { type: "TIMESTAMP" },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
