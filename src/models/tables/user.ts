import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import {LOGIN_TYPE, PLATFORM} from '@/const'
import {USER_TYPE} from '../../const'
import {
    Contact,
    DislikePost,
    Event,
    Favourite,
    FavouritePost,
    History,
    Notification,
    Post,
    PushNotification,
    RecentReading,
    Recruit,
    Report,
    Reservation,
    Shop,
    Block,
    FavouriteShortVideo,
    Question,
    AnswerQuestion,
    Conversation,
    Message, SecondHandMarket, RealEstate,
} from '.'

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - username
 *         - password
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique user identifier
 *         username:
 *           type: string
 *           description: User's username
 *         password:
 *           type: string
 *           description: User's password (hashed)
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         nickname:
 *           type: string
 *           description: User's nickname
 *         company_name:
 *           type: string
 *           description: User's company name (for business accounts)
 *         phone:
 *           type: string
 *           description: User's phone number
 *         avatar:
 *           type: string
 *           description: URL to user's avatar image
 *         cover_avatar:
 *           type: string
 *           description: URL to user's cover image
 *         login_type:
 *           type: string
 *           enum: [facebook, google, in_app, kakaotalk, naver, apple]
 *           description: Method used for login
 *         account_type:
 *           type: string
 *           enum: [biz_user, free_user, paid_user, admin]
 *           description: Type of user account
 *         latitude:
 *           type: number
 *           format: double
 *           description: User's latitude coordinate
 *         longitude:
 *           type: number
 *           format: double
 *           description: User's longitude coordinate
 *         verified:
 *           type: boolean
 *           description: Whether the user's account is verified
 *         point:
 *           type: integer
 *           description: User's reward points
 *         level:
 *           type: integer
 *           description: User's level in the system
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the user account was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the user account was last updated
 */

export const User = sequelize.define(
    'tbl_user',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        username: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        password: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        email: {
            type: Sequelize.STRING,
            validate: {
                isEmail: true,
            },
        },
        nickname: {
            type: Sequelize.STRING,
        },
        company_name: {
            type: Sequelize.STRING,
        },
        phone: {
            type: Sequelize.STRING,
        },
        avatar: {
            type: Sequelize.STRING,
        },
        cover_avatar: {
            type: Sequelize.STRING,
        },
        login_type: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        LOGIN_TYPE.FACEBOOK,
                        LOGIN_TYPE.GOOGLE,
                        LOGIN_TYPE.IN_APP,
                        LOGIN_TYPE.KAKAO,
                        LOGIN_TYPE.NAVER,
                        LOGIN_TYPE.APPLE,
                    ],
                ],
            },
            defaultValue: LOGIN_TYPE.IN_APP,
        },
        account_type: {
            type: Sequelize.STRING,
            validate: {
                isIn: [
                    [
                        USER_TYPE.BIZ_USER,
                        USER_TYPE.FREE_USER,
                        USER_TYPE.PAID_USER,
                        USER_TYPE.ADMIN,
                    ],
                ],
            },
        },
        latitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        longitude: {
            type: Sequelize.DOUBLE,
            defaultValue: 0,
        },
        verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        show_shop_tag: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
        },
        post_limit: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        current_active_post: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        current_pending_post: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        current_expired_post: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        current_rejected_post: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        current_recommendation_post: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        event_type: {
            type: Sequelize.STRING,
            validate: {
                isIn: [['A', 'B', 'C']],
            },
            defaultValue: 'B',
        },
        // current_active_event: {
        //   type: Sequelize.INTEGER,
        //   defaultValue: 0,
        // },
        post_start_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        post_expired_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        paid_user_expiration_date: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        post_period: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        memo: {
            type: Sequelize.TEXT,
        },
        exp: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        level: {
            type: Sequelize.INTEGER,
            validate: {
                min: 1,
            },
            defaultValue: 1,
        },
        level_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_exp_level',
                key: 'id',
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL',
        },
        expert_info_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
            model: 'tbl_expert_info',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        },
        ranking: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        group: {
            type: Sequelize.STRING,
        },
        groups: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
        },
        depositor: {
            type: Sequelize.STRING,
        },
        contact: {
            type: Sequelize.STRING,
        },
        deposit_date: {
            type: Sequelize.STRING,
        },
        deposit_amount: {
            type: Sequelize.STRING,
        },
        exposure_bulletin_board: {
            type: Sequelize.TEXT,
        },
        start_date: {
            type: Sequelize.STRING,
        },
        end_date: {
            type: Sequelize.STRING,
        },
        uniqueness: {
            type: Sequelize.TEXT,
        },
        attachments: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        daily_ranking_delta: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        noti_sound: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
        },
        language: {
            type: Sequelize.STRING,
            defaultValue: 'ko',
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        image_id_card: {
            type: Sequelize.STRING,
        },
        approve: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        jump_limit: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        notice_messenger_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        point: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
            allowNull: false,
            validate: {
                min: 0,
            },
        },
        sign_in_time_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        group_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_group',
                key: 'id',
            },
        },
        platform_create: {
            type: Sequelize.STRING,
            allowNull: true,
            validate: {
                isIn: [
                    [
                        PLATFORM.BROWSER,
                        PLATFORM.APPLE,
                        PLATFORM.ANDROID,
                        PLATFORM.BROWSER_MOBILE,
                    ],
                ],
            },
            defaultValue: PLATFORM.BROWSER,
        },
        birthday: {
            type: Sequelize.DATEONLY,
            allowNull: true
        },
        auto_approve_reservation: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        secret_answer: {
            type: Sequelize.STRING,
            allowNull: true
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        reservation_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
            },
            beforeDestroy: (item: any) => {
                Contact.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                DislikePost.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Event.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                FavouritePost.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Favourite.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                History.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Notification.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Post.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                PushNotification.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                RecentReading.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Recruit.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Report.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Reservation.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Reservation.destroy({
                    where: {
                        seller_id: item.id,
                    },
                })
                Shop.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Block.destroy({
                    where: {
                        $or: [{user_id: item.id}, {user_id_blocked: item.id}],
                    },
                })
                FavouriteShortVideo.destroy({
                    where: {
                        user_id: item.id,
                    },
                })
                Question.destroy({where: {user_id: item.id}})
                AnswerQuestion.destroy({where: {user_id: item.id}})
                Message.destroy({where: {user_id: item.id}, force: true})
                Conversation.destroy({where: {user_id: item.id}, force: true})
                RealEstate.destroy({where: {user_id: item.id}, force: true})
                SecondHandMarket.destroy({where: {user_id: item.id}, force: true})
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
