import { sequelize, Sequelize } from '../base'

export const ThemaFeedbackItem = sequelize.define(
  'tbl_thema_feedback_item',
  {
    thema_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tbl_thema',
        key: 'id',
      },
    },
    feedback_item_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tbl_feedback_item',
        key: 'id',
      },
    },
  },
  {
    timestamps: false,
    underscored: true,
    freezeTableName: true,
  }
)
