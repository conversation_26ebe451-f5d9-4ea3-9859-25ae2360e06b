import { sequelize, Sequelize } from "../base";
import * as moment from "moment";

export const Video = sequelize.define(
  "tbl_video",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.literal("uuid_generate_v4()"),
      primaryKey: true,
    },
    shop_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: "tbl_shop",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    url: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    thumb: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    duration: {
      type: Sequelize.FLOAT,
      allowNull: false,
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("NOW()"),
    },
    updated_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal("NOW()"),
    },
    deleted_at: {
        type: Sequelize.DATE, 
        allowNull: true,
      },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
