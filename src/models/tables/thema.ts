import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import {BOARD, USER_PERMISSION} from '@/const'
import {Link} from "@/models";

export const Thema = sequelize.define(
    'tbl_thema',
    {
        // default field:
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        name: {
            type: Sequelize.STRING,
        },
        alias: {
            type: Sequelize.STRING,
        },
        visible_boards: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        ids_shop_banner: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
        },
        bonus_point: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        review_require: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        mentor_status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        is_for_adults: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        is_post_moderation: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        view_user_permissions: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
            allowNull: true,
        },
        post_user_permissions: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
            allowNull: true,
        },
        comment_user_permissions: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
            allowNull: true,
        },
        start_time: {
            type: Sequelize.TIME,
            allowNull: true
        },
        end_time: {
            type: Sequelize.TIME,
            allowNull: true
        },
        geolocation_api_type: {
            type: Sequelize.STRING,
            defaultValue: 'NAVER',
        },
        map_marker_cluster: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        description: {
            type: Sequelize.TEXT,
        },
        image: {
            type: Sequelize.STRING,
            allowNull: true
        },
        keywords: {
            type: Sequelize.TEXT,
        },
        content_heading: {
            type: Sequelize.TEXT,
        },
        // foreign key:
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                item.alias = item.name.trim().split('/').join('-').split(' ').join('-')
            },
            beforeUpdate: (item: any) => {
                item.alias = item.name.trim().split('/').join('-').split(' ').join('-')
            },
            beforeDestroy : async (item:any)=> {
              await Link.destroy({
                    where : {
                        thema_id : item.id
                    }
                })
            }
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
