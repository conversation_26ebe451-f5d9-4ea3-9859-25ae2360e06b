import {sequelize, Sequelize} from '../base'

export const Conversation = sequelize.define(
    'tbl_conversation',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: true,
        },
        shop_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_shop',
                key: 'id',
            },
            allowNull: true,
        },
        last_message_id: {
            type: Sequelize.UUIDV1,
            references: {
                model: 'tbl_message',
                key: 'id',
            },
            onDelete: 'SET NULL',
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        hide_user_id: {
            type: Sequelize.UUID,
            allowNull: true,
        },
        hide_shop_id: {
            type: Sequelize.UUID,
            allowNull: true,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        report: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        user_id_2: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: true,
        },
        shop_id_2: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_shop',
                key: 'id',
            },
            allowNull: true,
        },
        hide_user_id_2: {
            type: Sequelize.UUID,
            allowNull: true,
        },
        hide_shop_id_2: {
            type: Sequelize.UUID,
            allowNull: true,
        },
    },
    {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
    }
)
