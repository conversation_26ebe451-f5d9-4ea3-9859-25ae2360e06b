import {sequelize, Sequelize} from '../base';
import * as moment from 'moment';
import {AREA_SETTING} from "@/const";
import {SettingStationLine} from "@/models";

export const SettingStation = sequelize.define(
    'tbl_setting_station',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        name: {
            type: Sequelize.STRING,
        },
        en_name: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
        },
        region: {
            type: Sequelize.STRING,
            defaultValue : AREA_SETTING.GLOBAL
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf();
            },
            beforeDestroy : async (item: any) => {
                await SettingStationLine.destroy({
                    where : {
                        setting_station_id : item.id
                    }
                })
            }
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
);
