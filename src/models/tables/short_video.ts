import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'
import { FavouriteShortVideo } from '@/models/tables'
import { VIDEO_FORMAT } from '@/const'
const _ = require('lodash')

export const ShortVideo = sequelize.define(
  'tbl_short_video',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    title: {
      type: Sequelize.STRING,
    },
    thumbnail: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    video: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    resolution: {
      type: Sequelize.STRING,
      allowNull: false,
      validate: {
        isIn: [[VIDEO_FORMAT.HORIZONTAL, VIDEO_FORMAT.VERTICAL]],
      },
    },
    index: {
      type: Sequelize.FLOAT,
      validate: {
        min: 0,
        isFloat: true,
      },
    },
    like: {
      type: Sequelize.INTEGER,
      validate: {
        min: 0,
      },
      defaultValue: 0,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: {type: 'TIMESTAMP'},
  },
  {
    indexes: [
      {
        unique: true,
        fields: ['index'],
      },
    ],
    hooks: {
      beforeCreate: async (item: any) => {
        const index = await ShortVideo.findOne({
          order: [[sequelize.fn('max', sequelize.col('index')), 'DESC']],
          group: ['tbl_short_video.id'],
          paranoid: false,
        })

        if (index) {
          item.index = index.index + 1
        } else {
          item.index = 1
        }
        item.created_at_unix_timestamp = moment().valueOf()
      },
      beforeDestroy: (item: any) => {
        FavouriteShortVideo.destroy({where: {short_video_id: item.id}})
      },
    },
    timestamps: true,
    paranoid: true,
    underscored: true,
    freezeTableName: true,
    defaultScope: {
      attributes: {exclude: ['deleted_at']},
    },
    scopes: {
      delete: {
        where: {delete_at: {$ne: null}},
        paranoid: false,
      },
    },
  }
)
