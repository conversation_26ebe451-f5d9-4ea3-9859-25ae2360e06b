import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

/**
 * @swagger
 * components:
 *   schemas:
 *     Comment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique comment identifier
 *         content:
 *           type: string
 *           description: Comment text content
 *         parent_id:
 *           type: string
 *           format: uuid
 *           description: ID of the parent comment (for replies)
 *         parent_user_id:
 *           type: string
 *           description: ID of the user who created the parent comment
 *         image:
 *           type: string
 *           description: URL to an image attached to the comment
 *         thumbnail:
 *           type: string
 *           description: URL to a thumbnail of the attached image
 *         user_id:
 *           type: string
 *           format: uuid
 *           description: ID of the user who created this comment
 *         post_id:
 *           type: string
 *           format: uuid
 *           description: ID of the post this comment belongs to
 *         status:
 *           type: boolean
 *           description: Comment status (active/inactive)
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the comment was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the comment was last updated
 */

export const Comment = sequelize.define(
  'tbl_comment',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    // new field:
    content: {
      type: Sequelize.STRING,
    },
    parent_id: {
      type: Sequelize.UUID,
    },
    parent_user_id: {
      type: Sequelize.STRING,
    },
    image: {
      type: Sequelize.STRING,
    },
    thumbnail: {
      type: Sequelize.STRING,
    },
    // foreign key:
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
    },
    post_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_post',
        key: 'id',
      },
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
