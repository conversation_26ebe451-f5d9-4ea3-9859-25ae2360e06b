import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'

/**
 * @swagger
 * components:
 *   schemas:
 *     CarOption:
 *       type: object
 *       required:
 *         - name
 *         - option_group_id
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique option identifier
 *         name:
 *           type: string
 *           description: Option name (e.g., ABS, Air Conditioning, Navigation)
 *         description:
 *           type: string
 *           description: Detailed description of the option
 *         option_group_id:
 *           type: string
 *           format: uuid
 *           description: ID of the option group this belongs to
 *         icon:
 *           type: string
 *           description: Icon identifier or URL for the option
 *         is_premium:
 *           type: boolean
 *           description: Whether this is considered a premium option
 *         index:
 *           type: integer
 *           description: Display order within the group
 *         status:
 *           type: boolean
 *           description: Whether the option is active
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

export const CarOption = sequelize.define(
    'tbl_car_option',
    {
        // Standard fields following project patterns
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        
        // Option specific fields
        name: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        icon: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        is_premium: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        
        // Foreign key relationships
        option_group_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_option_group',
                key: 'id',
            },
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
            beforeUpdate: (item: any) => {
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
        indexes: [
            {
                unique: true,
                fields: ['option_group_id', 'name'],
                name: 'unique_group_option'
            }
        ]
    }
) 