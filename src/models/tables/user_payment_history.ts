import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'

export const EditUserHistory = sequelize.define(
  'tbl_user_payment_history',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: false,
    },
    content: {
      type: Sequelize.TEXT,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: {type: 'TIMESTAMP'},
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf()
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: {exclude: ['deleted_at']},
    },
    scopes: {
      deleted: {
        where: {deleted_at: {$ne: null}},
        paranoid: false,
      },
    },
  }
)
