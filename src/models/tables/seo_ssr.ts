import {sequelize, Sequelize} from '../base'

export const SeoSsr = sequelize.define(
    'tbl_seo_ssr',
    {
        id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        title: {
            type: Sequelize.STRING,
        },
        description: {
            type: Sequelize.TEXT,
        },
        keywords: {
            type: Sequelize.TEXT,
        },
        site_name: {
            type: Sequelize.STRING,
            allowNull: true
        },
        icon: {
            type: Sequelize.STRING,
        },
        avatar: {
            type: Sequelize.STRING,
        },
        meta: {
            type: Sequelize.TEXT,
        },
        meta_naver: {
            type: Sequelize.TEXT,
        },
        google_ads: {
            type: Sequelize.TEXT,
            allowNull : true
        },
        author: {
            type: Sequelize.TEXT,
            allowNull : true
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
    },
    {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
    }
)
