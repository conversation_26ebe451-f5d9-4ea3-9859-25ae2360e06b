import { sequelize, Sequelize } from "../base";

export const RankingDetail = sequelize.define(
  "tbl_ranking_detail",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    ranking_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: "tbl_ranking",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    action_type: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    score: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("NOW()"),
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("NOW()"),
    },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  }
);
