import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import {utilService} from '@/services'
import {shortenShopListData} from '@/config/utils'

export const Report = sequelize.define(
    'tbl_report',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        //
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: true,
        },
        shop_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_shop',
                key: 'id',
            },
            allowNull: true,
        },
        post_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_post',
                key: 'id',
            },
            allowNull: true,
        },
        review_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_review',
                key: 'id',
            },
            allowNull: true,
        },
        chat_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_conversation',
                key: 'id',
            },
            allowNull: true,
        },
        //
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        is_solved: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
        message: {
            type: Sequelize.STRING,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
