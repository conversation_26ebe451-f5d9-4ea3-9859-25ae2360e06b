import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';
import { utilService } from '@/services';
import { shortenShopListData } from '@/config/utils';

export const History = sequelize.define(
  'tbl_history',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    category: {
      type: Sequelize.STRING,
      allowNull: false
    },
    type_1: {
      type: Sequelize.STRING,
    },
    type_2: {
      type: Sequelize.STRING,
    },
    type_3: {
      type: Sequelize.STRING,
    },
    note: {
      type: Sequelize.TEXT,
    },
    value_1: {
      type: Sequelize.INTEGER,
      defaultValue: 0,
    },
    //
    shop_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_shop',
        key: 'id',
      },
      allowNull: true
    },
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: true
    },
    post_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_post',
        key: 'id',
      },
      allowNull: true
    },
    review_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_review',
        key: 'id',
      },
      allowNull: true
    },
    //
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
