import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

export const LinkCategory = sequelize.define(
  'tbl_link_category',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    // new field:

    // key
    link_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_link',
        key: 'id',
      },
    },
    category_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_category',
        key: 'id',
      },
    },
    index: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
      },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
