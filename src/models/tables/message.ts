import {sequelize, Sequelize} from '../base'
import {socketService} from '@/services/socketService'
import {Conversation} from '@/models'

export const Message = sequelize.define(
    'tbl_message',
    {
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        conversation_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_conversation',
                key: 'id',
            },
            onDelete: 'cascade',
        },
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
            allowNull: true,
        },
        shop_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_shop',
                key: 'id',
            },
            allowNull: true,
        },
        recruit_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_recruit',
                key: 'id',
            },
            allowNull: true,
        },
        content: {
            type: Sequelize.TEXT,
        },
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        thumbs: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        is_call: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        duration: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            allowNull: true,
        },
        call_status: {
            type: Sequelize.BOOLEAN,
            allowNull: true,
        },
        hide_user_id: {
            type: Sequelize.UUID,
            allowNull: true,
        },
        hide_shop_id: {
            type: Sequelize.UUID,
            allowNull: true,
        },
        _10_minutes_unread: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        _1_hour_unread: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        _1_day_unread: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        hide_user_id_2: {
            type: Sequelize.UUID,
            allowNull: true,
        },
        hide_shop_id_2: {
            type: Sequelize.UUID,
            allowNull: true,
        },
    },
    {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
    }
)
