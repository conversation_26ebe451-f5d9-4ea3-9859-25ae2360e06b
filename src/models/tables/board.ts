import { sequelize, Sequelize } from "../base";

export const Board = sequelize.define(
  "tbl_board",
  {
    id: {
        type: Sequelize.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV1,
        comment: 'Primary key - UUID of the board',
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Display name of the board',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('NOW()'),
        comment: 'Timestamp when the board was created',
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('NOW()'),
        comment: 'Timestamp of last update to the board',
      },
    deleted_at: { type: "TIMESTAMP" },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
