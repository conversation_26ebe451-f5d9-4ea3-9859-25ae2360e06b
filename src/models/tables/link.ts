import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import {BOARD} from '@/const'
import {LinkCategory} from "@/models";

export const Link = sequelize.define(
    'tbl_link',
    {
        // default field:
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        name: {
            type: Sequelize.STRING,
        },
        image: {
            type: Sequelize.STRING,
        },
        accessible_user_type: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
        },
        route: {
            type: Sequelize.STRING,
            defaultValue: BOARD.EVENT_BOARD,
            validate: {
                isIn: [
                    [
                        BOARD.BULLETIN_BOARD,
                        BOARD.BULLETIN_BOARD_2,
                        BOARD.DISTANCE_ORDER_BOARD,
                        BOARD.EVENT_BOARD,
                        BOARD.STORE_PROFILE,
                        BOARD.DISTANCE_ORDER_BOARD_2,
                        BOARD.PROFILE_DESIGN1,
                        BOARD.RECRUIT_BOARD,
                        BOARD.RECRUIT_BOARD_2,
                        BOARD.SHOP_SALES_BOARD,
                        BOARD.JUMP_UP_SHOP_LIST_BOARD,
                        BOARD.BLOG,
                        BOARD.BLOG_2,
                        BOARD.SITE,
                        BOARD.REAL_ESTATE,
                        BOARD.SECOND_HAND_MARKET,
                        BOARD.DISTANCE_ORDER_BOARD_3,
                        BOARD.FIND_TECH_BOARD
                    ],
                ],
            },
        },
        geolocation_api_type: {
            type: Sequelize.STRING,
            defaultValue: 'NAVER',
        },
        // key
        thema_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_thema',
                key: 'id',
            },
        },
        keywords: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        themas: {
            type: Sequelize.ARRAY({type: Sequelize.UUID}),
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
            },
            beforeDestroy: async (item: any) => {
                await LinkCategory.destroy({
                    where: {
                        link_id: item.id
                    }
                })
            }
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
