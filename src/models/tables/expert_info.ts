import { sequelize, Sequelize } from '../base';

export const ExpertInfo = sequelize.define(
  'tbl_expert_info',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },

    affiliation_classification: {
      type: Sequelize.STRING,
    },
    representative_service: {
      type: Sequelize.STRING,
    },
    region: {
      type: Sequelize.STRING,
    },
    business_card_image_urls: {
      type: Sequelize.ARRAY(Sequelize.STRING),
    },
    brand_name: {
      type: Sequelize.STRING,
    },
    brief_introduction: {
      type: Sequelize.TEXT,
    },
    personal_history: {
      type: Sequelize.TEXT,
    },
    portfolio_image_urls: {
      type: Sequelize.ARRAY(Sequelize.STRING),
    },
    education: {
      type: Sequelize.TEXT,
    },
    sns_urls: {
      type: Sequelize.ARRAY(Sequelize.STRING),
    },
    qualifications: {
      type: Sequelize.ARRAY(Sequelize.STRING),
    },
    email: {
      type: Sequelize.STRING,
    },
    my_tags: {
      type: Sequelize.ARRAY(Sequelize.STRING),
    },
    created_at: {
      type: 'TIMESTAMP',
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
      type: 'TIMESTAMP',
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    deleted_at: {
      type: 'TIMESTAMP',
    },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
