import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'

export const Reservation = sequelize.define(
	'tbl_reservation',
	{
		id: {
			type: Sequelize.UUID,
			defaultValue: Sequelize.UUIDV1,
			primaryKey: true,
		},
		state: {
			type: Sequelize.STRING,
			allowNull: false,
		},
		date: {
			type: Sequelize.BIGINT,
			validate: {
				min: 0,
			},
			allowNull: false,
		},
		paymentMethod: {
			type: Sequelize.STRING,
			allowNull: false,
		},
		contact: {
			type: Sequelize.STRING,
		},
		memo: {
			type: Sequelize.STRING,
		},
		reason: {
			type: Sequelize.STRING,
		},
		prices: {
			type: Sequelize.ARRAY({
				type: Sequelize.JSONB,
			}),
		},
		buyer_status: {
			type: Sequelize.BOOLEAN,
			defaultValue: true,
			allowNull: false,
		},
		seller_status: {
			type: Sequelize.BOOLEAN,
			defaultValue: true,
			allowNull: false,
		},
		shop_id: {
			type: Sequelize.UUID,
			references: {
				model: 'tbl_shop',
				key: 'id',
			},
		},
		user_id: {
			type: Sequelize.UUID,
			references: {
				model: 'tbl_user',
				key: 'id',
			},
		},
		seller_id: {
			type: Sequelize.UUID,
			references: {
				model: 'tbl_user',
				key: 'id',
			},
		},
		status: {
			type: Sequelize.BOOLEAN,
			defaultValue: true,
			allowNull: false,
		},
		count_service: {
			type: Sequelize.INTEGER,
			allowNull: false,
			validate: {
				min: 0,
			},
		},
		feedback_status: {
			type: Sequelize.BOOLEAN,
			allowNull: true,
			defaultValue: false,
		},
		created_at_unix_timestamp: {
			type: Sequelize.BIGINT,
			validate: {
				min: 0,
			},
		},
		created_at: {
			type: 'TIMESTAMP',
			defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
			allowNull: false,
		},
		updated_at: {
			type: 'TIMESTAMP',
			defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
			allowNull: false,
		},
		deleted_at: { type: 'TIMESTAMP' },
		paymentInfo: {
			type: Sequelize.STRING,
			allowNull: true,
		},
		title: {
			type: Sequelize.STRING,
			allowNull: true,
		},
	},
	{
		hooks: {
			beforeCreate: (item: any) => {
				item.created_at_unix_timestamp = moment().valueOf()
			},
		},
		timestamps: true,
		underscored: true,
		freezeTableName: true,
		paranoid: true,
		defaultScope: {
			attributes: { exclude: ['deleted_at'] },
		},
		scopes: {
			deleted: {
				where: { deleted_at: { $ne: null } },
				paranoid: false,
			},
		},
	}
)
