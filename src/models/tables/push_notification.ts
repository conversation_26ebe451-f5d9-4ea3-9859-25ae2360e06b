import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'

export const PushNotification = sequelize.define(
  'tbl_push_notification',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    title: {
      type: Sequelize.STRING,
    },
    message_title: {
      type: Sequelize.STRING,
    },
    message_content: {
      type: Sequelize.STRING,
    },
    sending_unix_timestamp: {
      type: Sequelize.BIGINT,
    },
    time: {
      type: Sequelize.STRING,
    },
    user_id: {
      type: Sequelize.STRING,
    },
    class: {
      type: Sequelize.STRING,
    },
    age_from: {
      type: Sequelize.INTEGER,
    },
    age_to: {
      type: Sequelize.INTEGER,
    },
    frequency: {
      type: Sequelize.STRING,
      validate: {
        isIn: [['ONE_TIME', 'REGULAR_EVERY_7_DAYS', 'REGULAR_EVERY_30_DAYS']],
      },
      defaultValue: 'ONE_TIME',
    },
    type: {
      type: Sequelize.STRING,
      validate: {
        isIn: [['USER', 'GROUP']],
      },
      defaultValue: 'USER',
    },
    last_number_of_user_to_send: {
      type: Sequelize.INTEGER,
      validate: {
        min: 0,
      },
      defaultValue: 0,
    },
    last_number_of_user_actually_be_sent: {
      type: Sequelize.INTEGER,
      validate: {
        min: 0,
      },
      defaultValue: 0,
    },
    last_number_of_user_actually_be_sent_successfully: {
      type: Sequelize.INTEGER,
      validate: {
        min: 0,
      },
      defaultValue: 0,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    hooks: {
      beforeCreate(instances: any, options, fn) {
        if (instances.sending_unix_timestamp) {
          const time = moment(Number(instances.sending_unix_timestamp)).format(
            'HH:mm'
          )
          instances.time = time
        }
      },
      beforeUpdate(instances: any, options, fn) {
        if (instances.sending_unix_timestamp) {
          const time = moment(Number(instances.sending_unix_timestamp)).format(
            'HH:mm'
          )
          instances.time = time
        }
      },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
)
