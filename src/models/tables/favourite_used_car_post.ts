import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

/**
 * @swagger
 * components:
 *   schemas:
 *     FavouriteUsedCarPost:
 *       type: object
 *       description: User favorites for used car posts
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the favorite relationship
 *           example: "123e4567-e89b-12d3-a456-************"
 *         status:
 *           type: boolean
 *           description: Whether this favorite is active
 *           default: true
 *           example: true
 *         created_at_unix_timestamp:
 *           type: integer
 *           format: int64
 *           description: Unix timestamp when the favorite was created
 *           example: 1640995200000
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the favorite was created
 *           example: "2023-12-31T00:00:00.000Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the favorite was last updated
 *           example: "2023-12-31T00:00:00.000Z"
 *         user_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the user who favorited the post
 *           example: "123e4567-e89b-12d3-a456-************"
 *         used_car_post_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the favorited used car post
 *           example: "123e4567-e89b-12d3-a456-************"
 *       required:
 *         - id
 *         - status
 *         - created_at
 *         - updated_at
 *         - user_id
 *         - used_car_post_id
 */

export const FavouriteUsedCarPost = sequelize.define(
  'tbl_favourite_used_car_post',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
      comment: 'Primary key - UUID for the favorite relationship',
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
      comment: 'Whether this favorite is active',
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
      comment: 'Unix timestamp when the favorite was created',
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the favorite was created',
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the favorite was last updated',
    },
    deleted_at: { 
      type: 'TIMESTAMP',
      comment: 'Timestamp when the favorite was soft deleted',
    },
    
    // foreign key:
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the user who favorited the post',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    used_car_post_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_used_car_post',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the favorited used car post',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
      beforeUpdate: (item: any) => {
        item.updated_at = moment().toDate();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'used_car_post_id'],
        name: 'unique_user_used_car_post_favourite',
        where: {
          deleted_at: null,
        },
      },
      {
        fields: ['user_id'],
        name: 'idx_favourite_used_car_post_user_id',
      },
      {
        fields: ['used_car_post_id'],
        name: 'idx_favourite_used_car_post_post_id',
      },
    ],
    tableName: 'tbl_favourite_used_car_post',
    comment: 'User favorites for used car posts',
  }
); 