import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';
import { REVIEW_TYPE } from '@/const';
import { utilService } from '@/services';
import { shortenShopListData } from '@/config/utils';

export const RecentReading = sequelize.define(
  'tbl_recent_reading',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    // new field:
    type: {
      type: Sequelize.STRING,
      validate: {
        isIn: [
          [
            REVIEW_TYPE.SHOP,
            REVIEW_TYPE.RECRUIT
          ],
        ],
      },
      defaultValue: REVIEW_TYPE.SHOP,
    },
    // foreign key:
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
    },
    shop_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_shop',
        key: 'id',
      },
    },
    recruit_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_recruit',
        key: 'id',
      },
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
