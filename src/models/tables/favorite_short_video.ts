import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'

export const FavouriteShortVideo = sequelize.define(
  'tbl_favourite_short_video',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
    },
    short_video_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_short_video',
        key: 'id',
      },
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: {type: 'TIMESTAMP'},
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf()
      },
    },
    timestamps: true,
    paranoid: true,
    underscored: true,
    freezeTableName: true,
    defaultScope: {
      attributes: {exclude: ['deleted_at']},
    },
    scopes: {
      delete: {
        where: {delete_at: {$ne: null}},
        paranoid: false,
      },
    },
  }
)
