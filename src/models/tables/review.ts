import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import {REVIEW_TYPE, REVIEW_SUB_TYPE} from '@/const'

export const Review = sequelize.define(
    'tbl_review',
    {
        // default field:
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        content: {
            type: Sequelize.TEXT,
            allowNull: false,
        },
        board_type: {
            type: Sequelize.STRING,
        },
        parent_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_review',
                key: 'id',
            },
        },
        parent_user_id: {
            type: Sequelize.STRING,
        },
        private: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        image: {
            type: Sequelize.STRING,
        },
        thumbnail: {
            type: Sequelize.STRING,
        },
        type: {
            type: Sequelize.STRING,
            validate: {
                isIn: [[REVIEW_TYPE.SHOP, REVIEW_TYPE.RECRUIT, REVIEW_TYPE.POST]],
            },
            defaultValue: REVIEW_TYPE.SHOP,
        },
        sub_type: {
            type: Sequelize.ENUM(REVIEW_SUB_TYPE.DEFAULT, REVIEW_SUB_TYPE.FEEDBACK),
            defaultValue: REVIEW_SUB_TYPE.DEFAULT,
            allowNull: false,
        },
        last_5_comments: {
            type: Sequelize.ARRAY({type: Sequelize.JSONB}),
            defaultValue: [],
        },
        report: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        // foreign key:
        user_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        shop_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_shop',
                key: 'id',
            },
        },
        recruit_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_recruit',
                key: 'id',
            },
        },
        post_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_post',
                key: 'id',
            },
        },
        user_name: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        employee_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_employee',
                key: 'id',
            },
        },
        like: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        dislike: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
