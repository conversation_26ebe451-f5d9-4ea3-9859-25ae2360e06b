import {sequelize, Sequelize} from '../base';
import * as moment from 'moment';
import {BOARD, NAV} from "@/const";

export const Navbar = sequelize.define(
    'tbl_nav_bar',
    {
        // default field:
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        name: {
            type: Sequelize.STRING,
        },
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        type: {
            type: Sequelize.STRING,
            // defaultValue: NAV.ROAD_SHOP,
            // validate: {
            //     isIn: [
            //         [
            //             NAV.ROAD_SHOP,
            //             NAV.EVENT,
            //             NAV.HOME_MASSAGE,
            //             NAV.RESTAURANT,
            //         ],
            //     ],
            // },
        },
        image_active: {
            type: Sequelize.STRING,
            allowNull : true
        },
        image_inactive: {
            type: Sequelize.STRING,
            allowNull : true
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        // link_id: {
        //     type: Sequelize.UUID,
        //     references: {
        //         model: 'tbl_link',
        //         key: 'id',
        //     },
        // },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf();
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
);
