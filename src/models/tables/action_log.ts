import { sequelize, Sequelize } from "../base";

export const ActionLog = sequelize.define(
  "tbl_action_log",
  {
    id: {
      type: Sequelize.UUID,
      allowNull: false,
      primaryKey: true,
      defaultValue: Sequelize.UUIDV1,
      comment: 'Primary key - UUID',
    },
    user_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      comment: 'ID of the user who performed the action',
    },
    action_type: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'Type of the action performed (e.g., write_comment, daily_login)',
    },
    point: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    exp: {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
      comment: 'Timestamp when the action was performed',
    },
    deleted_at: { type: "TIMESTAMP" },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
