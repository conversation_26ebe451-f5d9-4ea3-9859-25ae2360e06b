import { sequelize, Sequelize } from "../base";

export const BoardPermission = sequelize.define(
  "tbl_board_permission",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    level_id: {
      type: Sequelize.UUID,
      allowNull: false,
      comment: 'Foreign key referencing level (access group)',
      references: {
        model: 'tbl_level',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    board_id: {
      type: Sequelize.UUID,
      allowNull: false,
      comment: 'Foreign key referencing board',
      references: {
        model: 'tbl_board',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    can_read: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether users at this level can read this board',
    },
    can_write: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether users at this level can write to this board',
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    deleted_at: { type: "TIMESTAMP" },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        name: 'uq_board_permission_level_board',
        fields: ['level_id', 'board_id'],
      },
    ],
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
);
