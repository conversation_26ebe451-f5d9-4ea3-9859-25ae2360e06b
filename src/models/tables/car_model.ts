import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'

/**
 * @swagger
 * components:
 *   schemas:
 *     CarModel:
 *       type: object
 *       required:
 *         - name
 *         - manufacturer_id
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique model identifier
 *         name:
 *           type: string
 *           description: Model name (e.g., Camry, Civic, 3 Series)
 *         manufacturer_id:
 *           type: string
 *           format: uuid
 *           description: ID of the car manufacturer
 *         body_type:
 *           type: string
 *           enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
 *           description: Default body type for this model
 *         fuel_type:
 *           type: string
 *           enum: [gasoline, diesel, hybrid, electric, lpg]
 *           description: Default fuel type for this model
 *         transmission:
 *           type: string
 *           enum: [manual, automatic, cvt]
 *           description: Default transmission for this model
 *         engine_size:
 *           type: string
 *           description: Engine size/displacement (e.g., 2.0L, 1.5T)
 *         start_year:
 *           type: integer
 *           description: First production year
 *         end_year:
 *           type: integer
 *           description: Last production year (null if still in production)
 *         description:
 *           type: string
 *           description: Brief description of the model
 *         image_url:
 *           type: string
 *           description: URL to model image
 *         status:
 *           type: boolean
 *           description: Whether the model is active
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

export const CarModel = sequelize.define(
    'tbl_car_model',
    {
        // Standard fields following project patterns
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        
        // Model-specific fields
        name: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        body_type: {
            type: Sequelize.ENUM('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'wagon', 'pickup', 'van'),
            allowNull: true,
        },
        fuel_type: {
            type: Sequelize.ENUM('gasoline', 'diesel', 'hybrid', 'electric', 'lpg'),
            allowNull: true,
        },
        transmission: {
            type: Sequelize.ENUM('manual', 'automatic', 'cvt'),
            allowNull: true,
        },
        engine_size: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        start_year: {
            type: Sequelize.INTEGER,
            allowNull: true,
            validate: {
                min: 1900,
                max: new Date().getFullYear() + 5,
            },
        },
        end_year: {
            type: Sequelize.INTEGER,
            allowNull: true,
            validate: {
                min: 1900,
                max: new Date().getFullYear() + 5,
            },
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        image_url: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        
        // Foreign key relationships
        manufacturer_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_manufacturer',
                key: 'id',
            },
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
            beforeUpdate: (item: any) => {
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
        indexes: [
            {
                unique: true,
                fields: ['manufacturer_id', 'name'],
                name: 'unique_manufacturer_model'
            }
        ]
    }
) 