import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'

/**
 * @swagger
 * components:
 *   schemas:
 *     CarManufacturer:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique manufacturer identifier
 *         name:
 *           type: string
 *           description: Manufacturer name (e.g., Toyota, Honda, BMW)
 *         country:
 *           type: string
 *           description: Country of origin
 *         logo_url:
 *           type: string
 *           description: URL to manufacturer logo
 *         website:
 *           type: string
 *           description: Official website URL
 *         description:
 *           type: string
 *           description: Brief description of the manufacturer
 *         status:
 *           type: boolean
 *           description: Whether the manufacturer is active
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

export const CarManufacturer = sequelize.define(
    'tbl_car_manufacturer',
    {
        // Standard fields following project patterns
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        
        // Manufacturer-specific fields
        name: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: true,
        },
        country: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        logo_url: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        website: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        alias: {
            type: Sequelize.STRING,
        },
        index: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
            beforeUpdate: (item: any) => {
                if (item.name) {
                    item.alias = item.name.trim().split('/').join('-').split(' ').join('-').toLowerCase()
                }
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
) 