import { sequelize, Sequelize } from '../base'
import { AnswerQuestion } from '@/models'
import { QUESTION_TYPE, QUESTION_STATUS } from '@/const'
import * as moment from 'moment'

export const Question = sequelize.define(
  'tbl_question',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
      allowNull: false,
    },
    type: {
      type: Sequelize.STRING,
      validate: {
        isIn: [
          [
            QUESTION_TYPE.CHANGE_DESIGN,
            QUESTION_TYPE.PAYMENT_ADVERTISEMENT,
            QUESTION_TYPE.LIMIT_FUNCTION,
            QUESTION_TYPE.NORMAL_QUESTION,
            QUESTION_TYPE.ERROR,
            QUESTION_TYPE.OTHER,
          ],
        ],
      },
      defaultValue: QUESTION_TYPE.OTHER,
      allowNull: false,
    },
    status: {
      type: Sequelize.STRING,
      validate: {
        isIn: [
          [
            QUESTION_STATUS.COMPLETED,
            QUESTION_STATUS.PENDING,
            QUESTION_STATUS.MORE,
          ],
        ],
      },
      defaultValue: QUESTION_STATUS.PENDING,
      allowNull: false,
    },
    phone_number: {
      type: Sequelize.STRING,
    },
    category: {
      type: Sequelize.STRING,
    },
    content: {
      type: Sequelize.TEXT,
    },
    images: {
      type: Sequelize.ARRAY({type: Sequelize.STRING}),
    },
    thumbnails: {
      type: Sequelize.ARRAY({type: Sequelize.STRING}),
    },
    user_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: {type: 'TIMESTAMP'},
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf()
      },
      beforeDestroy: (item: any) => {
        AnswerQuestion.destroy({where: {question_id: item.id}})
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: {exclude: ['deleted_at']},
    },
    scopes: {
      deleted: {
        where: {deleted_at: {$ne: null}},
        paranoid: false,
      },
    },
  }
)
