import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

export const PopupWindow = sequelize.define(
  "tbl_popup_window",
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    name: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    image_url: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    short_cut_url_address: {
      type: Sequelize.STRING,
      allowNull: true,
    },
    administrator_note: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    is_exposed: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    is_mobile_featured: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    },
    display_order: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: "TIMESTAMP",
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      allowNull: false,
    },
    updated_at: {
      type: "TIMESTAMP",
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      allowNull: false,
    },
    deleted_at: {
      type: "TIMESTAMP",
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ["deleted_at"] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { [Sequelize.Op.ne]: null } },
        paranoid: false,
      },
    },
  }
);
