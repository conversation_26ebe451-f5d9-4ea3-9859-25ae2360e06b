import { sequelize, Sequelize } from '../base'
import * as moment from 'moment'

export const FavoriteJob = sequelize.define(
  'tbl_favorite_job',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    job_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tbl_job_request',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    created_at: {
      type: 'TIMESTAMP',
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
      type: 'TIMESTAMP',
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf()
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: {exclude: ['deleted_at']},
    },
    scopes: {
      deleted: {
        where: {deleted_at: {$ne: null}},
        paranoid: false,
      },
    },
  }
)
