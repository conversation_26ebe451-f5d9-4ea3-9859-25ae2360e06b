import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'
import { PRODUCT_STATE } from "@/const";

/**
 * @swagger
 * components:
 *   schemas:
 *     UsedCarPost:
 *       type: object
 *       required:
 *         - title
 *         - price
 *         - year
 *         - mileage
 *         - contact_phone
 *         - latitude
 *         - longitude
 *         - address
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique used car post identifier
 *         title:
 *           type: string
 *           description: Car listing title
 *         description:
 *           type: string
 *           description: Detailed description of the car
 *         price:
 *           type: integer
 *           description: Asking price for the car
 *         year:
 *           type: integer
 *           description: Manufacturing year of the car
 *         mileage:
 *           type: integer
 *           description: Current mileage in kilometers
 *         fuel_type:
 *           type: string
 *           enum: [gasoline, diesel, hybrid, electric, lpg]
 *           description: Type of fuel the car uses
 *         transmission:
 *           type: string
 *           enum: [manual, automatic, cvt]
 *           description: Transmission type
 *         body_type:
 *           type: string
 *           enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
 *           description: Body type of the car
 *         exterior_color:
 *           type: string
 *           description: Exterior color of the car
 *         interior_color:
 *           type: string
 *           description: Interior color of the car
 *         condition:
 *           type: string
 *           enum: [excellent, very_good, good, fair, poor]
 *           description: Overall condition of the car
 *         accident_history:
 *           type: boolean
 *           description: Whether the car has been in an accident
 *         number_of_owners:
 *           type: integer
 *           description: Number of previous owners
 *         images:
 *           type: array
 *           items:
 *             type: string
 *           description: URLs to car images
 *         thumbnails:
 *           type: array
 *           items:
 *             type: string
 *           description: URLs to car thumbnail images
 *         contact_phone:
 *           type: string
 *           description: Contact phone number
 *         contact_email:
 *           type: string
 *           description: Contact email address
 *         latitude:
 *           type: number
 *           description: Location latitude
 *         longitude:
 *           type: number
 *           description: Location longitude
 *         address:
 *           type: string
 *           description: Full address
 *         short_address:
 *           type: string
 *           description: Short address
 *         state:
 *           type: string
 *           enum: [available, pending, sold, withdrawn]
 *           description: Current status of the listing
 *         view_count:
 *           type: integer
 *           description: Number of views
 *         like_count:
 *           type: integer
 *           description: Number of likes
 *         comment_count:
 *           type: integer
 *           description: Number of comments
 *         user_id:
 *           type: string
 *           format: uuid
 *           description: ID of the user who created this listing
 *         manufacturer_id:
 *           type: string
 *           format: uuid
 *           description: ID of the car manufacturer
 *         model_id:
 *           type: string
 *           format: uuid
 *           description: ID of the car model
 *         category_id:
 *           type: string
 *           format: uuid
 *           description: ID of the post category
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the post was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the post was last updated
 */

export const UsedCarPost = sequelize.define(
    'tbl_used_car_post',
    {
        // Standard fields following project patterns
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        
        // Car-specific fields
        title: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        description: {
            type: Sequelize.TEXT,
            allowNull: true,
        },
        price: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            allowNull: false,
        },
        negotiable: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        
        // Car details
        year: {
            type: Sequelize.INTEGER,
            allowNull: false,
            validate: {
                min: 1900,
                max: new Date().getFullYear() + 1,
            },
        },
        mileage: {
            type: Sequelize.INTEGER,
            allowNull: false,
            validate: {
                min: 0,
            },
        },
        fuel_type: {
            type: Sequelize.ENUM('gasoline', 'diesel', 'hybrid', 'electric', 'lpg'),
            allowNull: false,
            defaultValue: 'gasoline',
        },
        transmission: {
            type: Sequelize.ENUM('manual', 'automatic', 'cvt'),
            allowNull: false,
            defaultValue: 'manual',
        },
        body_type: {
            type: Sequelize.ENUM('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'wagon', 'pickup', 'van'),
            allowNull: true,
        },
        exterior_color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        interior_color: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        condition: {
            type: Sequelize.ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
            allowNull: false,
            defaultValue: 'good',
        },
        accident_history: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        number_of_owners: {
            type: Sequelize.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
            },
        },
        
        // Image handling (reusing existing pattern)
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        thumbnails: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            allowNull: true,
        },
        
        // Contact information
        contact_phone: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        contact_email: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        
        // Location fields (reusing existing pattern from SecondHandMarket)
        latitude: {
            type: Sequelize.DOUBLE,
            allowNull: false,
        },
        longitude: {
            type: Sequelize.DOUBLE,
            allowNull: false,
        },
        position: {
            type: Sequelize.GEOMETRY('POINT', 4326),
            defaultValue: {
                type: 'Point',
                coordinates: [0, 0],
                crs: {type: 'name', properties: {name: 'EPSG:4326'}},
            },
        },
        address: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        short_address: {
            type: Sequelize.STRING,
        },
        address_2: {
            type: Sequelize.STRING,
        },
        geolocation_api_type: {
            type: Sequelize.STRING,
            defaultValue: 'NAVER',
            allowNull: false,
        },
        
        // Engagement metrics (reusing existing pattern)
        view_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        like_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        comment_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        
        // Status tracking
        state: {
            type: Sequelize.ENUM('available', 'pending', 'sold', 'withdrawn'),
            defaultValue: 'available',
        },
        sold_at: {
            type: 'TIMESTAMP',
            allowNull: true,
        },
        
        // Additional fields
        alias: {
            type: Sequelize.STRING,
        },
        featured: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        },
        rank: {
            type: Sequelize.BIGINT,
            defaultValue: 0,
        },
        
        // Foreign key relationships
        user_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        manufacturer_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_manufacturer',
                key: 'id',
            },
        },
        model_id: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
                model: 'tbl_car_model',
                key: 'id',
            },
        },
        category_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_category',
                key: 'id',
            },
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    }
                }
                if (item.title) {
                    item.alias = item.title.trim().split('/').join('-').split(' ').join('-')
                }
            },
            beforeUpdate: (item: any) => {
                if (item.longitude && item.latitude) {
                    item.position = {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude],
                        crs: {type: 'name', properties: {name: 'EPSG:4326'}},
                    }
                }
                if (item.title) {
                    item.alias = item.title.trim().split('/').join('-').split(' ').join('-')
                }
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
) 