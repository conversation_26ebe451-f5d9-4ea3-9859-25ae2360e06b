import { sequelize, Sequelize } from '../base'
import {BLOG_TYPE} from "@/const";

export const Blog = sequelize.define(
  'tbl_blog',
  {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
    },
    title: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    content: {
      type: Sequelize.TEXT,
    },
    images: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
        allowNull: true,
    },
    thumbnails: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
        allowNull: true,
    },
    tags: {
      type: Sequelize.ARRAY({ type: Sequelize.STRING }),
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    view: {
      type: Sequelize.INTEGER,
      defaultValue: 0,
    },
    category_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_category',
        key: 'id',
      },
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
    },
    deleted_at: { type: 'TIMESTAMP' },
    execute_at: {
          type: 'TIMESTAMP',
          allowNull: true,
      },
      status: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
          allowNull: false,
      },
      videos: {
          type: Sequelize.ARRAY({ type: Sequelize.STRING }),
          allowNull: true,
      },
      slug: {
          type: Sequelize.STRING,
          allowNull: false,
      },
      type: {
          type: Sequelize.STRING,
          defaultValue : BLOG_TYPE.BLOG_1
      },
      keywords: {
          type: Sequelize.TEXT,
      },
      description: {
          type: Sequelize.TEXT,
      },

      // video: {
      //     type: Sequelize.STRING,
      //     allowNull: true,
      // },
  },
  {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: {
        exclude: ['deleted_at'],
      },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
  }
)
