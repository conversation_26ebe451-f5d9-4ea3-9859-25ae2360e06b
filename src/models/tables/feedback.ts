import { sequelize, Sequelize } from '../base'

export const Feedback = sequelize.define(
  'tbl_feedback',
  {
    review_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_review',
        key: 'id',
      },
      allowNull: false,
    },
    feedback_item_id: {
      type: Sequelize.UUID,
      allowNull: false,
    },
  },
  {
    timestamps: false,
    underscored: true,
    freezeTableName: true,
  }
)
