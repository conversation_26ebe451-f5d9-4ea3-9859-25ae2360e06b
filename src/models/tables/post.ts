import {sequelize, Sequelize} from '../base'
import * as moment from 'moment'

/**
 * @swagger
 * components:
 *   schemas:
 *     Post:
 *       type: object
 *       required:
 *         - location
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique post identifier
 *         title:
 *           type: string
 *           description: Post title
 *         content:
 *           type: string
 *           description: Post content text
 *         images:
 *           type: array
 *           items:
 *             type: string
 *           description: URLs to post images
 *         thumbnails:
 *           type: array
 *           items:
 *             type: string
 *           description: URLs to post thumbnails
 *         videos:
 *           type: array
 *           items:
 *             type: string
 *           description: URLs to post videos
 *         comment:
 *           type: integer
 *           description: Number of comments on this post
 *         soft_comment_count:
 *           type: integer
 *           description: Number of soft comments on this post
 *         like:
 *           type: integer
 *           description: Number of likes on this post
 *         dislike:
 *           type: integer
 *           description: Number of dislikes on this post
 *         report:
 *           type: integer
 *           description: Number of reports on this post
 *         view:
 *           type: integer
 *           description: Number of views on this post
 *         user_id:
 *           type: string
 *           format: uuid
 *           description: ID of the user who created this post
 *         employee_id:
 *           type: string
 *           format: uuid
 *           description: ID of the associated employee (if applicable)
 *         category_id:
 *           type: string
 *           format: uuid
 *           description: ID of the post category
 *         location:
 *           type: string
 *           description: Location information for the post
 *         execute_at:
 *           type: string
 *           format: date-time
 *           description: When the post is scheduled to be published
 *         user_name:
 *           type: string
 *           description: Name of the user who created the post
 *         is_hide:
 *           type: boolean
 *           description: Whether the post is hidden
 *         status:
 *           type: boolean
 *           description: Post status (active/inactive)
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the post was created
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the post was last updated
 */

export const Post = sequelize.define(
    'tbl_post',
    {
        // default field:
        id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV1,
            primaryKey: true,
        },
        status: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
        created_at_unix_timestamp: {
            type: Sequelize.BIGINT,
            validate: {
                min: 0,
            },
        },
        created_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        updated_at: {
            type: 'TIMESTAMP',
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false,
        },
        deleted_at: {type: 'TIMESTAMP'},
        // new field:
        title: {
            type: Sequelize.TEXT,
        },
        content: {
            type: Sequelize.TEXT,
        },
        images: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        thumbnails: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
        },
        videos: {
            type: Sequelize.ARRAY({type: Sequelize.STRING}),
            defaultValue: [],
        },
        comment: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        soft_comment_count: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        like: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        dislike: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        report: {
            type: Sequelize.INTEGER,
            validate: {
                min: 0,
            },
            defaultValue: 0,
        },
        view: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        // foreign key:
        user_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_user',
                key: 'id',
            },
        },
        employee_id: {
            type: Sequelize.UUID,
            allowNull: true,
            references: {
                model: 'tbl_employee',
                key: 'id',
            },
        },
        category_id: {
            type: Sequelize.UUID,
            references: {
                model: 'tbl_category',
                key: 'id',
            },
        },
        location: {
            type: Sequelize.STRING,
            allowNull: false,
        },
        execute_at: {
            type: 'TIMESTAMP',
            allowNull: true,
        },
        user_name: {
            type: Sequelize.STRING,
            allowNull: true,
        },
        is_hide: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
        },
    },
    {
        hooks: {
            beforeCreate: (item: any) => {
                item.created_at_unix_timestamp = moment().valueOf()
            },
        },
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        paranoid: true,
        defaultScope: {
            attributes: {exclude: ['deleted_at']},
        },
        scopes: {
            deleted: {
                where: {deleted_at: {$ne: null}},
                paranoid: false,
            },
        },
    }
)
