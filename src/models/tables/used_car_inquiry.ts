import { sequelize, Sequelize } from '../base';
import * as moment from 'moment';

/**
 * @swagger
 * components:
 *   schemas:
 *     UsedCarInquiry:
 *       type: object
 *       description: Inquiry from a potential buyer to a seller about a used car post
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the inquiry
 *           example: "123e4567-e89b-12d3-a456-************"
 *         status:
 *           type: boolean
 *           description: Whether this inquiry is active
 *           default: true
 *           example: true
 *         created_at_unix_timestamp:
 *           type: integer
 *           format: int64
 *           description: Unix timestamp when the inquiry was created
 *           example: 1640995200000
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the inquiry was created
 *           example: "2023-12-31T00:00:00.000Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: ISO timestamp when the inquiry was last updated
 *           example: "2023-12-31T00:00:00.000Z"
 *         used_car_post_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the used car post being inquired about
 *           example: "123e4567-e89b-12d3-a456-************"
 *         buyer_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the user making the inquiry (buyer)
 *           example: "123e4567-e89b-12d3-a456-************"
 *         seller_id:
 *           type: string
 *           format: uuid
 *           description: Reference to the user who owns the car post (seller)
 *           example: "123e4567-e89b-12d3-a456-************"
 *         inquiry_type:
 *           type: string
 *           enum: [general, price_negotiation, test_drive, inspection, financing]
 *           description: Type of inquiry
 *           example: "general"
 *         subject:
 *           type: string
 *           description: Subject/title of the inquiry
 *           example: "Interested in your 2018 Honda Civic"
 *         message:
 *           type: string
 *           description: The inquiry message content
 *           example: "Hi, I'm interested in your car. Is it still available?"
 *         buyer_name:
 *           type: string
 *           description: Name of the buyer (can be different from username)
 *           example: "John Doe"
 *         buyer_phone:
 *           type: string
 *           description: Contact phone number of the buyer
 *           example: "******-0123"
 *         buyer_email:
 *           type: string
 *           description: Contact email of the buyer
 *           example: "<EMAIL>"
 *         preferred_contact_method:
 *           type: string
 *           enum: [phone, email, in_app]
 *           description: Buyer's preferred contact method
 *           default: "in_app"
 *           example: "phone"
 *         inquiry_status:
 *           type: string
 *           enum: [pending, replied, in_negotiation, scheduled, closed, cancelled]
 *           description: Status of the inquiry
 *           default: "pending"
 *           example: "pending"
 *         offered_price:
 *           type: number
 *           description: Price offered by the buyer (if any)
 *           example: 15000
 *         is_serious_buyer:
 *           type: boolean
 *           description: Whether the buyer indicates they are serious/ready to purchase
 *           default: false
 *           example: true
 *         preferred_meeting_location:
 *           type: string
 *           description: Buyer's preferred location for meeting/test drive
 *           example: "Downtown Mall parking lot"
 *         available_dates:
 *           type: string
 *           description: Text description of buyer's availability
 *           example: "Weekends, evenings after 6 PM"
 *         seller_response:
 *           type: string
 *           description: Seller's response message (if any)
 *           example: "Thanks for your interest! The car is still available."
 *         seller_responded_at:
 *           type: string
 *           format: date-time
 *           description: When the seller responded
 *           example: "2023-12-31T12:00:00.000Z"
 *         is_read_by_seller:
 *           type: boolean
 *           description: Whether the seller has read this inquiry
 *           default: false
 *           example: false
 *         is_read_by_buyer:
 *           type: boolean
 *           description: Whether the buyer has read the seller's response
 *           default: false
 *           example: false
 *         meeting_scheduled_at:
 *           type: string
 *           format: date-time
 *           description: Scheduled meeting time (if applicable)
 *           example: "2023-12-31T15:00:00.000Z"
 *         metadata:
 *           type: object
 *           description: Additional metadata (JSON)
 *           example: {"source": "mobile_app", "utm_campaign": "email"}
 *       required:
 *         - id
 *         - status
 *         - created_at
 *         - updated_at
 *         - used_car_post_id
 *         - buyer_id
 *         - seller_id
 *         - inquiry_type
 *         - message
 */

export const UsedCarInquiry = sequelize.define(
  'tbl_used_car_inquiry',
  {
    // default field:
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV1,
      primaryKey: true,
      comment: 'Primary key - UUID for the inquiry',
    },
    status: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: false,
      comment: 'Whether this inquiry is active',
    },
    created_at_unix_timestamp: {
      type: Sequelize.BIGINT,
      validate: {
        min: 0,
      },
      comment: 'Unix timestamp when the inquiry was created',
    },
    created_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the inquiry was created',
    },
    updated_at: {
      type: 'TIMESTAMP',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      allowNull: false,
      comment: 'Timestamp when the inquiry was last updated',
    },
    deleted_at: { 
      type: 'TIMESTAMP',
      comment: 'Timestamp when the inquiry was soft deleted',
    },
    
    // inquiry details:
    inquiry_type: {
      type: Sequelize.ENUM('general', 'price_negotiation', 'test_drive', 'inspection', 'financing'),
      allowNull: false,
      comment: 'Type of inquiry',
    },
    subject: {
      type: Sequelize.STRING,
      comment: 'Subject/title of the inquiry',
      validate: {
        len: [0, 200],
      },
    },
    message: {
      type: Sequelize.TEXT,
      allowNull: false,
      comment: 'The inquiry message content',
      validate: {
        len: [1, 5000],
      },
    },
    
    // buyer contact information:
    buyer_name: {
      type: Sequelize.STRING,
      comment: 'Name of the buyer (can be different from username)',
      validate: {
        len: [0, 100],
      },
    },
    buyer_phone: {
      type: Sequelize.STRING,
      comment: 'Contact phone number of the buyer',
      validate: {
        len: [0, 20],
      },
    },
    buyer_email: {
      type: Sequelize.STRING,
      comment: 'Contact email of the buyer',
      validate: {
        isEmail: true,
        len: [0, 100],
      },
    },
    preferred_contact_method: {
      type: Sequelize.ENUM('phone', 'email', 'in_app'),
      defaultValue: 'in_app',
      comment: "Buyer's preferred contact method",
    },
    
    // inquiry specifics:
    offered_price: {
      type: Sequelize.DECIMAL(15, 2),
      validate: {
        min: 0,
      },
      comment: 'Price offered by the buyer (if any)',
    },
    is_serious_buyer: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: 'Whether the buyer indicates they are serious/ready to purchase',
    },
    preferred_meeting_location: {
      type: Sequelize.STRING,
      comment: "Buyer's preferred location for meeting/test drive",
      validate: {
        len: [0, 200],
      },
    },
    available_dates: {
      type: Sequelize.TEXT,
      comment: "Text description of buyer's availability",
    },
    
    // inquiry status and responses:
    inquiry_status: {
      type: Sequelize.ENUM('pending', 'replied', 'in_negotiation', 'scheduled', 'closed', 'cancelled'),
      defaultValue: 'pending',
      comment: 'Status of the inquiry',
    },
    seller_response: {
      type: Sequelize.TEXT,
      comment: "Seller's response message (if any)",
      validate: {
        len: [0, 5000],
      },
    },
    seller_responded_at: {
      type: 'TIMESTAMP',
      comment: 'When the seller responded',
    },
    
    // read status:
    is_read_by_seller: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: 'Whether the seller has read this inquiry',
    },
    is_read_by_buyer: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: "Whether the buyer has read the seller's response",
    },
    
    // meeting scheduling:
    meeting_scheduled_at: {
      type: 'TIMESTAMP',
      comment: 'Scheduled meeting time (if applicable)',
    },
    
    // metadata:
    metadata: {
      type: Sequelize.JSONB,
      comment: 'Additional metadata (JSON)',
    },
    
    // foreign keys:
    used_car_post_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_used_car_post',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the used car post being inquired about',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    buyer_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the user making the inquiry (buyer)',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    seller_id: {
      type: Sequelize.UUID,
      references: {
        model: 'tbl_user',
        key: 'id',
      },
      allowNull: false,
      comment: 'Reference to the user who owns the car post (seller)',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  },
  {
    hooks: {
      beforeCreate: (item: any) => {
        item.created_at_unix_timestamp = moment().valueOf();
      },
      beforeUpdate: (item: any) => {
        item.updated_at = moment().toDate();
      },
    },
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,
    defaultScope: {
      attributes: { exclude: ['deleted_at'] },
    },
    scopes: {
      deleted: {
        where: { deleted_at: { $ne: null } },
        paranoid: false,
      },
    },
    indexes: [
      {
        fields: ['used_car_post_id'],
        name: 'idx_used_car_inquiry_post_id',
      },
      {
        fields: ['buyer_id'],
        name: 'idx_used_car_inquiry_buyer_id',
      },
      {
        fields: ['seller_id'],
        name: 'idx_used_car_inquiry_seller_id',
      },
      {
        fields: ['inquiry_status'],
        name: 'idx_used_car_inquiry_status',
      },
      {
        fields: ['inquiry_type'],
        name: 'idx_used_car_inquiry_type',
      },
      {
        fields: ['is_read_by_seller'],
        name: 'idx_used_car_inquiry_read_by_seller',
      },
      {
        fields: ['is_read_by_buyer'],
        name: 'idx_used_car_inquiry_read_by_buyer',
      },
      {
        fields: ['created_at'],
        name: 'idx_used_car_inquiry_created_at',
      },
      {
        unique: true,
        fields: ['buyer_id', 'used_car_post_id', 'created_at'],
        name: 'idx_used_car_inquiry_unique_buyer_post_time',
      },
    ],
    tableName: 'tbl_used_car_inquiry',
    comment: 'Inquiry from a potential buyer to a seller about a used car post',
  }
); 