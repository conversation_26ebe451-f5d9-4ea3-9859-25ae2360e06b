import { config } from '@/config'
import * as Sequelize from 'sequelize'

let option = undefined
if (process.env.NODE_ENV === 'production') {
  option = {
    host: config.database.sql['host'],
    port: config.database.sql['port'],
    dialect: config.database.sql['dialect'],
    // default setting
    pool: {
      max: 50,
      min: 0,
      idle: 1200000,
      acquire: 1000000,
    },
    timezone: '+00:00',
    logging: false,
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    },
  }
} else {
  option = {
    host: config.database.sql['host'],
    port: config.database.sql['port'],
    dialect: config.database.sql['dialect'],
    // default setting
    pool: {
      max: 50,
      min: 0,
      idle: 1200000,
      acquire: 1000000,
    },
    timezone: '+07:00',
    dialectOptions: {
      ssl: false
    },
    logging: false,
  }
}
const sequelize = new Sequelize(
  config.database.sql['database'],
  config.database.sql['username'],
  config.database.sql['password'],
  option
)

export { Sequelize, sequelize }
