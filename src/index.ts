import * as dotenv from 'dotenv'
dotenv.config()

import * as express from 'express'
import * as bodyParser from 'body-parser'
import * as logger from 'morgan'
import expressValidator = require('express-validator')
import { config } from '@/config'
import api from './routers'
import * as cors from 'cors'
import { scheduleService } from './services'
import * as compression from 'compression'
import { socketService } from '@/services/socketService'
import * as swaggerUi from 'swagger-ui-express'
import swaggerSpec from '@/config/swagger'
import { initRankingCron } from './jobs/cron/rankingCron'
import { startLevelUpCron } from './jobs/cron/levelUpCron'

console.log(
  'Starting server with at ' + process.pid + ' on port ' + config.server.port
)

const app = express()
app.use(
  logger('common', {
    skip: function (req, res) {
      if (req.url == '/_ah/health') {
        return true
      } else {
        return false
      }
    },
  })
)
app.use(
  bodyParser.json({
    limit: '100mb',
  })
)
app.use(compression())
app.use(
  bodyParser.urlencoded({
    extended: true,
    limit: '100mb',
  })
)
app.use(expressValidator())
// app.use('/api/*', cors())
app.use(cors());

// Swagger Documentation
app.use(
  '/api-docs',
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'API Documentation'
  })
)

app.use('/api', api)
// app.set('port', config.server.port)

socketService.init(app)
scheduleService.scheduleAll()

app.listen(config.server.port, () => {
  console.log(
    'Server is running on port ' + config.server.port + ' with pid ' + process.pid
  )
})

initRankingCron();
startLevelUpCron();