/** Declaration file generated by dts-gen */

export = guid;

declare function guid(guid: any): any;

declare namespace guid {
    const EMPTY: string;

    const prototype: {
    };

    function create(): any;

    function isGuid(value: any): any;

    function raw(): any;

    namespace create {
        const prototype: {
        };

    }

    namespace isGuid {
        const prototype: {
        };

    }

    namespace raw {
        const prototype: {
        };

    }

}

