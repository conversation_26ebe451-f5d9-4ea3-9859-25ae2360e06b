import { Op, fn } from "sequelize";
import {
  User,
  Level,
  ActionLog,
  ExpRule,
  Ranking,
  RankingDetail,
} from "../models";
import * as dayjs from "dayjs";
import { initSequelize } from "../models/initSequelize";

let rankingService: any;
let rankingDetailService: any;
const initialize = async () => {
  await initSequelize();

  // Import services sau khi initSequelize
  const servicesModule = await import("@/services");
  rankingService = servicesModule.rankingService;
  rankingDetailService = servicesModule.rankingDetailService;

  if (!rankingService.model) {
    if ("Ranking" in servicesModule) {
      rankingService.model = servicesModule.Ranking;
    } else if ("Ranking" in require("../models")) {
      rankingService.model = require("../models").Ranking;
    }
  }
  if (!rankingDetailService.model) {
    if ("RankingDetail" in servicesModule) {
      rankingDetailService.model = servicesModule.RankingDetail;
    } else if ("RankingDetail" in require("../models")) {
      rankingDetailService.model = require("../models").RankingDetail;
    }
  }
};

const calculateRanking = async (period: "weekly" | "monthly") => {
  if (!rankingService) {
    throw new Error(
      "rankingService has not been initialized. Please call initialize() first."
    );
  }

  const now = dayjs();
  const start =
    period === "weekly"
      ? now.startOf("week").toDate()
      : now.startOf("month").toDate();
  const end =
    period === "weekly"
      ? now.endOf("week").toDate()
      : now.endOf("month").toDate();

  const rules = await ExpRule.findAll({ raw: true });

  const actionTypes = rules.map((r: any) => r.action_type);
  const scoreMap = Object.fromEntries(
    rules.map((r: any) => [r.action_type, r.point])
  );

  const logs = await ActionLog.findAll({
    attributes: ["user_id", "action_type", [fn("COUNT", "*"), "count"]],
    where: {
      created_at: { [Op.between]: [start, end] },
      action_type: { [Op.in]: actionTypes },
    },
    group: ["user_id", "action_type"],
    raw: true,
  });
  // Build user score object
  const userScores: Record<string, any> = {};
  for (const row of logs as any[]) {
    const uid = row.user_id;
    const action = row.action_type;
    const count = parseInt(row.count, 10);
    const score = count * (scoreMap[action] || 0);

    if (!userScores[uid])
      userScores[uid] = { user_id: uid, total_score: 0, action_scores: {} };
    userScores[uid].action_scores[action] = score;
    userScores[uid].total_score += score;
  }

  // Sort users by total_score descending
  const sorted = Object.values(userScores).sort(
    (a, b) => b.total_score - a.total_score
  );

  const oldRankings = await Ranking.findAll({
    attributes: ["id"],
    where: {
      period,
      period_start: start,
      metric: "total",
    },
    raw: true,
  });

  const oldRankingIds = oldRankings.map((r: any) => r.id);

  if (oldRankingIds.length > 0) {
    await rankingDetailService.model.destroy({
      where: {
        ranking_id: { [Op.in]: oldRankingIds },
      },
    });
  }
  // Clear previous ranking data for the period & metric 'total'
  await Ranking.destroy({
    where: {
      period,
      period_start: start,
      metric: "total",
    },
    force: true,
  });

  // Insert new ranking + ranking_detail
  let rank = 1;

  console.log("sorted", sorted.slice(0, 100));
  for (const scoreData of sorted.slice(0, 100)) {
    const ranking = await rankingService.create({
      period,
      period_start: start,
      metric: "total",
      user_id: scoreData.user_id,
      score: scoreData.total_score | 0,
      rank,
    });

    // Insert ranking details per action_type
    const details = Object.entries(scoreData.action_scores).map(
      ([action_type, score]) => ({
        ranking_id: ranking.id,
        action_type,
        score,
        created_at: new Date(),
        updated_at: new Date(),
      })
    );
    await rankingDetailService.model.bulkCreate(details);

    rank++;
  }

  console.log(
    `[RankingJob] ${period} ranking calculated at ${new Date().toISOString()}`
  );
};

export { initialize, calculateRanking };
