import cron from "node-cron";
import { calculateRanking, initialize } from "../rankingJob";

export const initRankingCron = () => {
  cron.schedule("0 1 * * *", async () => {
    try {
      console.log("[CRON] Updating weekly/monthly ranking...");
      await initialize();
      await calculateRanking("weekly");
      await calculateRanking("monthly");
    } catch (error) {
      console.error("[CRON ERROR]", error);
    }
  });
};
