import { User, Level } from '../models';
import { Op } from 'sequelize';

const BATCH_SIZE = 100;

export const runLevelUpJob = async () => {
  const levels = await Level.findAll({ order: [['exp_min', 'ASC']] });

  let offset = 0;
  let hasMore = true;

  while (hasMore) {
    const users = await User.findAll({
      limit: BATCH_SIZE,
      offset,
      order: [['id', 'ASC']],
    });

    if (users.length === 0) {
      hasMore = false;
      break;
    }

    for (const user of users) {
      const userExp = user.exp || 0;

      const matchedLevel: any = levels.find(
        (lvl: any) => userExp >= lvl.exp_min && userExp <= lvl.exp_max
      );

      if (matchedLevel && user.level_id !== matchedLevel.id) {
        await user.update({ level_id: matchedLevel.id });
        console.log(`✅ Updated user ${user.id} to level ${matchedLevel.name}`);
      }
    }

    offset += BATCH_SIZE;
  }

  console.log('🎉 Level-up job finished.');
};
